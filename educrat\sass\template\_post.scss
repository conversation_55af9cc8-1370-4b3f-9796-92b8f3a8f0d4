/*
* General Post Style using for all with naming class entry
*/
.post.no-results{
	text-align: center;
	.widget_search{
		margin: 25px auto 0;
		max-width: 600px;
	}
	.title-no-results{
		color: $body-color;
		margin: 0 0 10px;
		color: $body-link;
		font-size: 25px;
		@media(min-width: 1200px){
			font-size: 30px;
		}
	}
	margin: 0 0 30px;
	@media(min-width: 1200px){
		margin: 0 0 50px;
	}
}
.entry-title{
    margin: 0;
    word-wrap: break-word;
    word-break: break-word;
    font-size: 18px;
    font-weight: 500;
	@media(min-width: 1200px){
		font-size: 20px;
	}
}
.detail-title{
	margin: 10px 0;
	font-size: 28px;
	text-transform: capitalize;
	@media(min-width: 1200px){
		font-size: 40px;
	}
}
.entry-create{
	margin: 0 0 15px;
	> *{
		margin-right: 2px;
	}
	.author{
		font-style: italic;
		text-transform: capitalize;
	}
}
.comment-form-cookies-consent{
	[type="checkbox"]{
		margin-right: 7px;
	}
}
.entry-link{
	margin-top: 20px;
	.readmore {
		color: $theme-color;
		text-transform: capitalize;
		font-weight: 500;
		 font-size: $font-size-base - 2;
		&:hover{
			color: #000;
		}
	}
}
.entry-meta{
	margin:0;
	.fa,.icon{
		margin-right: 3px;
	}
}
.wp-block-quote,
blockquote{
	margin: 0.9375rem 0;
	position: relative;
	@include border-radius(0);
	font-weight: 400;
	background: #fff;
	font-size: 1.0625rem;
	font-style: italic;
	padding:15px 15px 15px 50px;
	border: 0;
	border-left: 5px solid $theme-color;
	color: $body-link;
	@media(min-width: 1200px){
		padding:28px 28px 28px 80px;
		margin: 1.875rem 0;
	}
	cite{
		font-size: 18px;
		color: $body-link;
		display: inline-block;
		font-weight: 500;
		font-family: $headings-font-family;
		font-style: normal;
	}
	p{
		margin-bottom: 12px;
		&:last-child{
			margin-bottom: 0;
		}
	}
	&:before{
		color: darken(#EEF2F6,2%);
		font-size: 60px;
		line-height: 1;
		position: absolute;
		top: 15px;
		left: 5px;
		@media(min-width: 1200px){
			left: 10px;
			font-size: 90px;
		}
	}
}
.entry-vote{
	z-index: 1;
	display: table;
	text-align: center;
	top: 20px;
	position: absolute;
	background: rgba($black,.5);
	@include size(44px,44px);
	right: 20px;
	.entry-vote-inner{
		color: $white;
		display: table-cell;
		vertical-align: middle;
		font-weight: $headings-font-weight;
	}
	&.vote-perfect{
		.entry-vote-inner{
			color: $red;
		}
	}
	&.vote-good{
		.entry-vote-inner{
			color: $yellow;
		}
	}
	&.vote-average{
		.entry-vote-inner{
			color: #91e536;
		}
	}
	&.vote-bad{
		.entry-vote-inner{
			color: $orange;
		}
	}
	&.vote-poor{
		.entry-vote-inner{
			color: $green;
		}
	}
}
.type-post{
	margin-bottom:$theme-margin;
}
.blog-title{
	margin-bottom: $theme-margin;
}
.layout-posts-list{
	> .post {
		margin-bottom: 15px;
		@media(min-width: 992px){
			margin-bottom: $theme-margin;
		}
	}
}
.categories-list{
	list-style: none;
	padding: 0;
	margin: 0 0 $theme-margin;
	@include flexbox();
	align-items: center;
	overflow: auto;
	@media(min-width: 576px){
		justify-content: center;
		margin-bottom: 50px;
	}
	li{
		margin: 0 2px;
		a{
			display: inline-block;
			white-space: nowrap;
			font-weight: 500;
			padding: 6px 18px;
			color: $body-color;
			@include border-radius($border-radius);
			@include transition(all 0.3s ease-in-out 0s);
			&:hover,
			&:focus,
			&.active{
				color: $theme-color;
				background: var(--educrat-theme-color-007);
			}
		}
	}
}
//commentform
.comment-form-theme{
	margin-bottom:10px;
	label{
		font-weight: 500;
		color: $body-link;
		margin:0 0 5px;
	}
	.comment-form-cookies-consent{
		display: none;
		font-size: 14px;
		label{
			margin-bottom: 0;
			display: inline;
		}
	}
	.form-group{
		margin-bottom: 1.25rem;
		@media(min-width: 1200px){
			margin-bottom: 25px;
		}
		position: relative;
	}
	.yourview{
		margin-bottom: 5px;
	}
	.form-control{
	    margin: 0;
	    border-width: 2px;
	    @media(min-width: 1200px){
	    	height: 55px;
	    }
	}
	textarea.form-control{
		height: 150px;
		@media(min-width: 1200px){
			height: 220px;
		}
		resize: none;
	}
	.form-submit{
		margin-bottom: 0;
	}
	#cancel-comment-reply-link{
		color: $danger;
	}
	.group-upload{
		[class="hidden"]{
			display: none;
		}
		button{
			border: 1px solid $theme-color;
			padding: 0.5rem 1rem;
			@media(min-width: 1200px){
				padding: 1rem 2rem;
				min-width: 300px;
				text-align: center;
			}
			background-color: #fff;
			color: $theme-color;
			@include border-radius($border-radius);
			@include transition(all 0.3s ease-in-out 0s);
			&:hover,&:focus{
				background-color: $theme-color;
				color: #fff;
				border-color: $theme-color;
			}
			.upload-file-btn{
				margin-top: 5px;
			}
			i{
				margin-right: 5px;
			}
		}
	}
}
/* Post type: List widget list*/
.posts-list{
	list-style: none;
	padding:0;
	margin:0;
	> li{
        margin: 0 0 20px;
        &:last-child{
            margin:0;
        }
    }
    .entry-title{
    	line-height: 22px;
    	font-weight: 500;
    	font-size: $font-size-base;
    	margin: 0;
    	display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .image{
    	width: 80px;
    	padding-right: 15px;
    	+ .inner{
    		flex:1;
    		-webkit-box-flex:1;
    		-ms-flex:1;
    	}
    	.image-inner{
    		max-height: 65px;
    		overflow: hidden;
    		@include border-radius($border-radius);
    	}
    }
    .date{
    	font-size: 13px;
    }
}
// post-grid
.post-layout{
	@include transition(all 0.3s ease-in-out 0s);
	overflow: hidden;
	.post-sticky{
		background:$danger;
		color:#fff;
		display:inline-block;
		padding:0 15px;
		margin:5px 0;
		font-size:14px;
	}
	.categories-name{
		font-size: 0.875rem;
		font-weight: 500;
		color: $theme-color;
		text-transform: uppercase;
	}
	.entry-title{
		margin-top: 5px;
		.stick-icon{
			display: inline-block;
			line-height: 1;
			margin-right: 5px;
		}
	}
	.top-image{
		overflow: hidden;
		position:relative;
		@include border-radius($border-radius);
		margin-bottom: 15px;
		img{
			@include transition(all 0.3s ease-in-out 0s);
		}
		.entry-thumb{
			margin: 0;
		}
	}
	iframe{
		max-width: 100%;
	}
	.date{
		margin-top: 10px;
	}
	&:hover{
		.top-image{
			img{
				@include scale(1.05);
			}
		}
	}
	&.sticky{
		.entry-title a{
			color: $theme-color;
			text-decoration: underline;
		}
	}
}
.post-grid{
	.entry-title{
		display: -webkit-box;
	    -webkit-line-clamp: 2;
	    -webkit-box-orient: vertical;
	    overflow: hidden;
	}
	&.v2{
		.entry-title{
			font-size: 18px;
		}
	}
}

// post list
.blog-only-main.style-list{
	max-width: 1070px;
	margin-left: auto;
	margin-right: auto;
}
.has-sidebar{
	.post-list-item{
		.top-image{
			@media(min-width: 1200px){
				width: 400px;
				+ .col-content{
					padding-left: 50px;
				}
			}
		}
		.btn-readmore{
			margin-top: 20px;
		}
	}
}
.post-list-item{
	margin-bottom: 40px;
	@media(min-width: 1200px){
		margin-bottom: 50px;
	}
	.top-image{
		width: 100%;
		@media(min-width: 576px){
			width: 300px;
			margin: 0;
		}	
		@media(min-width: 1200px){
			width: 520px;
		}
		+ .col-content{
			@media(min-width: 576px){
				padding-left: 30px;
			}
			@media(min-width: 1200px){
				padding-left: 90px;
			}
		}
	}
	.date{
		margin: 0 0 0 12px;
	}
	.entry-title{
		margin-top: 10px;
		font-size: 20px;
		@media(min-width: 1200px){
			font-size: 24px;
			margin-top: 15px;
		}
	}
	.description{
		margin-top: 15px;
		@media(min-width: 1200px){
			margin-top: 20px;
		}
	}
	.btn-readmore{
		margin-top: 20px;
		@media(min-width: 1200px){
			margin-top: 25px;
		}
	}
	&:hover{
		.btn.btn-readmore{
			background: $theme-color;
			color: #fff;
		}
	}
}
.post-list-item-small{
	margin-bottom: $theme-margin / 2;
	@media(min-width: 768px){
		margin-bottom: $theme-margin;
	}
	&:last-child{
		margin-bottom: 0;
	}
	.top-image{
		width: 135px;
		margin: 0;
	}
	.col-content{
		padding-left: 18px;
	}
	.entry-title{
		display: -webkit-box;
	    -webkit-line-clamp: 2;
	    -webkit-box-orient: vertical;
	    overflow: hidden;
	    font-size: 17px;
	}
	.categories-name,
	.date{
		font-size: 13px;
	}
}
/* Post type: By Category */
.top-blog-info{
	padding:25px 0;
	margin-bottom:20px;
	border-bottom:1px solid $border-color;
	i{
		margin-right: 10px;
	}
	.categories{
		margin-right: 35px;
	}
	.author{
		a{
			color: $theme-color;
		}
	}
	a{
		color: $body-color;
		&:hover,&:active{
			color:$theme-color;
		}
	}
}
.category-posts{
	position: relative;
	&::after{
		content: "";
		top: 20px;
		position: absolute;
		right: 0;
		@include size(1px,1000px);
		background: $border-color;
	}
	.post{
		border-bottom: 1px solid $border-color;
	}
	.category-posts-label{
		padding: 1px 3px;
		@include border-radius(0);
		background: $theme-color;
		font-weight: $category-posts-label-font-weight;
		text-transform: $category-posts-label-transform;
		a{
			color: $category-posts-label-color;
		}
	}
	.entry-meta{
		&::after{
			display: none;
		}
	}
	.posts-more{
		.post{
			&:last-child{
				border: 0px;
			}
		}
		.entry-title{
			a{
				color: $gray-100;
				&:hover{
					color: $theme-color;
				}
			}
		}
	}
}
/*------------------------------------*\
    Comment List
\*------------------------------------*/
.comment-list{
	padding:0;
	margin: 0;
	list-style: none;
	.comment-respond{
		margin-bottom: 20px;
		@media(min-width: 1200px){
			margin-bottom: 40px;
		}
		small{
			margin-left: 5px;
			font-size: 14px;
			font-weight: 400;
		}
		#submit{
			min-width: auto !important;
		}
		textarea#comment{
			height: 120px;
		}
		.btn{
			padding: 0.6rem 1.875rem;
			font-size: 0.9375rem;
		}
	}
	#cancel-comment-reply-link{
		color:$danger;
	}
	.comment-author{
		font-size: 0.875rem;
		i{
			margin-right: 5px;
		}
		> *{
			margin-right: 15px;
			&:last-child{
				margin-right: 0;
			}
		}
		+ .comment-respond{
			margin: 15px 0 0;
		}
	}
	.name-comment{
		font-weight: 500;
		margin: 0;
		text-transform: capitalize;
		font-size: 1rem;
		@media(min-width: 1200px){
			font-size: 17px;
		}
	}
	.review-stars-rated-wrapper{
		margin-top: 5px;
	}
	.date{
		font-size: 13px;
		margin-left: 5px;
		white-space: nowrap;
	}
	.inner-left{
		padding-right: 20px;
	}
	.children{
		list-style: none;
		margin: 0;
		padding: 0;
		padding-left: 15px;
		@media(min-width: 1200px){
			padding-left: 40px;
		}
	}
	.comment-edit-link{
		color: $danger;
		font-weight: 500;
	}
	.comment-reply-link{
		font-weight: 500;
		color:#02ccad;
		white-space: nowrap;
	}
	.comment-text{
		margin-top: 12px;
		p:last-child{
			margin:0;
		}
	}
	div.avatar{
		@include size(50px,50px);
		@media(min-width: 1200px){
			@include size(60px,60px);
		}
		overflow: hidden;
		@include border-radius(50%);
		float: left;
		img{
			margin:0;
		}
		+ .comment-box{
			overflow:hidden;
			padding-left: 15px;
			@media(min-width: 1200px){
				padding-left: 20px;
			}
		}
	}
	.the-comment{
		margin: 0 0 15px;
		padding: 0 0 15px;
		border-bottom: 1px solid $border-color;
		@media(min-width:1200px){
			margin: 0 0 30px;
			padding: 0 0 30px;
		}
	}
	> li:last-child{
		> .the-comment:last-child{
			border-bottom: 0;
			margin-bottom: 10px;
		}
	}
}
.logged-in-as{
	a + a{
		color: $danger;
	}
}
/*------------------------------------*\
    Single post
\*------------------------------------*/
.social-networks{
	li{
		padding-left: 10px;
		padding-right: 10px;
		&:last-child{
			a{
				margin-right: 0;
			}
		}
		a{
			font-size: 14px;
			&:hover{
				color: $theme-color;
			}
		}
	}
}
//post-navigation
.post-navigation{
	position: relative;
	@media(min-width: 1200px){
		padding:5px 0;
	}
	.screen-reader-text{
		display: none;
	}
	.nav-links{
		@include flexbox();
	    margin-left: -(15px);
	    margin-right: -(15px);
	    position: relative;
	    &:before{

	    }
		> *{
			width: 40%;
			float: left;
			padding-left: 15px;
			padding-right: 15px;
			i{
				font-weight: 400;
				font-size: 0.875rem;
			}
			&.nav-next{
				margin-left: auto;
				float: right;
				text-align: right;
				i{
					
				}
				.title-direct{
					float: right;
				}
			}
			> a{
				&:hover{
					.navi{
						color: $theme-color;
					}
				}
			}
		}
		.title-direct{
			font-size: 0.875rem;
			color: $body-color;
		  	display: -webkit-box;
		  	-webkit-line-clamp: 2;
		  	-webkit-box-orient: vertical;  
		  	overflow: hidden;
		}
		.navi{
			@include transition(all 0.3s ease-in-out 0s);
			font-weight: 500;
			font-size: 17px;
			text-transform: capitalize;
		}
	}
	.inner-right{
		padding-right: 35px;
		position: relative;
		i{
			position: absolute;
			top: 4px;
			right: 0;
		}
	}
	.inner-left{
		padding-left: 35px;
		position: relative;
		i{
			position: absolute;
			top: 4px;
			left: 0;
		}
	}
}
.author-info{
	.author-title{
		font-size: 17px;
		font-weight:500;
		margin: 0 0 10px;
		text-transform: capitalize;
		@media(min-width: 1200px){
			margin-bottom: 20;
		}
	}
	.avatar-img{
		@include size(70px,70px);
		overflow: hidden;
		@include border-radius(50%);
	}
	.description{
		padding-left: $theme-margin / 2;
		@media(min-width: 1200px){
			padding-left: $theme-margin;
		}
		flex:1;
		-webkit-box-flex:1;
		-ms-flex:1;
	}
}
//related-posts
.wrapper-posts-related{
	padding: $theme-margin 0;
	background: #F7F8FB;
	@media(min-width: 1200px){
		padding:90px 0;
	}
	.type-post{
		margin-bottom: 0;
	}
}
.related-posts{
	.title{
		font-size: 25px;
		margin:0 0 $theme-margin;
		text-align: center;
		@media(min-width:1200px){
			margin-bottom: 50px;
			font-size: 30px;
		}
	}
}

// post gallery
.gallery{
	margin-left: -(15px);
	margin-right: -(15px);
	overflow: hidden;
	.gallery-item{
		float: left;
		margin-bottom:(15px);
		padding-right: (15px);
		padding-left: (15px);
		position: relative;
		figcaption{
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			color: #fff;
			max-height: 50%;
			font-size: 12px;
			background: rgba(0,0,0,0.5);
			margin-left: (15px);
			margin-right: (15px);
			@include opacity(0);
			padding:8px (15px);
		}
		&:hover{
			figcaption{
				@include opacity(1);
			}
		}
	}
	&.gallery-columns-9{
		.gallery-item{
			width: 11%;
		}
	}
	&.gallery-columns-8{
		.gallery-item{
			width: 12.5%;
		}
	}
	&.gallery-columns-7{
		.gallery-item{
			width: 14%;
		}
	}
	&.gallery-columns-6{
		.gallery-item{
			width: 16.5%;
		}
	}
	&.gallery-columns-5{
		.gallery-item{
			width: 20%;
		}
	}
	&.gallery-columns-4{
		.gallery-item{
			width: 25%;
		}
	}
	&.gallery-columns-3{
		.gallery-item{
			width: 33%;
		}
	}
	&.gallery-columns-1{
		.gallery-item{
			width: 100%;
		}
	}
	&.gallery-columns-2{
		.gallery-item{
			width: 50%;
		}
	}
}
// navigation
.comment-navigation{
  overflow: hidden;
  padding: 20px 0;
  .nav-links{
    > div{
      display: inline-block;
      + div{
        line-height: 1.1;
        margin-left: 15px;
        padding-left: 15px;
        border-left: 2px solid $border-color;
      }
    }
  }
}
.top-detail-info{
	margin: 0 0 $theme-margin;
	text-align: center;
	@media(min-width: 1200px){
		margin-bottom: 80px;
	}
	.list-categories{
		font-size: 14px;
		font-weight: 500;
		text-transform: uppercase;
		a{
			color: $theme-color;
		}
	}
	.date{
		font-size: 17px;
	}
}
.main-content-detail {
	.single-info .entry-thumb{
		margin-bottom: $theme-margin;
		overflow: hidden;
		@include border-radius($border-radius);
		@media(min-width: 1200px){
			margin-bottom: 50px;
		}
	}
	// check main
	&.only-main{
		.inner-detail{
			max-width: 870px;
			margin-left: auto;
			margin-right: auto;
		}
	}
}
.detail-post{
	iframe{
		max-width: 100%;
	}
	.tag-social{
		width: 100%;
		margin-top: 20px;
		@media(min-width: 1200px){
			margin-top: $theme-margin;
		}
	}
	.entry-tags-list{
		display: block;
		margin: 8px 0 0;
		position: relative;
	}
	#comments,
	.post-navigation,
	.author-info{
		padding-top: 20px;
		margin-top: 20px;
		border-top: 1px solid $border-color;
		@media(min-width: 1200px){
			padding-top: $theme-margin;
			margin-top: $theme-margin;
		}
	}
}
.author-post{
	.avatar {
		@include border-radius(50%);
	}
	.avatar-img{
		padding-right: 8px;
		float: left;
	}
	.name-author{
		display:inline-block;
		margin-top:9px;
	}
}
.author-wrapper{
	.avatar-img{
		overflow: hidden;
		@include border-radius(50%);
		@include size(40px,40px);
		@include flexbox();
		align-items:center;
		-webkit-align-items:center;
		-ms-align-items:center;
		justify-content:center;
		-webkit-justify-content:center;
		-ms-justify-content:center;
		img{
			margin:0 !important;
		}
	}
	.author-title{
		font-size: $font-size-base;
		font-weight: 400;
		margin:0;
		text-transform: capitalize;
		a{
			color: #777777;
			&:hover,&:focus{
				color: $body-link;
			}
		}
	}
	.right-inner{
		padding-left: 10px;
	}
}
#respond .comment-reply-title{
	font-weight: 500;
	font-size: 18px;
	margin: 0 0 10px;
	text-transform: capitalize;
	@media(min-width: 1200px){
		margin-bottom: 15px;
		font-size: 20px;
	}
	#cancel-comment-reply-link{
	    color:$danger;
	}
}
.comments-title{
	font-weight: 500;
	font-size: 18px;
	margin: 0 0 20px;
	text-transform: capitalize;
	@media(min-width: 1200px){
		margin-bottom: 30px;
		font-size: 20px;
	}
}