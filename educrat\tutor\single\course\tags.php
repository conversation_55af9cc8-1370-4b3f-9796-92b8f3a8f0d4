<?php
/**
 * Template for displaying course tags
 *
 * @since v.1.0.0
 *
 * <AUTHOR>
 * @url https://themeum.com
 *
 * @package TutorLMS/Templates
 * @version 1.4.3
 */

do_action( 'tutor_course/single/before/tags' );

$course_tags = get_tutor_course_tags();
if ( is_array( $course_tags ) && count( $course_tags ) ) { ?>
	<div class="box-info-white">
		<h3 class="title">
			<?php esc_html_e( 'Tags', 'educrat' ); ?>
		</h3>
		<div class="tutor-course-details-widget-tags">
		  <ul class="tutor-tag-list">
				<?php
				foreach ( $course_tags as $course_tag ) {
					$tag_link = get_term_link( $course_tag->term_id );
					echo "<li><a href=' " . esc_url( $tag_link ) . " '> " . esc_html( $course_tag->name ) . ' </a></li>';
				}
				?>
		  </ul>
		</div>
	</div>
	<?php
}

do_action( 'tutor_course/single/after/tags' ); ?>
