/*
*  Responsive
*/
@media(min-width: 1200px){
    .hidden-dots{
        .slick-dots{
            display: none;
        }
    }
    .btn{
        font-size: 1rem;
        padding: 14px 50px;
    }
    .btn-sm{
        padding: 0.5rem 1rem;
        font-size: 0.82031rem;
        border-radius: 4px;
    }
    .btn-lg{
        font-size: 17px;
        padding: 15px 50px;
    }
}
@media(min-width: 1350px){
    .container{
        max-width: 1320px;
    }
    .row-margin-left{
        > .elementor-container{
            overflow: hidden;
            width: calc( 1320px + ((100vw - 1320px) / 2) );
            max-width: calc( 1320px + ((100vw - 1320px) / 2) ) !important;
            left: calc( (100vw - 1320px) / 4 );
            padding-right: calc( (100vw - 1290px) / 2);
        }
    }
}
@media (min-width: 1350px) and (max-width: 1600px){
    .elementor-section.elementor-section-boxed:not(.row-margin-left) > .elementor-container{
      max-width: 1320px !important;
    }
}
@media (max-width: 1199px){
    .course-header .course-category-item{
        margin-bottom: 7px;
    }
    .course-header.v3 .apus-breadscrumb{
        margin: 0;
    }
}
@media (max-width: 991px){
    .inner-v6 .course-header-right{
        margin-top: 20px;
    }
    .widget-courses-tabs .top-info .ms-auto{
        margin: 15px 0 0;
        display: inline-block;
        .tabs-course {
            justify-content: start !important;
        }
    }
}
@media (max-width:767px) {
    .row{
        margin-left: -0.5rem;
        margin-right: -0.5rem;
        > [class *="col-"]:not(.elementor-column){
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
    }
    .nav-tabs{
        overflow-x: auto;
        white-space: nowrap;
        li{
            float: none;
            display: inline-block;
            margin-bottom: 8px !important;
        }
    }
    .apus-filter {
        > *{
            display: block;
            width: 100%;
            float: none !important;
            + *{
                margin-top: 15px;
            }
        }
    }
    .error-404 .top-image{
        margin-top: $theme-margin;
        img{
            max-width: 80%;
        }
    }
    .page-404 .not-found{
        text-align: center;
    }
    .details-product{
        .information{
            margin-top: 20px;
        }
    }
    .course-top-wrapper .course-found{
        margin-bottom: 15px;
    }
    .tabs-course.st_gray{
        @include border-radius($border-radius);
    }
    .course-list{
        margin-bottom: $theme-margin / 2;
    }
}
@media(max-width: 990px){
    .lp-user-profile #profile-nav{
      margin: 15px 0 0;
      border: 0;
    }
}
@media(max-width: 575px){
    .tutor-course-detail-author .course-author-infomation,
    .lp-course-detail-author .course-author-infomation{
        padding: 20px 0 10px;
        text-align: center;
    }
    .tutor-course-detail-author .author-image,
    .lp-course-detail-author .author-image{
        margin: auto;
    }
    .search-form-course .btn-search{
        width: 100%;
    }
    .widget-courses-related{
        .course-layout-item{
            margin-bottom: 0;
        }
    }
}
@media(max-width: 560px){
    #learn-press-profile #profile-nav .lp-profile-nav-tabs li{
      margin: 0 2px 5px;
    }
}
@media(max-width: 479px){
    .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > *,
    .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container, .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select, .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input{
        width: 100% !important;
        margin: 0 0 5px !important;
    }
    .woocommerce-page table.cart td.actions .coupon{
        margin: 0;
        .input-text{
            padding: 5px 10px !important;
            margin: 0;
        }
    }
    .actions .update_cart{
        width: 100%;
        margin-top: 10px !important;
    }
    .elementor-icon-list-items.elementor-inline-items .elementor-icon-list-item{
        margin-bottom: 10px;
    }
    .lp-user-profile .lp-profile-left{
        width: 100%;
        max-width: 100%;
    }
}
// fix admin
.admin-bar{
    @media(max-width: 782px){
        &.header_transparent{
            #apus-header{
                top: 46px;
            }
        }
        .header-mobile{
            top: 46px;
            &.sticky-header{

            }
        }
    }
}
@media(min-width: 783px){
    .admin-bar{
        .header-mobile{
            top: 32px;
        }
    }
}
@media(max-width: 600px){
    .admin-bar{
        .header-mobile.sticky-header{
            top: 0;
        }
    }
}