// widget vertical menu
.vertical-wrapper{
  position:relative;
  .content-vertical{
    padding:10px 0 0;
    display: none;
    position:absolute;
    top:100%;
    left: 0;
    width: 100%;
    z-index: 3;
    margin-top: 15px !important;
    min-width: 300px;
  }
  &.show-always{
    .content-vertical{
      display: block;
    }
  }
  .title{
    margin-left: 12px;
  }
}
.apus-vertical-menu{
  padding: 20px 30px;
  background: #fff;
  margin:0;
  list-style:none;
  border: 1px solid $border-color;
  @include border-radius($border-radius);
  @include box-shadow(0 25px 70px 0 rgba(#01213A,0.07));
  position: relative;
  line-height: 1;
  &:before{
    content: '';
    @include size(10px,10px);
    background: #fff;
    @include rotate(45deg);
    position: absolute;
    left: 35px;
    top: -5px;
  }
  > li{
    display: block;
    width: 100%;
    position: static;
    > a{
      display: inline-block;
      width: 100%;
      font-size: $font-size-base;
      padding: 10px 0;
      background:transparent !important;
      > i,
      > img{
        font-size: 22px;
        margin-right: 15px;
        width: 18px;
        display: inline-block;
        color: $theme-color;
      }
      .caret{
        display: none;
      }
    }
    .dropdown-toggle{
      &:after{
        border:0;
        content: "\e649";
        font-family: 'themify';
        font-size: 12px;
        float: right;
        margin-top: 1px;
      }
    }
    .apus-container{
      padding: 10px 35px;
    }
    &:hover,
    &.active{
      > a{
        color: $theme-color;
        text-decoration: underline;
      }
    }
  }
  li:hover{
    > .dropdown-menu{
      @include opacity(1);
      visibility: visible;
    }
  }
  .text-label{
    font-size: 12px;
    vertical-align: super;
    margin-left: 5px;
    color: $theme-color;
    font-family: $headings-font-family;
    &.label-hot{
      color: $danger;
    }
    &.label-new{
      color: $success;
    }     
  }
  .dropdown-menu{
    min-width: 240px;
    height: calc(100% + 2px);
    visibility: hidden;
    padding: 20px 30px;;
    @include border-radius($border-radius);
    display: block;
    @include opacity(0);
    @include transition(all 0.2s ease-in-out 0s);
    border: 1px solid $border-color;
    @include box-shadow(0 25px 70px 0 rgba(#01213A,0.07));
    > li{
      > a{
        background:transparent !important;
        padding: 10px 0;
        display: inline-block;
        &:hover,&:focus{
          text-decoration: underline;
        }
        > i,
        > img{
          font-size: 20px;
          margin-right: 10px;
          width: 15px;
          display: inline-block;
        }
      }
    }
    .widget{
      margin:0;
    }
    .widget .widget-title, .widget .widgettitle, .widget .widget-heading{
      margin: 0 0 10px;
      font-size: 18px;
    }
    .widget-nav-menu .menu li{
      margin: 0;
      a{
        display: inline-block;
        width: 100%;
        padding: 10px 0;
        color: $body-link;
        &:hover,&:focus{
          color: $theme-color;
          text-decoration: underline;
        }
      }
      &.active > a{
        color: $theme-color;
        text-decoration: underline;
      }
    }
  }
  .aligned-left{
    > .dropdown-menu{
      top: -1px;
      left: calc(100% - 15px);
    }
  }
  .aligned-right{
    > .dropdown-menu{
      top: -1px;
      right: calc(100% - 15px);
      left: inherit;
    }
  }
}

// version 2
.apus-vertical-menu-layout1{
  display: block;
  .sub-menu{
    width: 100% !important;
    display: none;
    list-style: none;
    margin:0;
    padding: 7px 0 0 $theme-margin !important;
    a{
      position: relative;
      &:before{
        top: 50%;
        @include translateY(-50%);
        left: 0;
        content:'';
        position: absolute;
        @include size(5px,5px);
        @include border-radius(50%);
        background: #DEDCDC;
        @include transition(all 0.3s ease-in-out 0s);
      }
    }
  }
  li{
    position: relative;
    .icon-toggle{
      font-size: 12px;
      position: absolute;
      top: 5px;
      right: 20px;
      z-index: 1;
      padding: 8px 0 8px 8px;
      cursor: pointer;
      color: $body-link;
    }
    a{
      display: inline-block;
      width: 100%;
      padding: 2px 20px;
      color: $body-color;
      &:hover,&:focus{
        color: $theme-color;
        &:before{
          background: $theme-color;
        }
      }
    }
    &:hover,
    &.active{
      > a{
        color: $theme-color;
        &:before{
          background: $theme-color;
        }
      }
    }
  }
  > li{
    > a{
      font-size: 1rem;
      font-weight: 500;
      padding: 8px 20px;
      @include border-radius(50px);
      margin-bottom: 3px;
    }
    &:hover,
    &.active{
      > a{
        color: $theme-color;
        background: var(--educrat-theme-color-007);
      }
    }
  }
  .elementor-container{
    display: block;
    .elementor-element{
      width: 100%;
    }
  }
  .widget-nav-menu .menu li{
    margin: 0;
    a{
      text-decoration: none !important;
    }
  }
}

// apus_custom_menu
.apus_custom_menu{
  &.center{
    text-align: center;
    li{
      display:inline-block;
      margin:0 15px;
    }
  }
  &.left{
    text-align: left;
  }
  &.right{
    text-align: right;
  }
  &.inline{
    li{
      display:inline-block;
      vertical-align:middle;
      margin-bottom:0;
      margin-right: 20px;
      @media(min-width:1200px){
        margin-right: 40px;
      }
      &:last-child{
        margin:0;
      }
    }
  }
}
// slick
.slick-carousel{
  position:relative;
  margin-right: -8px;
  margin-left: -8px;
  @media(min-width: 1200px){
    margin-right: -15px;
    margin-left: -15px;
  }
  .slick-arrow{
    background: #fff;
    color: #1A064F;
    padding: 0;
    border: 2px solid #1A064F;
    display: inline-block;
    font-size: 18px;
    @include size(30px,30px);
    line-height: 26px;
    @media(min-width: 1200px){
      @include size(50px,50px);
      line-height: 46px;
    }
    @include transition(all 0.3s ease-in-out 0s);
    position:absolute;
    top: 50%;
    @include translate(0,-50%);
    z-index: 2;
    @include border-radius(50%);
    .textnav{
      display: none;
    }
    &:hover,&:focus{
      color: #fff;
      background: #1A064F;
      border-color: #1A064F;
    }
  }
  .slick-prev{
    left: 0;
    @media(min-width: 1200px){
      left: -10px;
    }
  }
  .slick-next{
    right: 0;
    @media(min-width: 1200px){
      right: -10px;
    }
  }
  .slick-slide{
    outline: none !important;
    padding-left:8px;
    padding-right:8px;
    @media(min-width: 1200px){
      padding-left:15px;
      padding-right:15px;
    }
  }
  &.no-gap{
    margin:0;
    .slick-slide{
      padding-left: 0;
      padding-right: 0;
    }
  }
  &.gap-10{
    margin-left: -5px;
    margin-right: -5px;
    .slick-slide{
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  &.gap-2{
    margin-left: -1px;
    margin-right: -1px;
    .slick-slide{
      padding-left: 1px;
      padding-right: 1px;
    }
  }
  &.show-text{
    .textnav{
      display: inline-block;
      margin: 0 2px;
    }
    .slick-arrow{
      @include size(auto,auto);
      background: transparent !important;
      font-weight:500;
      font-size: 12px;
      color: $body-link;
      &:hover,&:active,&:focus{
        color:$theme-color;
      }
    }
    .slick-prev{
      left:0;
      right: inherit;
    }
    .slick-next{
      right:0;
      left: inherit;
    }
  }
  .slick-track{
    margin: inherit;
  }
  // dots
  .slick-dots{
    margin:0 !important;
    padding:1rem 0 0;
    @media(min-width: 1200px){
      padding:1.875rem 0 0;
    }
    text-align: center;
    list-style: none;
    line-height: 1;
    li{
      @include transition(all 0.3s ease-in-out 0s);
      position:relative;
      display: inline-block;
      cursor: pointer;
      margin:0 5px;
      border:0;
      padding:0;
      background:transparent;
      button {
        border:none;
        display: block;
        text-indent: -9999em;
        @include size(8px,8px);
        padding:0;
        background: #D4D4D4;
        @include border-radius(50%);
        @include transition(all 0.2s ease-in-out 0s);
      }
      &.slick-active{
        button{
          background: $body-link;
        }
      }
    }
  }
}
.slick-carousel.st_white{
  .slick-dots li{
    &.slick-active,
    button{
      background-color: #fff !important;
    }
  }
}

// widget-social
.widget-socials{
  .social{
    padding: 0;
    list-style: none;
    margin: 0;
    > li{
      padding:0;
      display: inline-block;
      margin-right: 5px;
      &:last-child{
        margin: 0;
      }
    }
    a{
      font-size: $font-size-base - 1;
      display: inline-block;
      @include size(30px,30px);
      line-height: 30px;
      @media(min-width: 1200px){
        @include size(40px,40px);
        line-height: 40px;
      }
      text-align: center;
      @include border-radius(50%);
      background-color: $theme-color;
      color: #fff;
      @include transition(all 0.3s ease-in-out 0s);
      &:hover,&:focus{
        color: #fff;
        background: $theme-hover-color;
      }
    }
  }
  &.st_normal{
    .social{
      > li{
        margin-right: 1rem;
        @media(min-width: 1200px){
          margin-right: 1.5625rem;
        }
        &:last-child{
          margin-right: 0;
        }
      }
      a{
        @include size(auto,auto);
        line-height: $line-height-base;
        background-color: transparent;
        color: $body-link;
      }
    }
  }
}
.list-icon{
  margin-bottom: 0.75rem;
  &:last-child{
    margin-bottom: 0;
  }
  .title{
    margin:0;
  }
  .box-content{
    padding-left: 10px;
  }
}
.vertical-icon{
  position: relative;
  display: block;
  @include size(25px,12px);
  @include transition(all 0.2s ease-in-out 0s);
  cursor: pointer;
  &:after,
  &:before{
    content:'';
    position: absolute;
    right: 0;
    bottom: 0;
    @include size(20px,2px);
    background-color: $body-link;
    @include transition(all 0.2s ease-in-out 0s);
  }
  &:after{
    @include size(25px,2px);
    top: 0;
    bottom: inherit;
  }
  &:focus,
  &:hover{
    &:before{
      width: 100%;
    }
  }
}
// navbar header
.navbar-wrapper{
    .close-navbar-sidebar{
      cursor: pointer;
      @include opacity(0.8);
      @include transition(all 0.2s ease-in-out 0s);
      color: $body-link;
      background-color: $theme-color !important;
      position: absolute;
      z-index: 1;
      text-align: center;
      @include border-radius(50%);
      top: 10px;
      right: 10px;
      @include size(30px,30px);
      line-height: 30px;
      @media(min-width: 1200px){
        font-size: 15px;
        top: 40px;
        right: 40px;
        @include size(42px,42px);
        line-height: 42px;
      }
      &:hover,&:focus{
        @include opacity(1);
      }
    }
    .navbar-sidebar-wrapper{
        z-index: 3;
        position:fixed;
        overflow-y:auto;
        scrollbar-width: thin;
        right:0;
        top:0;
        @include transition(all 0.35s ease-in-out 0s);
        @include opacity(0);
        width:450px;
        max-width: 80%;
        height: 100vh;
        @include translateX(100%);
        &.active{
          @include opacity(1);
          @include translateY(0);
        }
    }
    .navbar-sidebar-overlay{
      background:rgba(#0A2357,0.3);
      position:fixed;
      top:0;
      left:0;
      @include size(100vw,100vh);
      @include opacity(0);
      @include transition(all 0.3s ease-in-out 0s);
      cursor: no-drop;
      visibility: hidden;
      z-index: 2;
      &.active{
          visibility: visible;
          @include opacity(1);
      }
    }
    &.st_left{
        .navbar-sidebar-wrapper{
            @include translateX(-100%);
            right:initial;
            left: 0;
            &.active{
                @include translateY(0);
            }
        }
    }
}
// language
.language-wrapper{
  font-size:12px;
  display:inline-block;
  position:relative;
  &:before{
    content:'';
    position:absolute;
    top:100%;
    left:0;
    @include size(100%,5px);
  }
  .selected{
    .language-current{
      > img{
        margin:0 10px 0 0;
        vertical-align:sub;
      }
      > i{
        margin-left: 10px;
      }
    }
  }
  .dropdown-menu{
    background:#fff;
    display:block;
    @include opacity(0);
    @include translateY(10px);
    font-size:12px;
    margin-top:5px;
    @include border-radius(0);
    padding:13px 18px;
    min-width:130px;
    @include box-shadow(none);
    border:1px solid $border-color;
    @include transition(all 0.3s ease-in-out 0s);
    visibility:hidden;
  }
  &:hover{
    .dropdown-menu{
      visibility:visible;
      @include translateY(0);
      @include opacity(1);
    }
  }
  .list-language{
    list-style:none;
    padding:0;
    margin:0;
    li{
      margin-bottom:10px;
      &:last-child{
        margin-bottom:0;
      }
    }
    a{
      &:hover,&:focus{
        color: $theme-color;
      }
    }
    img{
      margin-right: 6px;
      margin-bottom:0;
    }
  }
}
//social-link
.social-link{
  display: inline-block;
  margin: 0 5px;
  padding:0;
  li{
    display: inline-block;
    margin: 0 5px;
    a{
      background: #f4f4f4 none repeat scroll 0 0;
      border-radius: 100%;
      color: $body-color;
      display: inline-block;
      height: 40px;
      line-height: 38px;
      text-align: center;
      width: 40px;
      border:1px solid $border-color;
    }
  }
  &.lighten{
    li a{
      background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
      border: 1px solid #ffffff;
      color: #ffffff;
    }
  }
}

// widget feature
.item-inner-features{
  text-align: center;
  position: relative;
  overflow: hidden;
  @include border-radius($border-radius);
  padding: 1rem;
  background-color: #fff;
  border: 1px solid transparent;
  @media(min-width: 1200px){
    padding: 50px 68px;
  }
  @include transition(all 0.3s ease-in-out 0s);
  .title{
    @include transition(all 0.3s ease-in-out 0s);
    font-size: 18px;
    @media(min-width: 1200px){
      font-size: 24px;
    }
    margin:0 0 12px;
  }
  .features-box-image{
    line-height: 1;
    font-size: 50px;
    margin-bottom: 10px;
    @media(min-width: 1200px){
      font-size: 80px;
      margin-bottom: 20px;
    }
    color: #00FF84;
    @include transition(all 0.3s ease-in-out 0s);
    img{
      display: block;
    }
  }
  &.style2{
    text-align: left;
    padding: 20px;
    @media(min-width: 1200px){
      padding: 40px;
    }
    background: #F7F8FB;
    @include border-radius(16px);
    .features-box-image {
      background: #fff;
      @include border-radius(50%);
      overflow: hidden;
      @include size(70px,70px);
      @include flexbox();
      align-items: center;
      justify-content: center;
      @media(min-width: 1200px){
        margin-bottom: 30px;
        font-size: 70px;
      }
    }
    .title{
      font-weight: 500;
      margin: 0 0 5px;
      font-size: 18px;
      @media(min-width: 1200px){
        font-size: 20px;
      }
    }
    &:hover{
      .title{
        color: $theme-color;
      }
    }
  }
}

// wiget testimonials
.widget-testimonials{
  .star{
    color: #E59819;
    .inner{
      width: 79px;
      position: relative;
      margin-left: 10px;
      font-size: 10px;
      color: #c1cde4;
      letter-spacing: 5px;
      &:before{
        content: "\f005\f005\f005\f005\f005";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
      }
    }
    .text{
      font-size: 14px;
      font-weight: 500;
    }
  }
  .w-percent{
    color: #E59819;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    &:before{
      content: "\f005\f005\f005\f005\f005";
      font-family: "Font Awesome 5 Free";
      font-weight: 900;
    }
  }
  &.fullscreen{
    @media(min-width: 1350px){
      .slick-list{
        overflow: visible;
      }
    }
  }
  &.style1{
    &.show_nav{
      padding-bottom: 60px;
      @media(min-width: 1200px){
        padding-bottom: 110px;
      }
    }
    .slick-carousel{
      .slick-arrow{
        top: 100%;
        @include translate(0, 0);
        margin-top: $theme-margin;
        @media(min-width: 1200px){
          margin-top: 60px;
        }
        @include opacity(1);
        border-color: #fff;
        background: transparent;
        color: #fff;
        &:hover,&:focus{
          color: $body-link;
          background: #fff;
          border-color: #fff;
        }
      }
      .slick-prev{
        right: 60px;
        @media(min-width: 1200px){
          right: 90px;
        }
        left: inherit;
      }
      .slick-next{
        right: 15px;
        left: inherit;
      }
      .slick-dots{
        text-align: inherit;
        padding-left: 10px;
      }
    }
  }
  &.style3{
    @media(min-width: 1350px){
      .slick-list{
        overflow: visible;
      }
    }
  }
  &.style4{
    .slick-carousel{
      margin: 0;
      .slick-slide{
        padding: 0;
        @include opacity(0.2);
        @include scale(0.8)
        @include transition(all 0.2s ease-in-out 0s);
        margin: 0 -30px;
      }
      .slick-current{
        position: relative;
        z-index: 1;
        @include opacity(1);
        @include scale(1)
      }
    }
  }
}
.testimonials-item{
  position: relative;
  padding: $theme-padding / 2;
  background: #fff;
  @include border-radius($border-radius-lg);
  border: 1px solid transparent;
  @media(min-width: 1200px){
    padding: $theme-padding $theme-padding 20px;
  }
  &:before{
    position: absolute;
    top: 10px;
    right: 15px;
    @media(min-width: 1200px){
      top: 20px;
      right: 25px;
    }
    z-index: 1;
    color: #E5F0FD;
    line-height: 1;
    font-size: 90px;
  }
  .avarta{
    overflow: hidden;
    @include transition(all 0.3s ease-in-out 0s);
    @include border-radius(50%);
    @include size(60px,60px);
    background-color: #fff;
    position: relative;
  }
  .name-client{
    font-weight: 500;
    font-size: 15px;
    margin: 0;
  }
  .description{
    margin-top: 0.9375rem;
    font-size: 15px;
    font-weight: 500;
    color: $body-link;
  }
  .job{
    color: $body-color;
    font-size: 13px;
  }
  .star{
    display: inline-flex;
    align-items: center;
    padding: 0 7px;
    background: #ff9800;
    color: #ffffff;
    font-size: 13px;
    @include border-radius(3px);
    margin-top: 3px;
    i{
      margin-right: 4px;
      font-size: 10px;
    }
  }
  .info-testimonials{
    flex-grow: 1;
    padding-left: 20px;
  }
  .title{
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 18px;
  }
  .inner-bottom{
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid $border-color;
  }
}
.testimonials-item2{
  .description{
    line-height: 1.5;
    font-size: 18px;
    font-weight: 500;
    color: $body-link;
    max-width: 610px;
    margin:0 auto $theme-margin;
    @media(min-width: 1200px){
      margin-bottom: 50px;
      font-size: 24px;
    }
  }
  .name-client{
    font-size: 17px;
    font-weight: 500;
    margin: 0 0 2px;
  }
}
.wrapper-testimonial-thumbnail{
  max-width: 500px;
  margin: 15px auto 0;
  @media(min-width: 1200px){
    margin-top: $theme-margin;
  }
  .slick-carousel{
    margin-left: -5px;
    margin-right: -5px;
    .slick-slide{
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  .avarta{
    cursor: pointer;
    max-width: 100%;
    @include size(92px,92px);
    overflow: hidden;
    border:2px solid transparent;
    @include border-radius(50%);
    padding: 8px;
    @include transition(all 0.2s ease-in-out 0s);
    &:hover,&:focus{
      border-color: $body-link;
    }
  }
  .slick-current{
    .avarta{
      border-color: $body-link;
    }
  }
}
.testimonials-item3{
  padding: 20px;
  @media(min-width: 1200px){
    padding: 60px;
    margin-bottom: $theme-margin;
  }
  background: #fff;
  @include border-radius($border-radius);
  .wrapper-avarta{
    @include size(100px,100px);
    @media(min-width: 1200px){
      @include size(170px,170px);
    }
    @include border-radius(50%);
    img{
      @include border-radius(50%);
    }
  }
  .info-testimonials{
    padding-left: 15px;
    @media(min-width: 1200px){
      padding-left: $theme-padding;
    }
  }
  .name-client{
    font-size: 15px;
    font-weight: 500;
    margin: 10px 0 0;
  }
  .job{
    font-size: 13px;
  }
  .star{
    margin: 0 0 10px;
  }
  .description{
    @media(min-width: 1200px){
      font-size: 1rem;
    }
    font-weight: 500;
  }
  .icon{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    @include size(50px,50px);
    border: 5px solid #fff;
    @include border-radius(50%);
    background: #EEF2F6;
    color: $theme-color;
    font-size: 60px;
    line-height: 1;
    span{
      height: 20px;
    }
  }
}

// custom menu
.widget-nav-menu{
  .menu{
    li{
      margin:0 0 5px;
      @media(min-width: 1200px){
        margin:0 0 10px;
      }
      > a{
        position: relative;
        @include transition(all 0.3s ease-in-out 0s);
        display: inline-block;
        color: $body-color;
      }
      &:hover,
      &.active,
      &.current-cat-parent,
      &.current-cat{
        > a{
          color: $link-hover-color;
          text-decoration: underline;
        }
      }
      &:last-child{
        margin:0;
      }
    }
  }
  .btn-url{
    font-weight: 500;
    color: $theme-color;
    text-decoration: underline;
  }
  .wrapper-url{
    margin: 5px 0 0;
  }
  &.st_line{
    .menu{
      li{
        margin-bottom: 0;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        @media(min-width: 1200px){
          margin-right: 20px;
        }
        &:last-child{
          margin-right: 0;
        }
      }
    }
  }
  &.st_circle{
    li{
      a{
        text-decoration: none !important;
        &:before{
          content:'';
          @include size(6px,6px);
          background-color: #CCCCCC;
          @include border-radius(50%);
          display: inline-block;
          vertical-align: middle;
          margin-right: 12px;
          margin-top: -3px;
          @include transition(all 0.3s ease-in-out 0s);
        }
        &:hover,&:focus{
          &:before{
            background-color: $link-hover-color;
          }
        }
      }
      &:hover,
      &.current-cat-parent,
      &.current-cat,
      &.active{
        > a{
          &:before{
            background-color: $link-hover-color;
          }
        }
      }
    }
  }
}
.widget-mailchimp{
  &.st1{
    .btn i{
      display: none;
    }
  }
  &.st2{
    .btn .text{
      display: none;
    }
  }
}
form.mc4wp-form{
  padding: 5px;
  @media(min-width: 1200px){
    padding: 10px;
  }
  background: #fff;
  @include border-radius($border-radius);
  [type="email"]{
    outline: none;
    border-color: transparent !important;
  }
  [type="submit"]{
    font-size: 15px;
    padding: 10px 30px;
    @include border-radius($border-radius !important);
  }
}
.wpcf7-form{
  position: relative;
  label{
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 5px;
    color: $body-link;
  }
  .form-control{
    margin-bottom: 1rem;
    height: 50px;
    @media(min-width: 1200px){
      margin-bottom: 20px;
      height: 55px;
    }
  }
  textarea.form-control{
    height: 150px;
    @media(min-width: 1200px){
      height: 250px;
      margin-bottom: $theme-margin;
    }
  }
}
//widget-brands
.widget-brand{
  .top-info{
    width: 100%;
    @media(min-width: 1200px){
      flex-shrink: 0;
      width: 18%;
      padding-right: 20px;
      text-align: right;
      + .content-inner{
        width: 82%;
        padding-left: 20px;
      }
    }
  }
  @media(min-width: 1200px){
    .content-inner{
      flex-grow: 1;
    }
  }
  .title{
    font-size: 1rem;
    margin: 0;
  }
  .brand-item{
    text-align: center;
    @include transition(all 0.2s ease-in-out 0s);
    img{
      display: inline-block;
    }
  }
  .slick-track{
    @include flexbox();
    align-items: center;
    -webkit-align-items: center;
    -ms-align-items: center;
  }
  &.st2{
    .brand-item {
      overflow: hidden;
      border:1px solid $border-color;
      @include border-radius($border-radius);
      @include box-shadow(0 20px 30px 0 rgba(#19192E,0.04));
      &:hover,&:focus{
        @include box-shadow(0 20px 30px 0 rgba(#19192E,0.08));
      }
    }
    .slick-list{
      padding-bottom: 30px;
      @media(min-width: 1200px){
        padding-bottom: 50px;
      }
    }
    .slick-dots{
      padding: 0;
    }
  }
  &.st3{
    .brand-item {
      @include opacity(0.4);
      &:hover,&:focus{
        @include opacity(1);
      }
    }
  }
}

.box-theme{
  padding: 15px;
  @media(min-width: 1200px){
    padding: 40px 30px;
  }
  @include border-radius($border-radius);
  background: #282664;
  color: #fff;
  .list-check{
    color: #fff;
  }
}

.times{
  @include flexbox();
  text-align: center;
  font-size: 35px;
  line-height: 1.5;
  @media(min-width: 1200px){
    font-size: 45px;
  }
  font-weight: 700;
  > div ~ div{
    margin-left: 20px;
    @media(min-width: 1200px){
      margin-left: 45px;
    }
  }
  .title{
    font-size: 15px;
    font-weight: 400;
  }
  span{
    display: block;
  }
}

.elementor-accordion {
  .elementor-accordion-item{
    margin-bottom: 20px;
    @include border-radius($border-radius);
    background: #fff; 
    @include box-shadow(0 1px 4px 0 rgba(#140342,0.07));
    &:last-child{
      margin-bottom: 0;
    }
    .elementor-tab-title{
      @include flexbox();
      align-items: center;
      width: 100%;
      .elementor-accordion-icon{
        margin-right: 25px;
        @include size(40px,40px);
        @include flexbox();
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: $theme-color;
        background: #E5F0FD;
        @include transition(all 0.2s ease-in-out 0s);
        @include border-radius(50%);
        &:hover,&:focus{
          color: #fff;
          background: $theme-color;
        }
      }
      &.elementor-active{
        .elementor-accordion-icon{
          color: #fff;
          background: $theme-color;
        }
      }
    }
  }
}

.text-theme{
  color: $theme-color !important;
}
.text-white{
  color: #fff !important;
}
.text-green{
  color: #00FF84 !important;
}
.elementor-widget-button.w-100{
  .elementor-button{
    width: 100%;
  }
}
.deleted_wpb_single_image{
  position: relative;
  overflow: hidden;
  &:before{
    position: absolute;
    z-index: 2;
    @include transition(all 0.2s ease-in-out 0s);
    content: '';
    @include size(100%,100%);
    background: $theme-color;
    @include opacity(0);
    top: 0;
    left: 0;
  }
  &:after{
    position: absolute;
    @include transition(all 0.3s ease-in-out 0s);
    content: '';
    top: $theme-margin;
    left: $theme-margin;
    right: $theme-margin;
    bottom: $theme-margin;
    border:1px solid #fff;
    z-index: 3;
    @include scale(0);
  }
  &:hover{
    &:before{
      @include opacity(0.5);
    }
    &:after{
      @include scale(1);
    }
  }
}
.widget-team{
  text-align: center;
  .team-item{
    @include border-radius($border-radius);
    overflow: hidden;
    position: relative;
    &:before{
      content: '';
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      background-color: rgba(39, 42, 51, .7);
      @include transition(all 0.3s ease-in-out 0s);
      @include size(100%,100%);
      @include opacity(0);
    }
    img{
      @include transition(all 0.5s ease-in-out 0s);
    }
  }
  .name-team{
    font-size: 1.125rem;
    margin:1rem 0 5px;
    @media(min-width: 1200px){
      margin:1.5625rem 0 5px;
    }
  }
  .listing{
    @media(min-width: 1200px){
      font-size: 0.9375rem;
    }
  }
  .social{
    position:absolute;
    left: 0;
    width:100%;
    z-index: 1;
    list-style: none;
    margin:0;
    top: 50%;
    @include translateY(0);
    visibility: hidden;
    @include opacity(0);
    @include transition(all 0.3s ease-in-out 0s);
    li{
      display: inline-block;
      margin-right: 10px;
      @media(min-width: 1200px){
        margin-right: 20px;
      }
      &:last-child{
        margin-right: 0;
      }
      a{
        color: #fff !important;
      }
    }
  }
  &:hover{
    .team-item img{
      @include transform(scale(1.15) rotate(-1deg));
    }
    .team-item:before{
      @include opacity(1);
    }
    .social{
      @include translateY(-50%);
      visibility: visible;
      @include opacity(1);
    }
  }
}
// vertical menu
.widget_apus_vertical_menu{
  .widget-title{
    font-size: 16px;
    font-weight: normal;
    margin:0 0 10px;
    padding:15px $theme-margin 0;
  }
  border-left: 4px solid #2e2d2d;
  .apus-vertical-menu{
    border:none;
    > li{
      margin-left: -4px;
      > a{
        border-left: 4px solid transparent;
        font-size: 16px;
        padding:0 $theme-margin;
      }
      &.active,&:hover{
        > a{
          border-color:$theme-color;
        }
      }
    }
  }
}
body.single-product .apus-main-content{
  overflow: hidden;
}
.nav.tabs-product{
  border:none;
  margin: 0 0 1.5rem;
  justify-content: center;
  position: relative;
  padding: ($theme-margin / 2) 0 0;
  @media(min-width: 1200px){
    margin-bottom: 40px;
    padding: $theme-margin 0 0;
  }
  &:before{
    content:'';
    position: absolute;
    top: 0;
    left: 0;
    @include size(100%,1px);
    background: $border-color;
    @media(min-width: 1200px){
      left: -1000px;
      @include size(3000px,1px);
    }
  }
  > li{
    display: inline-block;
    float: none;
    margin: 0 10px;
    @media(min-width: 1200px){
      margin: 0 20px;
    }
    > a{
      border: 0;
      font-size: 1rem;
      text-transform: capitalize;
      font-weight: 500;
      color: $body-link;
      display: inline-block;
      background:transparent;
      position:relative;
      padding: 0;
      &:hover,
      &:focus{
        color: $theme-color;
      }
    }
    &.active > a{
      color: $theme-color;
    }
  }
}
.updow{
  &:hover{
    .top-img img,
    .img img,
    .image-wrapper img{
      -webkit-animation: updow 0.8s ease-in-out 0s infinite ;
      animation: updow 0.8s ease-in-out 0s infinite ;
    }
  }
}
.updow-infinite{
  img{
    -webkit-animation: updow 1s ease-in-out 0s infinite ;
    animation: updow 1s ease-in-out 0s infinite ;
  }
}
@-webkit-keyframes updow {
  50% {
    @include translateY(-10px);
  }
  0%, 100% {
    @include translateY(0px);
  }
}

@keyframes updow {
  50% {
    @include translateY(-10px);
  }
  0%, 100% {
    @include translateY(0px);
  }
}

@-webkit-keyframes fadeleft {
  from {
    @include opacity(1);
  }
  to {
    @include opacity(0);
    @include translate(-15px,0);
  }
}
@keyframes fadeleft {
  from {
    @include opacity(1);
  }
  to {
    @include opacity(0);
    @include translate(-15px,0);
  }
}

@-webkit-keyframes faderight {
  from {
    @include opacity(0);
    @include translate(15px,0);
  }
  to {
    @include opacity(1);
    @include translate(0,0);
  }
}
@keyframes faderight {
  from {
    @include opacity(0);
    @include translate(15px,0);
  }
  to {
    @include opacity(1);
    @include translate(0,0);
  }
}
.mb0 {
    margin-bottom: 0px !important;
}
.width-full{
  width:100% !important;
}
//custom-menu
.custom-menu{
  list-style: none;
  padding:0;
  margin:0;
  li{
    padding-left: 22px;
    margin-bottom: 18px;
    position:relative;
    line-height: 1.4;
    &:before{
      content:'';
      position:absolute;
      top:0;
      left: 0;
      @include size(2px,100%);
      background:#221f1f;
      @include opacity(0);
      @include transition(all 0.3s ease-in-out 0s);
    }
    &:last-child{
      margin:0;
    }
    i{
      margin-right: 15px;
      font-size:18px;
      @media(min-width: 1200px){
        font-size:23px;
      }
    }
    a{
      color: $body-color;
      &:focus,
      &:hover{
        color: $body-link;
      }
    }
    &:hover,
    &.active{
      &:before{
        @include opacity(1);
      }
    }
  }
}
.banner-item{
  min-height: 100px;
  position: relative;
  z-index: 0;
  overflow: hidden;
  @include border-radius($border-radius);
  text-align: center;
  display: block;
  .title{
    position: absolute;
    z-index: 2;
    bottom: 0;
    left: 0;
    width: 100%;
    margin:0;
    padding: 1.25rem;
    font-size: 1.125rem;
    color: #fff;
  }
}
.banner-item-link{
  display: block;
  position: relative;
  &:before{
    content: '';
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    @include size(100%,100%);
    background-color: rgba(#272A33, 0.3);
  }
  img{
    @include transition(all 0.6s ease-in-out 0s);
  }
  &:hover,&:focus{
    img{
      @include transform(scale(1.1) rotate(-1deg));
    }
  }
}

/*-----------------------------*\
        Widget video
\*-----------------------------*/
.video-wrapper-inner{
  .popup-video{
    position: relative;
    display: inline-block;
    @include size(40px,40px);
    line-height: 36px;
    font-size: 18px;
    @media(min-width: 1200px){
      font-size: 20px;
      @include size(60px,60px);
      line-height: 56px;
    }
    @include border-radius(50%);
    @include transition(all 0.3s ease-in-out 0s);
    border: 2px solid $body-link;
    color: $body-link;
    text-align: center;
    margin-right: 10px;
    &:hover,&:focus{
      color: $theme-color;
      border-color: $theme-color;
    }
  }
  .title{
    margin:0;
    font-size: 1rem;
    font-weight: 500;
    @include transition(all 0.3s ease-in-out 0s);
    &:hover,&:focus{
      color: $theme-color;
    }
  }
  &:hover,&:focus{
    .title,
    .popup-video{
      color: $theme-color;
      border-color: $theme-color;
    }
  }
  &.st2{
    .popup-video{
      background: #fff;
      border-color: #fff;
      @include box-shadow( 0px 0px 0 7px rgba(#fff,0.3) );
      &:hover,&:focus{
        @include box-shadow( 0px 0px 0 12px rgba(#fff,0.4) );
      }
    }
  }
}
.elementor-icon-box-title{
  margin-top: 0;
}
.row-20{
  margin-left: -10px !important;
  margin-right: -10px !important;
  > [class*="col-"]{
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}
.row-10{
  margin-left: -5px !important;
  margin-right: -5px !important;
  [class*="col-"]{
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}
.list-circle{
  list-style: none;
  padding: 0;
  li{
    margin-bottom: 7px;
    @media(min-width: 1200px){
      margin-bottom: 12px;
    }
    position: relative;
    padding-left: 20px;
    &:before{
      content: '';
      background-color: #6A7A99;
      @include size(7px,7px);
      @include border-radius(50%);
      position: absolute;
      top: 10px;
      left: 0;
    }
  }
  &.column2{
    max-width: 600px;
  }
}
.list-circle-check{
  list-style: none;
  padding:0;
  @media(min-width: 768px){
    column-count: 2;
  }
  li{
    margin-bottom: 10px;
    @media(min-width: 1200px){
      margin-bottom: 17px;
    }
    &:before{
      font-size: 8px;
      content: '\f00c';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      display: inline-block;
      vertical-align: text-bottom;
      margin-right: 10px;
      background-color: #696969;
      @include size(18px,18px);
      @include border-radius(50%);
      line-height: 18px;
      text-align: center;
      color: #fff;
    }
  }
}
.column2{
  @media(min-width: 768px){
    column-count: 2;
  }
}
.m-770{
  max-width: 770px;
  margin-right: auto;
  margin-left: auto;
}
.mb-per2-gutter{
  margin-bottom: $theme-margin / 2;
}
@media(min-width: 992px){
  .mb-lg-gutter{
    margin-bottom: $theme-margin;
  }
}
.max-600{
  max-width: 600px;
}
.mb-30{
  margin-bottom: 1.875rem !important;
}
.list-check{
  margin: 0;
  padding: 0;
  list-style: none;
  font-weight: 500;
  color: $headings-color;
  font-size: 15px;
  text-transform: capitalize;
  li{
    @include flexbox();
    align-items: center;
    width: 100%;
    margin-bottom: 0.625rem;
    @media(min-width: 1200px){
      margin-bottom: 20px;
    }
    &:last-child{
      margin-bottom: 0;
    }
  }
  i{
    color: #fff;
    background: $theme-color;
    font-size: 8px;
    @include size(25px,25px);
    @include border-radius(50%);
    @include flexbox();
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
  }
}
.list-border-check{
  margin: 0;
  padding: 0;
  list-style: none !important;
  i{
    color: #6A7A99;
    border: 2px solid $border-color;
    font-size: 8px;
    @include size(20px,20px);
    @include border-radius(50%);
    @include flexbox();
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    margin-top: 2px;
  }
  li{
    @include flexbox();
    width: 100%;
    margin-bottom: 0.625rem;
    @media(min-width: 1200px){
      margin-bottom: 20px;
    }
    &:last-child{
      margin-bottom: 0;
    }
  }
}
.list-detail-course{
  list-style: none;
  padding: 0;
  margin: 0;
  i{
    font-size: 13px;
    margin-right: 10px;
  }
  li{
    margin-bottom: 12px;
  }
}
.widget-box{
  .item.col-sm-12{
    margin-bottom: 15px;
    @media(min-width: 1200px){
      margin-bottom: 25px;
    }
    &:last-child{
      margin-bottom: 0;
    }
  }
}
.item-box{
  @include transition(all 0.25s ease-in-out 0s);
  .features-box-content{
    padding-left: $theme-margin / 2;
    @media(min-width: 1200px){
      padding-left: $theme-margin;
    }
  }
  .number{
    @include size(37px,37px);
    text-align: center;
    line-height: 37px;
    @include border-radius(50%);
    background: $body-link;
    color: #fff;
    font-size: 15px;
    font-weight: 500;
  }
  .title{
    @include transition(all 0.25s ease-in-out 0s);
    font-size: 17px;
    font-weight: 500;
    margin: 0 0 5px;
  }
  .features-box-image{
    @include transition(all 0.25s ease-in-out 0s);
    width: 50px;
    height: 50px;
    @include flexbox();
    align-items: center;
    justify-content: center;
    background: #E3EDFD;
    color: $theme-color;
    font-size: 30px;
    @include border-radius(50%);
    @media(min-width: 1200px){
      @include size(80px,80px);
      font-size: 40px;
    }
    img,
    i:before{
      vertical-align: middle;
    }
    a{
      display: inline-block;
      line-height: 0;
    }
  }
  &.st2{
    align-items: center;
    .title{
      font-size: 20px;
      margin: 0;
    }
    .features-box-image{
      line-height: 1;
      @include size(auto,auto);
      background: transparent;
      font-size: 50px;
    }
    .features-box-content{
      @media(min-width: 1200px){
        padding-left: 20px;
      }
    }
  }
  &.st3{
    flex-direction: column;
    text-align: center;
    .features-box-content{
      padding: 15px 0 0;
      @media(min-width: 1200px){
        padding: 25px 0 0;
      }
    }
    .number{
      position: absolute;
      top: -3px;
      left: -3px;
      z-index: 1;
    }
    .top-inner{
      @include flexbox();
      justify-content: center;
    }
  }
}
.app-wrapper{
  @include flexbox();
  align-items: center;
  padding: 10px 15px;
  @media(min-width: 1200px){
    padding: 0.6rem 1.5rem;
  }
  @include border-radius($border-radius);
  background: #21242b;
  .aps_ico{
    margin-right: 8px;
    width: 35px;
    img{
      margin:0 !important;
    }
  }
  .aps_capt{
    color: #fff;
    font-size: 13px;
    font-weight: 600;
  }
  h4{
    color: #ffffff;
    margin: 0;
    font-size: 1rem;
    text-transform: capitalize;
    @media(min-width: 1200px){
      font-size: 21px;
    }
  }
  &:hover,&:focus{
    background: darken( #21242b,10%);
  }
  &.bg-theme{
    &:hover,&:focus{
      background: $theme-hover-color;
    }
  }
}
.under-theme{
  color: $theme-color;
  text-decoration: underline;
}
.text-underline{
  text-decoration: underline !important;
}
.space-b-20{
  margin-bottom: 20px;
}
.box-white{
  padding: 20px;
  @media(min-width: 1200px){
    padding: 50px 60px;
  }
  background: #fff;
  @include border-radius($border-radius);
  .title{
    margin: 0;
    font-size: 30px;
    @media(min-width: 1200px){
      font-size: 55px;
    }
  }
}
.recommen-inner{
  position: relative;
  .form-select{
    background-color: #EEF2F6;
    @include border-radius(60px);
    outline: none;
    @include box-shadow(none !important);
    height: 50px;
    min-width: 200px;
    @media(min-width: 1200px){
      height: 60px;
    }
    color: $body-link;
    font-weight: 500;
    border:2px solid #EEF2F6;
    &:focus{
      border-color: darken(#EEF2F6, 7%); 
    }
  }
  > *:not(.wpcf7-spinner){
    display: block;
    margin-right:$theme-margin / 2;
    @media(min-width: 1200px){
      margin-right: $theme-margin;
    }
    &:last-child{
      margin-right: 0;
    }
    @media(max-width: 575px){
      width: 100%;
      margin: 0 0 10px;
    }
  }
  option{
    background: #fff;
  }
  [type="submit"]{
    margin: 0;
    @include border-radius(60px);
  }
  @media(min-width: 1200px){
    .wpcf7-spinner{
      position: absolute;
      top: 50%;
      left: 100%;
      @include translateY(-50%);
    }
  }
  @media(max-width: 767px){
    .wpcf7-spinner{
      margin: 10px 0 0;
    }
  }
  &.st_white{
    .form-select{
      background-color: #fff;
      border-color: #fff;
    }
  }
}
.elementor-tabs-view-horizontal.elementor-widget-tabs{
  .elementor-tab-mobile-title{
    display: none !important;
  }
  .elementor-tabs-wrapper{
    position: relative;
    @include flexbox();
    align-items: center;
    overflow: auto;
    &:before{
      content: '';
      background: $border-color;
      @include size(100%,2px);
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .elementor-tab-desktop-title{
    font-size: 15px;
    font-weight: 400;
    color: $body-color;
    padding: 15px 0;
    border: 0;
    margin: 0;
    position: relative;
    z-index: 1;
    display: block;
    white-space: nowrap;
    &:before{
      @include transition(all 0.3s ease-in-out 0s);
      content:'';
      position: absolute;
      bottom: 0;
      left: 0;
      right: inherit;
      height: 2px !important;
      width: 0;
      background: $theme-color;
      border:0 !important;
    }
    &:after{
      display: none !important;
    }
    &.elementor-active{
      color: $theme-color;
      &:before{
        width: 100% !important;
      }
    }
    + .elementor-tab-desktop-title{
      margin-left: 15px;
      @media(min-width: 1200px){
        margin-left: 35px;
      }
    }
  }
  .elementor-tabs-content-wrapper{
    border:0;
    padding: 30px 0 0;
    @media(min-width: 1200px){
      padding: 50px 0 0;
    }
    .elementor-tab-content{
      padding: 0;
      border:0;
    }
  }
}