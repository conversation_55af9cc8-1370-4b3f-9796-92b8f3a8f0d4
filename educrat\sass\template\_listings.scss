.lp-archive-courses ul, .lp-archive-courses ol{
	list-style: initial;
	padding: 0;
}
.banner-content-wrapper{
	@include transition(all 0.3s ease-in-out 0s);
	.banner-title{
		@include transition(all 0.2s ease-in-out 0s);
		font-size: $font-size-base;
		line-height: 1.5;
		@media(min-width: 1200px){
			font-size: 17px;
		}
		font-weight: 500;
		margin:0 0 2px;
	}
	.number{
		@include transition(all 0.2s ease-in-out 0s);
		color: $body-color;
		font-size: 13px;
	}
	.features-box-image{
		@include transition(all 0.2s ease-in-out 0s);
	}
}
.banner-content-wrapper.style1{
	align-items: center;
	text-align: center;
	padding: 15px;
	background: #EEF2F6;
	@include border-radius($border-radius);
	@media(min-width: 1200px){
		padding: 25px 38px;
	}
	.features-box-image{
		color: $theme-color;
		font-size: 25px;
		width: 50px;
		height: 50px;
		background: #fff;
		margin-bottom: 10px;
		@include border-radius(50%);
		line-height: 1;
		@media(min-width: 1200px){
			font-size: 45px;
			width: 90px;
			height: 90px;
			margin-bottom: 15px;
		}
	}
	&:hover,&:focus{
		background: #1A064F;
		.number,
		.banner-title{
			color: #fff;
		}
	}
}

.banner-content-wrapper.style2{
	padding: 1rem;
	@include border-radius($border-radius);
	text-align: center;
	background-color: $body-color;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	position: relative;
	z-index: 1;
	overflow: hidden;
	@media(min-width: 1200px){
		padding: $theme-margin;
	}
	&:before{
		@include transition(all 0.3s ease-in-out 0s);
		z-index: -1;
		position: absolute;
		@include opacity(0);
		background: $theme-color;
		@include size(100%,100%);
		content:'';
		top: 0;
		left: 0;
	}
	.number{
		color: #fff;
	}
	.banner-title{
		color: #fff;
	}
	.banner-content{
		color: #fff;
	}
	.right-inner{
		position: absolute;
		width: 100%;
		z-index: 1;
		top: 50%;
		left: 0;
		@include translateY(-50%);
	}
	&:hover,&:focus{
		&:before{
			@include opacity(0.6);
		}
	}
}
.banner-content-wrapper.style3{
	flex-direction: row !important;
	align-items: center;
	.features-box-image{
		flex-shrink: 0;
		@include size(80px,80px);
		line-height: 1;
		overflow: hidden;
		@include border-radius($border-radius);
		+ .right-inner{
			padding-left: 15px;
			@media(min-width: 576px){
				padding-left: 20px;
			}
		}
	}
	.right-inner{
		flex-grow: 1;
	}
	&:hover{
		.banner-title{
			color: $theme-color;
		}
	}
}
.banner-content-wrapper.style4{
	flex-direction: row !important;
	align-items: center;
	border:1px solid $border-color;
	padding: 10px;
	background: #fff;
	@include border-radius($border-radius);
	.features-box-image{
		flex-shrink: 0;
		@include size(60px,60px);
		font-size: 30px;
		@media(min-width: 1200px){
			@include size(80px,80px);
			font-size: 35px;
		}
		line-height: 1;
		overflow: hidden;
		color: $body-color;
		background: #EEF2F6;
		@include border-radius(50%);
		+ .right-inner{
			padding-left: 15px;
			@media(min-width: 576px){
				padding-left: 20px;
			}
		}
	}
	.right-inner{
		flex-grow: 1;
	}
	&:hover{
		@include box-shadow(0 6px 15px 0 rgba(#404F68,0.05));
		.banner-title{
			color: $theme-color;
		}
		.features-box-image{
			color: #fff;
			background: $theme-color;
		}
	}
}
.banner-content-wrapper.style5{
	.features-box-image{
		line-height: 1;
		font-size: 60px;
		padding: 30px 15px;
		@media(min-width: 1200px){
			font-size: 100px;
			padding: 60px 30px;
		}
		@include border-radius($border-radius);
		background: #F5F7FE;
		color: #1A064F;
		margin-bottom: 15px;
	}
	&:focus,
	&:hover{
		.features-box-image,
		.banner-title{
			color: $theme-color;
		}
	}
}

.tabs-course{
	border:0;
	margin: 0 0 $theme-margin;
	@media(min-width: 1200px){
		margin-bottom: 60px;
	}
	> li{
		margin: 0 2px 0 0;
		&:last-child{
			margin-right: 0 !important;
		}
		> a{
			padding: 7px 18px;
			@include border-radius($border-radius);
			display: inline-block;
			font-size: $font-size-base;
			color: $body-color;
			font-weight: 500;
			@include transition(all 0.3s ease-in-out 0s);
			&.active,
			&:focus,
			&:hover{
				background: var(--educrat-theme-color-007);
				color: $theme-color;
			}
		}
	}
	&.st_gray{
		background: #EEF2F6;
		@include border-radius(50px);
		padding: 5px;
		> li > a{
			padding: 4px 25px;
		}
	}
}
.widget-courses-tabs{
	.title{
		font-size: 25px;
		@media(min-width: 1200px){
			font-size: 30px;
		}
		margin: 0 0 5px;
	}
	.top-info{
		margin-bottom: 20px;
		@media(min-width: 1200px){
			margin-bottom: 60px;
		}
		.ms-auto{
			margin-bottom: 5px;
		}
	}
}

// detail course
.review-stars-rated{
  position:relative;
  overflow: hidden;
  width: 82px;
  .review-stars{
    list-style: none;
    padding:0;
    margin:0;
    color: #dadde6;
    white-space: nowrap;
    overflow: hidden;
    font-size: 10px;
    letter-spacing: 2px;
    li{
      display: inline-block;
    }
    &.filled{
      color: #ff9800;
      position:absolute;
      top:0;
      left: 0;
    }
  }
}
.review-stars-rated-wrapper{
  @include flexbox();
  align-items: center;
  -webkit-align-items: center;
  .nb-review{
    margin-left: 7px;
  }
  i,
  .nb-pre-review{
    margin-right: 7px;
  }
}
.wrapper_rating_avg{
	.rating_avg{
    margin-right: 7px;
    font-weight: 500;
    font-size: 14px;
    color: #ff9800;
  }
}
.review-stars-wrap{
  display: inline-block;
  vertical-align: middle;
  position:relative;
  overflow: hidden;
  clear: both;
  cursor: pointer;
  .review-stars{
    color: #e1e1e1;
    list-style: none;
    padding:0;
    margin:0;
    font-size: 13px;
    letter-spacing: 6px;
    li{
      float: left;
      &.active{
        color: #e1e1e1;
      }
    }
    &.filled{
      color: #ff9800;
      position:absolute;
      top:0;
      left:0;
    }
  }
}
.box-info-white{
	position: relative;
	z-index: 1;
	margin-bottom: $theme-margin;
	@media(min-width: 1200px){
		margin-bottom: 60px;
	}
	.title{
		font-weight: 500;
		font-size: 20px;
		margin: 0 0 10px;
		@media(min-width: 1200px){
			margin-bottom: 20px;
		}
	}
	.comment-form{
		margin: 0;
	}
}
.detail-average-rating{
	padding: $theme-padding / 2;
	@media(min-width: 768px){
		width: 30%;
		padding: $theme-padding;
		margin-right: 10px;
	}
	text-align: center;
	background: #F5F7FE;
	@include border-radius($border-radius);
	.average-value{
		font-size: 35px;
		@media(min-width: 768px){
			font-size: 60px;
		}
		font-weight: 500;
		line-height: 1.2;
		display: block;
		color: $body-link;
	}
	.total-rating{
		margin-top: 2px;
	}
	.tutor-ratings-stars{
		font-size: 11px;
	}
}
.detail-rating{
	padding: $theme-padding / 2;
	background: #F5F7FE;
	@include border-radius($border-radius);
	@media(min-width: 768px){
		width: calc(100% - 30%);
		padding: $theme-padding;
	}
}
.list-rating{
	.progress{
		height: 5px;
		width: calc(100% - 140px);
		background: #CCE0F8;
		.progress-bar{
			background: $theme-color;
		}
	}
	.value-content{
		@include flexbox();
		align-items: center;
	}
	.value{
		color: $body-link;
		display: inline-block;
		font-size: $font-size-base;
		width: 140px;
		padding-left: 15px;
	}
}
.item-rating{
	margin-bottom: 5px;
	@media(min-width: 1200px){
		margin-bottom: 10px;
	}
	&:last-child{
		margin-bottom: 0;
	}
}
// author
.lp-course-author{
	.course-author__pull-left{
		width: 30px;
		height: 30px;
		@include border-radius(30px);
		overflow: hidden;
	}
	.author-title{
		font-weight: 400;
		font-size: 14px;
		padding-left: 10px;
	}
	a{
		color: $body-color;
		&:hover,&:focus{
			color: $body-link;
		}
	}
}
.tutor-tag-list{
	margin: 0;
}
.tutor-course-detail-author,
.lp-course-detail-author{
	.author-image{
		@include size(120px,120px);
		@include border-radius(50%);
		overflow: hidden;
	}
	.tutor-avatar-lg{
		@include size(100%,100%);
		@inlcude box-shadow(none);
	}
	.course-author-infomation{
		padding-left: 20px;
	}
}
.course-detail-author{
	.author-description{
		margin-top: 10px;
		@media(min-width: 1200px){
			margin-top: 20px;
		}
	}
}
.author-social{
	a{
		width: 36px;
		height: 36px;
		@include flexbox();
		background: var(--educrat-theme-color-010);
		align-items: center;
		justify-content: center;
		border: 1px solid var(--educrat-theme-color-010);
		@include border-radius($border-radius);
		font-size: 12px;
		color: $theme-color;
		&:hover,&:focus{
			color: #fff;
			background: $theme-color;
			border-color: $theme-color;
		}
		+ a{
			margin-left: 10px;
			@media(min-width: 1200px){
				margin-left: 15px;
			}
		}
	}
}
.author-top-content{
	margin-top: 2px;
	i{
		margin-right: 5px;
	}
	> *{
		display: inline-block;
		vertical-align: middle;
		+ *{
			margin-left: 0.625rem;
			@media(min-width: 1200px){
				margin-left: 1rem;
			}
		}
	}
}
.course-author-title{
	font-size: 17px;
	font-weight: 500;
	margin: 0 0 3px;
}
.comments-course{
	.comment-list{
		.the-comment{
			margin: 0 0 20px;
			padding: 0 0 20px;
			border-bottom: 1px solid $border-color;
			@media(min-width: 1200px){
				margin: 0 0 35px;
				padding: 0 0 35px;
			}
		}
		> .comment:last-child > .the-comment{
			margin: 0;
			padding: 0;
			border: 0;
		}
	}
}
#learn-press-checkout{
	margin-top: $theme-margin;
	margin-bottom: $theme-margin;
	@media(min-width: 1200px){
		margin-top: 50px;
		margin-bottom: 50px;
	}
}
.lp-content-wrap > h2{
	font-size: 24px;
	font-weight: 500;
}
.lp-checkout-form__before .lp-checkout-block h4, .lp-checkout-form__after .lp-checkout-block h4{
	font-size: 22px;
	font-weight: 500;
}
#checkout-order .course-name,
#checkout-order .col-number{
	font-weight: 500;
}
#checkout-order .order-total .col-number{
	color: $body-link;
}
#checkout-payment #checkout-order-action button.loading::before {
	margin-right: 3px;
}
.lp-list-table thead tr th{
	font-weight: 500;
	font-size: 1rem;
	color: $body-link;
}
.lp-user-profile .profile-orders .column-order-actions a{
	color: $theme-color !important;
}

.learn-press-form .form-field input, 
.learn-press-form .form-field textarea, 
form[name="profile-change-password"] .form-field input, 
form[name="profile-change-password"] .form-field textarea{
	outline: none !important;
	@include border-radius($border-radius);
	border: 1px solid $border-color !important;
	padding: 8px 15px !important;
	&:focus{
		border-color: $theme-color !important;
	}
}
.learnpress_avatar__button{
	margin:10px 10px 0 0;
}
button.learnpress_avatar__button,
#learn-press-profile-basic-information button[type="submit"],
form[name="profile-change-password"] button[type="submit"],
#popup-course #popup-content .lp-button,
#learn-press-profile #profile-content .lp-button,
#checkout-payment #checkout-order-action .lp-button,
.lp-course-buttons .lp-button,
.learnpress-page .lp-button,
.learnpress-page #lp-button{
	border:1px solid transparent;
	color: $theme-color;
	background: var(--educrat-theme-color-007);
	padding: 0.65rem 1.875rem;
	height: auto;
	font-size: $font-size-base;
	font-family: $font-family-base;
	font-weight: 500;
	line-height: $line-height-base;
	@include border-radius($border-radius);
	@include opacity(1);
	&:hover,&:focus{
		@include opacity(1);
		background: $theme-color;
		border-color: $theme-color;
		color: #fff;
	}
}
#popup-course #popup-header .lp-button{
	padding: 0 30px;
	background: $theme-color;
	color: #fff;
	white-space: nowrap;
	&:hover,&:focus{
		color: #fff;
		background: $theme-hover-color;
	}
}
.profile-basic-information .form-field > label, form[name="profile-change-password"] .form-field > label{
	font-size: $font-size-base;
	font-weight: 400;
	font-style: normal;
}
.course-header{
	background: #fff;
	padding-bottom: $theme-margin;
	.course-category{
		margin-bottom: 10px;
		@media(min-width: 1200px){
			margin-bottom: 15px;
		}
	}
	.course-excerpt{
		margin-top: 0.75rem;
		p:last-child{
			margin-bottom: 0;
		}
	}
	.course-header-bottom{
		margin-top: 10px;
		@media(min-width: 1200px){
			margin-top: 18px;
		}
	}
	.title{
		text-transform: capitalize;
		font-size: 22px;
		@media(min-width: 1200px){
			font-size: 30px;
			line-height: 45px;
		}
		margin: 0 0 10px;
		@media(min-width: 1200px){
			margin-bottom: 15px;
		}
	}
	.course-header-meta{
		margin-top: 10px;
		@media(min-width: 1200px){
			margin-top: 15px;
		}
		> div{
			display: inline-block;
			vertical-align: middle;
			font-size: 14px;
			+ div{
				margin-left: $theme-margin / 2;
				@media(min-width: 1200px){
					margin-left: $theme-margin;
				}
			}
		}
		i{
			line-height: 1;
			vertical-align: middle;
			font-size: 1rem;
			margin-right: 5px;
		}
	}
	&.default{
		background: #F5F7FE;
		padding: 0;
		margin: 0 0 $theme-margin;
	}
	.inner-default{
		padding: 15px 0 $theme-margin;
		@media(min-width: 1200px){
			padding: 65px 0rem 90px;
		}
	}
}
.course-category-item{
	padding: 10px 16px 8px;
	line-height: 1;
	font-size: 11px;
	font-weight: 500;
	@include border-radius(30px);
	display: inline-block;
	text-transform: uppercase;
	color: #fff;
	background: $theme-color;
	&:hover,&:focus{
		color: #fff;
		background: $theme-hover-color;
	}
	+ .course-category-item{
		margin-left: 5px;
		@media(min-width: 1200px){
			margin-left: 12px;
		}
	}
}
// header v2
.course-header.v2{
	position: relative;
	z-index: 1;
	color: #6A7A99;
	.breadcrumb > li + li::before{
		color: #6A7A99;
	}
	a:not([class]){
		color: #6A7A99;
		&:hover,&:focus{
			color: #fff;
		}
	}
	&:before{
		z-index: -1;
		content: '';
		position: absolute;
		@include size(100%,100%);
		top: 0;
		left: 0;
		background: $body-link;
	}
	.active,
	.title{
		color: #fff;
	}
}
// header v3
.header-inner-v3{
	background: $theme-color;
	color: #fff;
	max-width: 1500px;
	margin-right: auto;
	margin-left: auto;
	@media(min-width: 1530px){
		@include border-radius($border-radius);
	}
	a:not([class]){
		color: #fff !important;
		&:hover,&:focus{
			text-decoration: underline;
		}
	}
	.tutor-color-secondary,
	.title{
		color: #fff;
	}
	.inner-default{
		padding: $theme-padding 0;
	}
	@media(min-width: 1200px){
		.inner-default{
			padding: 90px 0;
		}
	}
	.course-category-item{
		background: #00FF84;
		color: $body-link;
		&:hover,&:focus{
			color: #fff;
			background: #4417FF;
		}
	}
}
.course-header.v3 .apus-breadscrumb{
	@media(min-width: 1530px){
		margin-bottom: 90px;
	}
}
// header v4
.course-header.v4{
	background: #fff;
	.inner-default{
		padding: 0;
	}
	@media(min-width: 1200px){
		.inner-default {
		  padding: 25px 0rem 50px;
		}
	}
}
.course-header.v5{
	background: #F7F8FB;
	.apus-breadscrumb{
		background: #fff;
	}
	+ #main-container{
		background: #F7F8FB;
	}
	.inner-default{
		padding: 0;
	}
	@media(min-width: 1200px){
		.inner-default{
			padding: 25px 0rem 50px;
		}
	}
}
.course-header.v6{
	background: $body-link;
	color: #6A7A99;
	a:not([class]){
		color: #6A7A99;
		&:hover,&:focus{
			color: #fff;
		}
	}
	.breadcrumb > li + li::before{
		color: #6A7A99;
	}
	.active,
	.title{
		color: #fff;
	}
	.apus-social-share{
		margin-top: 20px;
		text-align: left !important;
		@media(min-width: 1500px){
			margin-left: -15px;
		}
		a{
			color: #6A7A99;
			background: transparent;
			&:hover,&:hover{
				color: #fff;
				background: #282664;
			}
		}
	}
	.inner-v6{
		padding: 10px 0 0;
		@media(min-width: 1200px){
			padding: 70px 0 30px;
		}
	}
	.course-header-right{
		@media(min-width: 1350px){
			padding-left: 110px;
		}
		.tutor-video-player,
		.course-video{
			@include border-radius($border-radius);
			overflow: hidden;
			margin-bottom: $theme-margin / 2;
			@media(min-width: 1200px){
				margin-bottom: $theme-margin;
			}
		}
		.course-price{
			color: #fff;
			line-height: 1;
			font-size: 18px;
			font-weight: 500;
			margin-bottom: $theme-margin / 2;
			@media(min-width: 1200px){
				margin-bottom: $theme-margin;
				font-size: 24px;
			}
		}
		.sale-price{
			del,
			.origin-price{
				display: none;
			}
			+ del,
			+ .origin-price{
				font-size: $font-size-base;
				margin-right: 10px;
				color: #6A7A99;
				text-decoration: line-through;
			}
		}
		.lp-button{
			width: 100%;
			text-align: center;
			@media(min-width: 1200px){
				font-size: 1rem;
				padding: 14px 30px;
			}
			border-color: $theme-color;
			background: $theme-color;
			color: #fff;
			&:hover,&:focus{
				border-color: $theme-hover-color;
				background: $theme-hover-color;
				color: #fff;
			}
			+ *{
				margin-top: 10px;
			}
		}
		.lp-course-buttons{
			form{
				width: 100%;
			}
		}
	}
	.course-info-widget{
		background: transparent;
		@include border-radius(0);
		border: 0;
		padding: 0;
		@include box-shadow(none);
		.bottom-inner{
			padding: 0;
		}
	}
}

.course-cover-thumb{
	position: relative;
	overflow: hidden;
	.video{
		bottom: 12px;
		left: 10px;
		position: absolute;
		z-index: 1;
		line-height: 40px;
		padding: 0 12px;
		background: rgba(2,2,2,0.2);
		@include border-radius($border-radius);
		color: #ffffff;
		font-weight: 500;
		font-size: 1rem;
		font-family: $headings-font-family;
		@include transition(all 0.3s ease-in-out 0s);
		i{
			width: 25px;
			height: 25px;
			font-size: 10px;
			margin-right: 7px;
			@include border-radius(50%);
			display: inline-block;
			line-height: 25px;
			text-align: center;
			background: #ffffff;
			color: #7b7d98;
		}
		&:hover,&:focus{
			background: rgba(2,2,2,0.5);
		}
	}
}
.course-detail-info-bottom{
	font-size: 14px;
	padding: 1rem;
	@media(min-width: 1200px){
		padding: 1.5rem 20px;
	}
	background: #fff;
	@include border-radius(10px);
	@include box-shadow(rgba(62, 28, 131, 0.1) 0px 0px 20px 0px);
	i{
		width: 50px;
		height: 50px;
		font-size: 20px;
		@include border-radius(50%);
		background: rgba(255, 87, 34,0.11);
		border: 1px dashed;
		color: #ff5722;
		&.fa-play{
			background: rgba(144, 106, 212,0.12);
			color: #906ad4;
		}
		&.fa-user-shield{
			background: rgba(124, 191, 47,0.12);
			color: #7cbf2f;
		}
	}
	.inner-right{
		flex: 1;
		padding-left: 1rem;
	}
	.info{
		font-size: 18px;
		margin: 0 0 2px;
	}
}
.single-content-course{
	padding-bottom: $theme-padding;
	@media(min-width: 1200px){
		padding-bottom: 50px;
	}
	&.v4{
		 @media (min-width: 1200px){
			.detail-course .sidebar {
			  margin-top: -350px;
			}
		}
	}
	&.v5{
		 @media (min-width: 1200px){
		 	padding-bottom: 80px;
			.detail-course .sidebar {
			  margin-top: -350px;
			}
		}
		.apus-lp-content-area{
			border: 1px solid $border-color;
			background: #fff;
			@include box-shadow(0 20px 30px 0 rgba(#19192E,0.04));
			@include border-radius($border-radius);
			@media(max-width: 991px){
				margin-bottom: $theme-margin;
			}
			.course-single-tab{
				padding: 15px 15px 0;
				border-width: 0 0 1px;
				@media(min-width: 1200px){
					padding: 14px 30px 0;
				}
			}
			.course-tab-panels{
				padding: 0 15px 15px;
				@media(min-width: 1200px){
					padding: 0 30px 30px;
				}
				.box-info-white:last-child{
					margin-bottom: 0;
				}
			}
		}
	}
	&.v6{
		.apus-lp-content-area{
			padding-top: $theme-padding;
			@media(min-width: 1200px){
				padding-top: 100px;
			}
		}
		@media(min-width: 1200px){
			.course-single-tab{
				border-width: 0 0 0 1px !important;
				border-style: solid;
				border-color: $border-color;
				> li{
					width: 100%;
					margin: 0;
					> a{
						font-size: 18px;
						padding: 10px 20px;
						color: $body-link;
						&:before{
							top: 0;
							bottom: inherit;
							left: -1px;
							@include size(2px,0);
						}
						&.is-active,
						&.active{
							color: $theme-color;
							&:before{
								@include size(2px,100%);
							}
						}
					}
					&:hover{
						> a.is-active,
						> a.active,
						> a{
							color: $theme-color;
							&:before{
								@include size(2px,100%);
							}
						}
					}
				}
			}
			.apus-lp-content-area{
				@include flexbox();
				align-items: start;
			}
			.tutor-is-sticky,
			.course-single-tab{
				width: 30%;
				padding-right: $theme-margin;
			}
			.tutor-is-sticky .course-single-tab{
				padding: 0;
				width: 100%;
			}
			.tutor-tab,
			.course-tabs-scrollspy{
				width: 70%;
			}
		}
	}
}
.tutor-course-details-page .tutor-course-details-tab .tutor-is-sticky{
	z-index: 2;
}
.course-tab-panel .course-description p{
	font-size: $font-size-base;
	color: $body-color;
}
.tutor-course-details-widget,
.apus-course-extra-box{
	margin-bottom: $theme-margin;
	@media(min-width: 1200px){
		margin-bottom: 40px;
	}
	&:last-child{
		margin: 0;
	}
	.tutor-course-details-widget-title,
	.title{
		font-weight: 500;
		font-size: 20px;
		margin: 0 0 10px;
		@media(min-width: 1200px){
			margin-bottom: 20px;
		}
	}
	ul{
		margin: 0;
	  padding: 0;
	  list-style: none;
	  color: $body-color;
	  li{
	  	&:before{
	  		content: "\f15d";
	  		font-family: Flaticon;
	  		color: #6A7A99;
		    border: 2px solid $border-color;
		    font-size: 8px;
		    @include size(20px,20px);
		    @include border-radius(50%);
		    @include flexbox();
		    align-items: center;
		    justify-content: center;
		    margin-right: 10px;
		    flex-shrink: 0;
	  	}
	    @include flexbox();
	    align-items: center;
	    width: 100%;
	    margin-bottom: 0.625rem;
	    @media(min-width: 1200px){
	      margin-bottom: 20px;
	    }
	    &:last-child{
	      margin-bottom: 0;
	    }
	  }
	}
	.tutor-icon-bullet-point::before {
		display: none;
	}
	.tutor-fs-6{
		font-size: inherit;
	}
}
.tutor-fs-1, .tutor-fs-2, .tutor-fs-3, .tutor-fs-4, .tutor-fs-5, .tutor-fs-6, .tutor-fs-7, .tutor-fs-8, .tutor-fs-9{
	line-height: inherit;
}
// accordion
.accordion-header{
	margin: 0;
}
.accordion-button{
	padding:.75rem 1.25rem;
	@media(min-width: 1200px){
		padding: 1.2rem $theme-padding;
	}
	font-size: 1rem;
	font-weight: 500;
	color: $body-link;
	border: 0;
	background: #F7F8FB !important;
	@include box-shadow(none !important);
	@include transition(all 0.3s ease-in-out 0s);
	&:before{
		content: "\e64b";
		font-family: 'themify';
		font-weight: 700;
		position: absolute;
		top: 50%;
		display: block;
		right: 1rem;
		@media(min-width: 1200px){
			right: $theme-margin;
		}
		font-size: 13px;
		display: block;
		@include translateY(-50%);
		@include transition(all 0.3s ease-in-out 0s);
	}
	&:after{
		display: none;
	}
	&:not(.collapsed){
		color: $theme-color;
		&:before{
			content: "\e648";
		}
	}
}
.accordion-item{
	border:1px solid $border-color !important;
	@include border-radius($border-radius);
	+ .accordion-item{
		margin-top: 10px;
	}
	.accordion-body{
		border-top: 1px solid $border-color;
		@media(min-width: 1200px){
			padding: 25px $theme-padding;
		}
	}
}
.course-video{
	position: relative;
	overflow: hidden;
	&:before{
		padding-top: 56.25%;
		content: "";
		display: block;
	}
	iframe{
		max-width: 100%;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
}
.course-summary{
	.tutor-video-player,
	.course-video{
		@include border-radius($border-radius-lg);
	}
	.tutor-video-player{
		margin-bottom: $theme-margin;
		@media(min-width: 1200px){
			margin-bottom: 60px;
		}
	}
}
#learn-press-course-curriculum.course-curriculum{
	ul.curriculum-sections{
		.section{
			cursor: pointer;
			border:1px solid $border-color;
			@include border-radius($border-radius);
			background: #F7F8FB !important;
			overflow: hidden;
			+ .section{
				margin-top: 10px;
			}
			.section-desc{
				color: $body-color;
				font-style: normal;
				margin: 10px 0 0;
			}
			&:not(.closed){
				.section-toggle:before{
					content: "\e648" !important;
				}
			}
		}
		.section-header{
			height: auto !important;
			border: 0;
			padding: .75rem 1.25rem !important;
			@media(min-width: 1200px){
				padding: 21px $theme-margin !important;
			}
			.section-left{
				.section-title{
					font-size: 1rem;
					font-weight: 500;
					color: $headings-color;
				}
				.section-toggle{
					@include size(14px,14px);
					flex: 0 0 14px !important;
					position: relative;
					line-height: 1;
					color: $body-link;
					font-size: 13px;
					i{
						display: none !important;
					}
					&:before{
						content: "\e64b";
						font-family: 'themify';
						font-weight: 700;
						position: absolute;
						top: 0;
						left: 0;
						display: block;
						@include transition(all 0.3s ease-in-out 0s);
					}
				}
			}
		}
		.section-content{
			margin: 0;
			background: #fff;
			.inner{
				padding: 1rem 1.25rem;
				@media(min-width: 1200px){
					padding: 25px $theme-margin;
				}
			}
			.course-item{
				margin: 0 0 20px;
				padding: 0;
				background: transparent;
				&:last-child{
					margin-bottom: 0;
				}
				&.item-locked .course-item-status:before{
					color: $danger;
				}
				.section-item-link{
					width: 100%;
					padding: 0;
					&:hover,&:focus{
						.item-name{
							color: $theme-color;
						}
					}
				}
				.section-item-link::before,
				.course-item-meta,
				.item-name{
					padding: 0
				}
				.section-item-link::before{
					font-size: 11px;
					color: $theme-color;
					background: var(--educrat-theme-color-007);
					@include size(25px,25px);
					min-width: 25px;
					line-height: 25px;
					text-align: center;
					@include border-radius(50%);
					padding: 0 !important;
				}
				.item-name{
					font-size: $font-size-base;
					font-weight: 400;
					@include transition(all 0.3s ease-in-out 0s);
					padding: 0 12px;
				}
			}
		}
	}
}
#popup-course #popup-sidebar .course-item{
	min-height: 0;
	padding: 12px 20px;
	margin-bottom: 10px;
	@include border-radius(3px);
}
#popup-course #popup-sidebar .section{
	padding: 0;
	border-width: 0 0 1px;
	border-color: $border-color;
}
.quiz-intro-item::before {
	color: $theme-color;
}
.lp-archive-courses thead th, .lp-archive-courses tr th{
	font-weight: 500;
}
.quiz-status > div{
	background: #F7F8FB;
}
.course-info-widget{
	border: 1px solid $border-color;
	@include border-radius($border-radius);
	background: #fff;
	@include box-shadow(0 20px 30px 0 rgba(#19192E,0.04));
	padding: 10px;
	.tutor-video-player,
	.course-video{
		@include border-radius($border-radius);
		overflow: hidden;
		margin: 0 0 10px;
	}
	.course-price{
		color: $body-link;
		line-height: 1;
		font-size: 18px;
		font-weight: 500;
		margin-bottom: $theme-margin / 2;
		@media(min-width: 1200px){
			margin-bottom: $theme-margin;
			font-size: 24px;
		}
	}
	.sale-price{
		del,
		.origin-price{
			display: none;
		}
		+ del,
		+ .origin-price{
			font-size: $font-size-base;
			margin-right: 10px;
			color: $body-color;
			text-decoration: line-through;
		}
	}
	.tutor-btn,
	.lp-button{
		width: 100%;
		text-align: center;
		@media(min-width: 1200px){
			font-size: 1rem;
			padding: 14px 30px;
		}
		border-color: $theme-color;
		background: $theme-color;
		color: #fff;
		line-height: $line-height-base;
		font-weight: 500;
		text-transform: capitalize;
		&:hover,&:focus{
			border-color: $theme-hover-color;
			background: $theme-hover-color;
			color: #fff;
		}
		+ *{
			margin-top: 10px;
		}
	}
	.save-bookmark-btn{
		margin-top: 18px;
		@media(min-width: 1200px){
			padding: 13px 50px;
		}
	}
	.lp-course-buttons form{
		width: 100%;
	}
	.purchase-course{
		width: 100%;
	}
	.title-info{
		font-size: 18px;
		margin: 0 0 1rem;
	}
	.bottom-inner{
		padding: 10px;
		@media(min-width: 1200px){
			padding: 20px;
		}
	}
	.apus-social-share{
		margin-top: $theme-margin / 2;
		@media(min-width: 1200px){
			margin-top: $theme-margin;
		}
	}
}
.tutor-course-info-fields,
.lp-course-info-fields{
	padding: 0;
	margin: 5px 0 0;
	@media(min-width: 1200px){
		margin-top: 20px;
	}
	color: $link-color;
	li{
		padding: 10px 0;
		@include flexbox();
		align-items: center;
		width: 100%;
		border-bottom: 1px solid $border-color;
	}
	i{
		margin-right: 10px;
	}
	.tutor-label,
	.lp-label{
		color: $body-color;
		margin-left: auto;
	}
	&.style2{
		margin:0;
		li:before{
			content: "\f00c";
			font-family: 'Font Awesome 5 Free';
			font-weight: 900;
			display: inline-block;
			margin-right: 12px;
			font-size: 12px;
		}
	}
	&.st_white{
		&,.lp-label,.tutor-label{
			color: #fff;
		}
		li{
			border-color: rgba(#fff,0.15);
		}
	}
}
.detail-course{
	@media(min-width: 1200px){
		> .row{
			> .col-lg-8{
				width: 70.5%;
				padding-right: 50px;
			}
			> .col-lg-4{
				width: 29.5%;
			}
		}
		.sidebar{
			margin-top: -360px;
		}
	}
}
#learn-press-course-tabs{
	&.course-tabs{
		margin: 0;
	}
}
#learn-press-course-tabs{
	ul.learn-press-nav-tabs{
		border:0;
		background: transparent;
	}
	.course-nav{
		border: 0;
		margin-right: 0.8rem;
		flex-grow: 0;
		&:last-child{
			margin-right: 0;
		}
		&:before{
			display: none !important;
		}
		label{
			display: inline-block;
			padding: .7rem 1.6rem;
			background: #ffffff;
			font-weight: 600;
			font-size: 14px;
			@include border-radius(.25rem);
			@include transition(all 0.3s ease-in-out 0s);
		}
		&.active{
			label{
				color: #fff !important;
				background: $theme-color !important;
			}
		}
	}
}
// tab profile
.learn-press-tabs{
	background: transparent;
	.learn-press-tabs__nav{
		border:0;
		li{
			border: 0;
			margin-right: 0.8rem;
			flex-grow: 0;
			background: transparent !important;
			&:last-child{
				margin-right: 0;
			}
			&:after,
			&:before{
				display: none !important;
			}
			label{
				overflow: hidden;
				display: inline-block;
				background: #ffffff;
				font-weight: 500;
				font-size: 14px;
				@include border-radius(.25rem);
				border: 1px solid $border-color;
				@include transition(all 0.3s ease-in-out 0s);
				a{
					color: inherit !important;
				}
			}
			&:hover,
			&.active{
				label{
					color: #fff !important;
					background: $theme-color !important;
					border-color: $theme-color !important;
				}
			}
		}
	}	
}

.course-top-wrapper{
	margin: 0 0 $theme-margin;
	.course-found{
		text-transform: capitalize;
		font-weight: 500;
		color: $headings-color;
		font-size: 14px;
	}
	label{
		color: $headings-color;
		font-size: 14px;
		margin-right: 20px;
		font-weight: 500;
	}
	select.orderby{
		cursor: pointer;
		color: var(--educrat-text-color);
		font-size: 14px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		background: url("../images/select.png") #EEF2F6 right 10px center no-repeat;
		padding: 10px 12px;
		margin: 0;
		min-width: 120px;
		border: 1px solid #EEF2F6;
		@include border-radius($border-radius);
	}
	.display-mode{
		margin-left: 7px;
		.change-view{
			@include border-radius($border-radius);
			width: 40px;
			height: 40px;
			@include flexbox();
			align-items: center;
			justify-content: center;
			color: $body-color;
			border: 1px solid $border-color;
			@include transition(all 0.3s ease-in-out 0s);
			&.active{
				color: $theme-color;
				border-color: $theme-color;
			}
			+ .change-view{
				margin-left: 7px;
			}
		}
	}
	.filter-top-btn,
	.filter-offcanvas-btn{
		i{
			margin-right: 10px;
		}
		margin-left: 20px;
		padding: 9px 20px;
		@include border-radius($border-radius);
		@include transition(all 0.3s ease-in-out 0s);
		color: $theme-color;
		background: rgb(229, 240, 253);
		display: inline-block;
		&:hover,&:focus{
			color: #fff;
			background: $theme-color;
		}
	}
}
.filter-offcanvas-sidebar{
	position: fixed;
	top: 0;
	right: 0;
	z-index: 8;
	background: #fff;
	padding: 20px;
	@media(min-width: 1200px){
		padding: $theme-padding;
	}
	@include transition(all 0.3s ease-in-out 0s);
	@include translateX(100%);
	@include opacity(0);
	@include size(320px,100%);
	max-width: 90%;
	scrollbar-width: thin;
	overflow-y: auto;
	&.active{
		@include translateX(0);
		@include opacity(1);
	}
}
.filter-offcanvas-sidebar-overlay{
	background: rgba(24, 24, 26, 0.7);
	z-index: 7;
	position: fixed;
	@include size(100%,100%);
	top: 0;
	left: 0;
	@include transition(all 0.3s ease-in-out 0s);
	visibility: hidden;
	@include opacity(0);
	cursor: pointer;
	&.active{
		visibility: visible;
		@include opacity(1);
	}
}
// course grid
.course-layout-item{
	@include transition(all 0.3s ease-in-out 0s);
	background: #fff;
	overflow: hidden;
	@include border-radius($border-radius);
	margin: 0 0 $theme-margin;
	.course-cover{
		overflow: hidden;
		@include border-radius($border-radius);
		img{
			@include transition(all 0.2s ease-in-out 0s);
		}
		figure{
			margin: 0;
		}
		.sale-label{
			line-height: 1;
			z-index: 2;
			display: inline-block;
			font-size: 11px;
			font-weight: 500;
			text-transform: uppercase;
			@include border-radius(50px);
			padding: 9px 20px;
			color: #fff;
			background: #E8543E;
			position: absolute;
			top: 10px;
			left: 10px;
		}
	}
	.post-thumbnail{
		display: block;
		position: relative;
		&:before{
			content:'';
			position: absolute;
			z-index: 1;
			top: 0;
			left: 0;
			@include size(100%,100%);
			background: #140342;
			@include opacity(0);
			@include transition(all 0.3s ease-in-out 0s);
		}
	}
	.course-layout-content{
		padding: 12px 0 0;
	}
	.course-title{
		text-transform: capitalize;
		font-size: 16px;
		@media(min-width: 1200px){
			font-size: 17px;
		}
		font-weight: 500;
		margin: 0 0 7px;
		display: -webkit-box;
  	-webkit-line-clamp: 2;
  	-webkit-box-orient: vertical;  
  	overflow: hidden;
	}
	.course-info-top{
		margin: 0 0 5px;
	}
	.course-meta-bottom{
		padding: 10px 0 0;
		margin-top: 8px;
		border-top: 1px solid $border-color;
	}
	.course-meta-middle{
		font-size: 14px;
		i{
			font-size: 1rem;
			margin-right: 5px;
			line-height: 1;
			vertical-align: middle;
			display: inline-block;
		}
		> *{
			display: inline-block;
			text-transform: capitalize;
			vertical-align: top;
			margin-right: 8px;
			@media(min-width: 1200px){
				margin-right: 15px;
			}
			&:last-child{
				margin-right: 0;
			}
		}
		.tutor-meta-value, .tutor-meta a{
			font-weight: 400;
			color: $body-color;
		}
	}
	.course-price{
		font-size: 16px;
		@media(min-width: 1200px){
			font-size: 18px;
		}
		color: $body-link;
		font-weight: 500;
		del,
		.origin-price{
			margin-right: 5px;
			text-decoration: line-through;
			font-size: $font-size-base;
			color: $body-color;
		}
	}
	.lp-button.button{
		line-height: 1;
		padding: 13px 25px;
		&:after{
			display: none;
		}
	}
	&:hover{
		.course-cover{
			img{
				@include scale(1.15);
			}
		}
		.post-thumbnail:before{
			@include opacity(0.5);
		}
	}
	.apus-wishlist-remove{
		background: #E8543E;
		color: #fff;
		@include size(25px,25px);
		line-height: 25px;
		font-size: 12px;
		text-align: center;
		display: inline-block;
		@include border-radius(4px);
		@include opacity(0.8);
		position: absolute;
		top: 15px;
		right: 15px;
		z-index: 1;
		&.loading{
      animation:rotate_icon 1500ms linear 0s normal none infinite running;
      -webkit-animation:rotate_icon 1500ms linear 0s normal none infinite running;
      i:before{
        content:'\f110';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
      }
    }
    &:hover,&:focus{
    	@include opacity(1);
    }
	}
}
// version 2
.course-grid-v2{
	.course-cover{
		@include border-radius($border-radius $border-radius 0 0);
	}
	.course-layout-content{
		padding: 12px 18px 10px;
		border-width: 0 1px 1px;
		border-style: solid;
		border-color: $border-color;
		@include border-radius(0 0 $border-radius $border-radius);
	}
	.course-meta-middle{
		i{
			margin-right: 2px;
		}
		> *{
			margin-right: 6px;
			&:last-child{
				margin-right: 0;
			}
		}
	}
}
// version 3
.course-grid-v3{
	border: 1px solid $border-color;
	@include border-radius($border-radius);
	padding: 10px;
	.course-layout-content{
		padding: 12px 10px 0;
	}
	.course-meta-middle{
		i{
			margin-right: 2px;
		}
		> *{
			margin-right: 8px;
			&:last-child{
				margin-right: 0;
			}
		}
	}
}
// version 4
.course-grid-v4{
	@include box-shadow(0 20px 30px 0 rgba(#19192E,0.04));
	@include border-radius($border-radius);
	.course-layout-content{
		padding: 12px 15px 10px;
		@media(min-width: 1200px){
			padding: 12px 30px 10px;
		}
	}
	.course-title{
		@media(min-width: 1200px){
			font-size: 18px;
		}
	}
}
// version list
.course-list{
	padding: 10px;
	border: 1px solid $border-color;
	@include border-radius($border-radius);
	.course-cover-thumb{
		width: 150px;
		@media(min-width: 1200px){
			width: 250px;
		}
	}
	.course-cover{
		+ .course-layout-content{
			padding: 0 0 0 20px;
		}
	}
}
.course-list-v2{
	padding: 0;
	border:0;
}
.course-list-v3{
	padding: 0;
	border:0;
	.course-cover-thumb{
		width: 200px;
		@media(min-width: 1200px){
			width: 260px;
		}
	}
	@media(min-width: 1200px){
		.course-cover{
			+ .course-layout-content{
				padding: 0 0 0 $theme-margin;
			}
		}
	}
	.course-price{
		font-size: 18px;
		@media(min-width: 1200px){
			font-size: 24px;
		}
		> *{
			display: block;
		}
		del,
		.origin-price{
			margin: 0;
		}
	}
	.course-meta-price{
		text-align: right;
		padding: 30px 0 30px 30px;
		border-left: 1px solid $border-color;
	}
	.save-bookmark-btn,
	.btn-wishlist-course{
		margin-left: $theme-margin / 2;
		@media(min-width: 1200px){
			margin-left: $theme-margin;
		}
	}
	.main-info{
		padding-right: $theme-padding / 2;
		@media(min-width: 1200px){
			padding-right: $theme-padding;
		}
	}
	.course-excerpt{
		font-size: 14px;
		margin: 0 0 15px;
	}
	.course-meta-bottom{
		border:0;
	}
	@media(max-width: 575px){
		.course-cover-thumb{
			width: 100%;
			margin: 0 0 ($theme-margin / 2) 0;
			@include border-radius($border-radius);
		}
		.course-layout-content{
			padding: 0 !important;
		}
		.course-price{
			> *{
				margin-left: 5px;
				display: inline-block;
			}
		}
		.course-meta-price{
			padding:5px 0 0;
			border:0;
			text-align: left;
		}
		.course-meta-bottom{
			padding: 0;
		}
	}
}
.wishlist-not-found{
	font-size: 1rem;
	text-transform: capitalize;
}
.save-bookmark-btn,
.btn-wishlist-course{
	display: inline-block;
	color: $theme-color;
	text-transform: capitalize;
	@include transition(all 0.3s ease-in-out 0s);
	i{
		font-size: 1rem;
		line-height: 1;
		font-weight: 400;
		display: inline-block;
		vertical-align: text-top;
	}
	.wishlist-text{
		margin-left: 10px;
		display: inline-block;
		font-size: $font-size-base;
		font-weight: 500;
	}
	&:hover,&:focus{
		.wishlist-text{
			text-decoration: underline;
		}
	}
  &.apus-wishlist-added i:before{
  	content:'\f00d';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }
  &.is-loading i,
  &.loading i{
    animation:rotate_icon 1500ms linear 0s normal none infinite running;
    -webkit-animation:rotate_icon 1500ms linear 0s normal none infinite running;
    &:before{
      content:'\f110';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
    }
  }
	&.only-icon{
		@include size(50px,50px);
		line-height: 50px;
		text-align: center;
		@include border-radius(50%);
		background: #EEF2F6;
		color: #6A7A99;
		&.apus-wishlist-added,
		&:hover{
			background: $theme-color;
			&,i{
				color: #fff !important;
			}
		}
		i.tutor-icon-bookmark-bold{
			color: $theme-color;
		}
	}
}
// archive course
.sidebar-course{
	.widget_apus_course_filter_keywords{
		border:0;
		padding: 0;
	}
	@media(min-width: 1320px){
		.sidebar-left{
			padding-right: $theme-margin;
		}
		.sidebar-right{
			padding-left: $theme-margin;
		}
	}
}
.sidebar-course-single {
	.close-sidebar-btn{
		display: none !important;
	}
	.sidebar{
		.widget{
			border: 0;
		}
	}
}
.course-list-check{
	list-style: none;
	padding: 0;
	margin: 0;
	label{
		cursor: pointer;
		font-weight: 400;
		width: 100%;
		padding-left: 25px;
		@include flexbox();
		position: relative;
		color: $body-link;
		&:before{
			position: absolute;
			top: 5px;
			left: 0;
			@include size(15px,15px);
			border: 2px solid #6A7A99;
			background: #fff;
			content: '';
			@include border-radius(0);
		}
		&:after{
			content: "\f00c";
			font-family: 'Font Awesome 5 Free';
			position: absolute;
			left: 4px;
			top: 5px;
			font-size: 8px;
			color: #fff;
			font-weight: 900;
			@include opacity(0);
		}
		.count{
			margin-left: auto;
		}
	}
	> li{
		margin: 0 0 6px;
		&:last-child{
			margin-bottom: 0;
		}
	}
	input:checked + label{
		&:before{
			border-color: #1A064F;
			background: #1A064F;
		}
		&:after{
			@include opacity(1);
		}
	}
	.review-stars-rated-wrapper{
		margin-top: 2px;
	}
}
.rating-list{
	label{
		&:before{
			@include border-radius(50%);
			background: transparent !important;
		}
		&:after{
			content:'';
			@include size(7px,7px);
			@include border-radius(50%);
			background: #1A064F;
			top: 9px;
		}
	}
}
.learn-press-progress .learn-press-progress__active{
	background: $theme-color;
}
.course-curriculum .section-header .learn-press-progress{
	width: 100%;
	height: 4px;
}
.learn-press-progress::before {
	background: #e5e5e5;
}
#popup-course #popup-content #learn-press-quiz-app .questions-pagination .nav-links .page-numbers{
	@include border-radius($border-radius);
	border: 1px solid $theme-color;
	color: $theme-color;
	background: #fff;
	@include transition(all 0.3s ease-in-out 0s);
	font-weight: 600;
	font-size: 15px;
	margin: 0 3px;
	&.current,
	&:hover,&:focus{
		color: #fff;
		background: $theme-color;
	}
}
#popup-course #popup-footer .course-item-nav .prev, 
#popup-course #popup-footer .course-item-nav .next{
	&:before{
		color: $theme-color !important;
	}
	a{
		font-weight: 600;
		color: $theme-color !important;
	}
}
#popup-course #popup-footer .course-item-nav .course-item-nav__name{
	@include border-radius($border-radius);
	background: $theme-color;
	color: #fff;
	font-weight: 600;
}
.quiz-status .countdown{
	padding: 17px 30px;
	@include border-radius($border-radius);
}
#popup-course #popup-content .lp-button.instant-check.loading .instant-check__icon{
	display: none;
}
.answer-options .answer-option input[type="checkbox"]::after, 
.answer-options .answer-option input[type="radio"]::after {
	top: 12px;
}
.quiz-result .result-statistic .result-statistic-field p{
	color: $body-link;
}
#popup-course .quiz-result .result-grade svg circle{
	stroke: $theme-color;
}
#popup-course #sidebar-toggle::before {
	color: $theme-color;
}
.learnpress-page .lp-modal-footer{
	text-align: center;
	padding: 20px;
	.lp-button{
		margin: 0 5px;
		padding:10px 25px;
	}
}
#popup-course .quiz-questions .lp-fib-content .lp-fib-input > input{
	border: 1px solid $border-color;
	@include border-radius($border-radius);
	margin: 0 2px;
}
.lp-modal-dialog .lp-modal-content .lp-modal-body .main-content{
	padding: $theme-padding / 2;
}
.lp-modal-dialog .lp-modal-content .lp-modal-header h3{
	text-align: center;
	font-size: 18px;
}
.content-item-summary .form-button-finish-course{
	margin: $theme-margin 0 0;
}
#popup-course #popup-content #learn-press-content-item .content-item-wrap{
	clear: both;
	overflow: hidden;
}
#popup-course #popup-header .popup-header__inner .course-title a{
	font-size: 18px;	
}
.apus-lp-content-area{
	ul.curriculum-sections .section-header .section-meta{
		display: none !important;
	}
}
.learn-press-profile-course__tab__inner{
	background: transparent;
	> li{
		margin-right: 15px !important;
		&:last-child{
			margin-right: 0 !important;
		}
		a{
			border: 1px solid $border-color;
			display: inline-block;
			padding: .7rem 1.6rem;
			background: #ffffff;
			font-weight: 500;
			font-size: 14px;
			@include border-radius($border-radius);
			@include transition(all 0.3s ease-in-out 0s);
			&.active{
				color: #fff;
				background: $theme-color;
				border-color: $theme-color;
			}
		}
	}
}
#learn-press-profile{
	background: transparent;
	@media(min-width: 1200px){
		margin-bottom: $theme-margin;
	}
}
.learn-press-course-tab-filters .learn-press-filters a{
	cursor: pointer;
	font-weight: 500;
	&.active{
		color: $theme-color;
	}
}
#learn-press-profile .dashboard-general-statistic__row .statistic-box{
	background: var(--educrat-theme-color-007) !important;
	border:1px solid var(--educrat-theme-color-010);
	color: $theme-color !important;
	.statistic-box__text,
	.statistic-box__number{
		color: inherit;
	}
}
.lp-content-area{
  padding: 0;
}
.lp-user-profile #profile-sidebar{
	border:0;
	margin: 0;
}
#learn-press-profile #profile-nav .lp-profile-nav-tabs li{
	@include transition(all 0.2s ease-in-out 0s);
	border: 0;
	margin: 0 0 10px;
	background: #fff !important;
	&:last-child{
		margin-bottom: 0;
	}
	a{
		@include border-radius($border-radius);
		background: var(--educrat-theme-color-007);
		color: $theme-color;
	}
	&:hover,
	&.active{
		> a{
			background: $theme-color;
			color: #fff;
		}
	}
	> ul{
		background: #fff;
		padding: 15px;
		@include border-radius($border-radius);
		margin-top: -15px;
	}
	&.active{
		> ul{
			@include box-shadow(none);
			margin: 10px 0 0;
			background: transparent;
			padding: 0;
		}
	}
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs > li > a:after,
.lp-user-profile #profile-nav .lp-profile-nav-tabs > li > a > i{
	color: inherit !important;
}
.lp-user-profile #profile-nav .lp-profile-nav-tabs > li ul li a{
	border:0;
	white-space: nowrap;
	i{
		font-size: 12px;
		margin-right: 3px;
		background: transparent !important;
		color: inherit !important;
	}
}
.lp-archive-courses{
	.apus-lp-content-area{
		display: block;
	}
}
.course-single-tab{
	background: #fff;
	border-bottom: 2px solid $border-color !important;
	margin:0 0 $theme-margin;
	@media(min-width: 1200px){
		margin-bottom: 50px;
	}
	list-style: none !important;
	> li{
		margin: 0 0 -2px;
		line-height: inherit;
		+ li{
			margin-left: 15px;
			@media(min-width: 1200px){
				margin-left: 38px;
			}
		}
		> .nav-link,
		> .tutor-nav-link,
		> label,
		> a{
			background: transparent !important;
			color: $body-color;
			font-size: $font-size-base;
			padding: 0 0 15px;
			margin: 0;
			border: 0 !important;
			@include border-radius(0);
			position: relative;
			cursor: pointer;
			&:before{
				content:'';
				position: absolute;
				bottom:0;
				left:0;
				background: $theme-color;
				@include size(0,2px);
				@include transition(all 0.3s ease-in-out 0s);
			}
			&:hover,
			&:focus,
			&.is-active,
			&.active{
				color: $theme-color;
				&:before{
					width: 100%;
				}
			}
		}
		&:hover,
		&.active{
			> .tutor-nav-link,
			> .tutor-nav-link.is-active,
			> .nav-link,
			> .nav-link.active,
			> label,
			> a.active,
			> a{
				color: $theme-color;
				&:before{
					width: 100%;
				}
			}
		}
	}
}

.learn-press-filters > li{
	span,a{
		font-weight: 500;
	}
	span{
		color: $theme-color;
	}
}
.learn-press-profile-course__tab__inner a.active::before {
	display: none;
}
#learn-press-profile .wrapper-profile-header{
	color: $body-color;
	background: transparent;
	margin:0;
}
#learn-press-profile .wrapper-profile-header .lp-profile-right .lp-profile-username{
	font-weight: 500;
	color: $body-link;
	@media(min-width: 1200px){
		font-size: 22px;
	}
	&:after,
	&:before{
		display: none;
	}
}
#learn-press-profile .wrapper-profile-header .lp-profile-content-area{
	background: #fff;
	@include border-radius($border-radius);
	padding: 20px;
	margin: 20px 0;
	border: 1px solid $border-color;
	@include box-shadow(0 25px 70px 0 rgba(#01213A,0.07));
	@media(min-width: 1200px){
		margin-bottom: 50px;
	}
	display: block;
	@media(min-width: 990px){
		@include flexbox();
		align-items: center;
	}
	min-height:0;
}
#learn-press-profile .wrapper-profile-header .lp-profile-left{
	position: static;
	width: 100%;
	@media(min-width: 990px){
		width: 200px;
	}
	padding: 0;
	border:0;
}
.lp-user-profile-avatar{
	@include border-radius($border-radius);
	overflow: hidden;
}
#learn-press-profile .wrapper-profile-header .lp-profile-right{
	margin: 0;
	width: 100%;
	@media(max-width: 989px){
		padding:15px 0 0;
	}
	@media(min-width: 990px){
		width: calc(100% - 200px);
	}
}
#learn-press-profile .lp-user-profile-socials a{
	@include size(40px,40px);
	line-height: 40px;
	border:0;
	color: $theme-color;
	background: var(--educrat-theme-color-007);
	font-size: $font-size-base;
	&:focus,&:hover{
		background: $theme-color;
		color: #fff;
	}
}
#learn-press-profile .lp-user-profile-socials{
	margin: 20px 0 0;
}
#learn-press-profile #profile-content{
	padding-top: 0;
}
@media(max-width: 990px){
	#learn-press-profile #profile-sidebar{
		margin:0 0 $theme-margin;
	}
	#learn-press-profile .dashboard-general-statistic__row .statistic-box{
		margin-bottom: 15px;
	}
}
div.order-recover input[type="text"]{
	border:1px solid $border-color;
	@include border-radius($border-radius);
	outline: none;
	height: 50px;
	&:focus{
		border-color: $theme-color;
	}
}
.order-comments{
	border:1px solid $border-color;
	@include border-radius($border-radius);
	outline: none;
	min-height: 145px;
	resize: none;
	padding: 10px 20px;
	&:focus{
		border-color: $theme-color;
	}
}
#checkout-order .lp-checkout-order__inner{
	border-color: $border-color;
}
#checkout-order .lp-checkout-order__inner .course-name a:hover{
	color: $theme-color;
}
#learn-press-checkout-form{
	margin: 0 0 $theme-margin;
	@media(min-width: 816px){
		.lp-checkout-form__after,
		.lp-checkout-form__before{
			margin:0;
			width: calc(50% - 15px);
		}
	}
}
.nav-tabs-account{
	justify-content: center;
	margin: ($theme-margin / 2) 0;
	@media(min-width: 1200px){
		margin: $theme-margin 0;
	}
	li{
		margin: 0 10px;
		button{
			border: 0;
			display: inline-block;
			padding: .7rem 2rem;
			background: #ffffff;
			font-weight: 500;
			font-size: 1rem;
			@include border-radius($border-radius);
			@include transition(all 0.3s ease-in-out 0s);
			&.active{
				color: #fff;
				background: $theme-color;
			}
		}
	}
}
.learn-press-form-login, 
.learn-press-form-register{
	@include box-shadow(none !important);
	border: 1px solid $border-color;
	background: #fff;
	@media(max-width: 1199px){
		padding: 20px;
		margin-bottom: $theme-margin;
	}
	.form-fields .form-field{
		input[type="url"],
		input[type="tel"],
		input[type="number"],
		input[type="password"],
		input[type="text"]{
			@include border-radius($border-radius);
			border:1px solid $border-color;
			&:focus{
				border-color: $theme-color;
				outline: none !important;
				@include box-shadow(none !important);
			}
			@media(min-width: 1200px){
				height: 50px;
				padding: 10px 20px;
			}
		}
	}
	button[type="submit"]{
		border: 2px solid $theme-color;
		color: #fff;
		background: $theme-color;
		@include border-radius($border-radius);
		font-weight: 500;
		&:hover,&:focus{
			background: #fff;
			border-color: $theme-color;
			color: $theme-color;
		}
	}
	@media(min-width: 1200px){
		.lp-password-input .lp-show-password-input{
			top: 12px;
		}
	}
	h3{
		margin: 0 0 20px;
		@media(max-width: 1199px){
			font-size: 23px;
		}
	}
}
// widget course
.widget-courses{
	.slick-carousel{
		.slick-dots{
			padding: 10px 0 0;
		}
		&.stretch_pagination{
			.slick-prev{
				@media(min-width: 1400px){
		      left: -65px;
		    }
			}
			.slick-next{
				@media(min-width: 1400px){
		      right: -65px;
		    }
			}
		}
	}
	&.fullscreen{
		@media(min-width: 1350px){
			.slick-list{
				overflow: visible;
			}
		}
		@media (min-width: 1400px){
			.slick-carousel .slick-next {
			  right: -10px;
			}
			.slick-carousel .slick-prev {
			  left: -10px;
			}
		}
	}
}
// widget instructors
.instructor-grid-inside{
	margin-bottom: $theme-padding / 2;
	@media(min-width: 1200px){
		margin-bottom: $theme-padding;
	}
	.instructor-name{
		font-weight: 500;
		font-size: 17px;
		margin: 0 0 5px;
	}
	.instructor-bottom{
		margin-top: 5px;
	}
	.instructor-bottom{
		[class*="flaticon"]{
			font-size: 1rem;
			line-height: 1;
			margin-right: 5px;
			vertical-align: middle;
		}
		> *{
			margin-right: 15px;
			&:last-child{
				margin-right: 0;
			}
		}
	}
	.socials{
		text-align: center;
		font-size: 1rem;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 0;
		width: 100%;
		@include opacity(0);
		@include translateY(0);
		@include transition(all 0.3s ease-in-out 0s);
		a{
			margin: 0 7px;
			@media(min-width: 1200px){
				margin: 0 12px;
			}
			color: #fff !important;
		}
	}
	.cover-inner{
		display: block;
		overflow: hidden;
		@include border-radius($border-radius);
		margin-bottom: 23px;
		&:before{
			z-index: 1;
			top: 0;
			left: 0;
			position: absolute;
			content:'';
			@include size(100%,100%);
			background: #1A064F;
			@include opacity(0);
			@include transition(all 0.3s ease-in-out 0s);
		}
		img{
			width: 100%;
		}
	}
	&:hover{
		.cover-inner:before{
			@include opacity(0.6);
		}
		.socials{
			@include opacity(1);
			@include translateY(-50%);
		}
	}
}
.instructor-grid-inside-v2{
	text-align: center;
	border:1px solid $border-color;
	@include border-radius($border-radius);
	@include transition(all 0.3s ease-in-out 0s);
	background: #fff;
	padding: $theme-padding / 2;
	margin-bottom: $theme-padding / 2;
	@media(min-width: 1200px){
		padding: $theme-padding;
		margin-bottom: $theme-padding;
	}
	@include box-shadow(0 6px 15px 0 rgba(#404F68,0.05));
	.instructor-name{
		font-weight: 500;
		font-size: 17px;
		margin: 0 0 3px;
	}
	.socials{
		margin: 5px 0 0;
		font-size: 13px;
		a{
			margin: 0 5px;
			@media(min-width: 1200px){
				margin: 0 10px;
			}
			color: #6A7A99;
			&:hover,&:focus{
				color: $theme-color;
			}
		}
	}
	.cover-inner{
		display: block;
		overflow: hidden;
		@include border-radius(50%);
		margin:0 auto 20px;
		@include size(90px,90px);
	}
	.btn{
		margin: 15px 0 0;
		width: 100%;
		font-weight: 400;
		font-size: $font-size-base;
		@include border-radius(50px);
		padding: 10px 20px;
	}
	&:hover{
		@include box-shadow(0 40px 30px 0 rgba(#19192E,0.04));
		.btn{
			color: #fff;
			background: $theme-color;
			border-color: $theme-color;
		}
	}
}
.widget-instructors{
	&.p-left{
		.slick-carousel .slick-dots{
			text-align: left;
			padding-left: 10px;
		}
	}
	&.p-right{
		.slick-carousel .slick-dots{
			text-align: right;
			padding-right: 10px;
		}
	}
	&.fullscreen{
		@media(min-width: 1350px){
			.slick-list{
				overflow: visible;
			}
		}
	}
}
// widget search
.search-form-course{
	position: relative;
	padding: 10px;
	background: #fff;
	@include border-radius($border-radius);
	.btn-search{
		margin: 0 !important;
		i{
			font-size: 20px;
			line-height: 1;
			vertical-align: middle;
			+ .text-search{
				display: inline-block;
				margin-left: 12px;
				vertical-align: middle;
			}
		}
	}
	.form-control{
		border-color: #fff;
	}
	select{
		border: 0;
		background: #fff;
		padding: 10px 0;
		color: $body-color;
	}
	.addon{
		@media(min-width: 576px){
			width: 52%;
			padding: 0 $theme-padding;
		}
		> *{
			width: 100%;
			flex-grow: 1;
			padding: 0 $theme-padding;
			border-left: 1px solid $border-color;
			@media(max-width: 575px){
				margin: 0 18px 10px;
				padding: 0;
				border-left: 0;
				width: auto;
				border-bottom: 1px solid $border-color;
				&:last-child{
					border-bottom: 0;
				}
				select{
					width: 100%;
				}
			}
		}
	}
	&.button{
		padding: 0 0 0 18px;
		border-bottom: 2px solid $border-color;
		@include border-radius(0);
		.btn-search{
			position: absolute;
			top: 50%;
			left: 0;
			@include translateY(-50%);
		}
		.form-control{
			font-size: 1rem;
			font-weight: 500;
			color: $body-link;
			@media(min-width: 1200px){
				height: 60px;
				font-size: 18px;
			}
			&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
		      @include opacity(1);
		      color: $body-link;
		  }
		  &::-moz-placeholder { /* Firefox 19+ */
		    @include opacity(1);
		    color: $body-link;
		  }
		  &:-ms-input-placeholder { /* IE 10+ */
		    @include opacity(1);
		    color: $body-link;
		  }
		  &:-moz-placeholder { /* Firefox 18- */
		    @include opacity(1);
		    color: $body-link;
		  }
		}
	}
}
.apus-search-form-course{
	.search-button{
		font-size: 20px;
		line-height: 1;
		display: inline-block;
		vertical-align: middle;
		cursor: pointer;
	}
	&.button{
		.form-inner{
			max-width: 1320px;
			padding-left: $theme-padding / 2;
			padding-right: $theme-padding / 2;
			margin-left: auto;
			margin-right: auto;
		}
		.search-form-popup{
			display: none;
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			z-index: 7;
			background: #fff;
			padding: $theme-padding 0;
			width: 100% !important;
			@media(min-width: 1200px){
				padding: 60px 0 80px;
			}
		}
	}
	.close-search{
		display: inline-block;
		line-height: 40px;
		text-align: center;
		@include size(40px,40px);
		@include border-radius(50%);
		font-size: 12px;
		color: $theme-color;
		background: var(--educrat-theme-color-007);
		@include transition(all 0.2s ease-in-out 0s);
		cursor: pointer;
		i:before{
			font-weight: 700;
		}
		&:hover,&:focus{
			color: #fff;
			background: $theme-color;
		}
	}
}
#checkout-account-guest{
	#guest_email{
		outline: none !important;
		padding: 5px 20px;
		height: 40px;
		@include border-radius($border-radius);
		border: 1px solid $border-color;
		&:focus{
			border-color: $theme-color;
		}
	}
}
#learn-press-profile #profile-content .lp-button,
#learn-press-profile #profile-content .lp-archive-courses ul.learn-press-courses .course{
	margin: 0;
}

// event simple
.event-item{
	margin-bottom: $theme-margin / 2;
	@media(min-width: 1200px){
		margin-bottom: $theme-margin;
	}
	.entry-thumb{
		@include border-radius($border-radius);
		overflow: hidden;
	}
	.entry-title{
		font-weight: 500;
		font-size: 17px;
		margin: 0 0 5px;
		display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
	}
	.inner-left{
		padding-right: 7px;
	}
	.event-metas{
		position: relative;
		z-index: 1;
		margin: -45px 10px 0;
		padding: 20px;
		background: #fff;
		@include border-radius($border-radius);
		@include box-shadow(0 6px 15px 0 rgba(#404F68,0.05));
		i{
			font-size: 1rem;
			margin-right: 5px;
		}
	}
	.event-address{
		margin-left: 10px;
	}
	.btn{
		font-size: 15px;
		padding: 5px 30px;
		@include border-radius(40px);
	}
	&:hover{
		.btn{
			color: #fff;
			background: $theme-color;
			border-color: $theme-color;
		}
	}
}
.event-grid{
	background: #fff;
	@include border-radius($border-radius);
	@include box-shadow(0 25px 60px 0 rgba(#01213A,0.07));
	@include transition(all 0.3s ease-in-out 0s);
	margin-bottom: $theme-margin / 2;
	@media(min-width: 1200px){
		margin-bottom: $theme-margin;
	}
	.icon-space{
		font-size: 1rem;
		margin-right: 5px;
	}
	.entry-title{
		font-weight: 500;
		font-size: 17px;
		margin: 8px 0;
		display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
	}
	.inner{
		padding: 10px 20px;
		@media(min-width: 1200px){
			padding: 20px 30px;
		}
		border-width: 0 1px 1px;
		border-style: solid;
		border-color: $border-color;
		@include border-radius(0 0 $border-radius $border-radius);
	}
	.entry-thumb{
		margin: 0;
		overflow: hidden;
		@include border-radius( $border-radius $border-radius 0 0);
	}
	&:hover{
		@include box-shadow(0 25px 60px 0 rgba(#01213A,0.15));
	}
}

.event-grid-v2{
	@include transition(all 0.3s ease-in-out 0s);
	background: #F7F8FB;
	@include border-radius($border-radius);
	overflow: hidden;
	height: 200px;
	@media(min-width: 1200px){
		height: 350px;
	}
	.startdate{
		line-height: 1;
		position: absolute;
		top: 20px;
		left: 20px;
		z-index: 1;
		@media(min-width: 1200px){
			top: 50px;
			left: 50px;
		}
	}
	.day{
		@include transition(all 0.3s ease-in-out 0s);
		font-weight: 700;
		font-size: 35px;
		color: $body-link;
		@media(min-width: 1200px){
			font-size: 45px;
		}
	}
	.month{
		text-transform: uppercase;
		@include transition(all 0.3s ease-in-out 0s);
		font-size: 18px;
		font-weight: 500;
		margin-left: 15px;
	}
	.entry-title{
		@include transition(all 0.3s ease-in-out 0s);
		@include translateY(70px);
		@media(min-width: 1200px){
			font-size: 24px;
			line-height: 35px;
		}
		margin: 0 0 15px;
		display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
	}
	.btn{
		padding: 8px 25px;
		font-weight: 500;
		font-size: $font-size-base;
		@include opacity(0);
		@include translateY(50px);
		i{
			margin-left: 10px;
		}
	}
	.event-metas{
		padding: 20px;
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		@media(min-width: 1200px){
			padding: 40px;
		}
	}
	&:hover{
		background: #282664;
		color: #fff;
		.month,
		.entry-title a,
		.day{
			color: #fff;
		}
		.entry-title{
			@include translateY(0);
		}
		.btn{
			@include opacity(1);
			@include translateY(0);
		}
	}
}
.event-list{
	background: #fff;
	border: 1px solid #EDEDED;
	padding: 10px;
	@include border-radius($border-radius);
	@include box-shadow(0 25px 70px 0 rgba(#01213A,0.07));
	margin-bottom: $theme-margin / 2;
	@media(min-width: 1200px){
		margin-bottom: $theme-margin;
	}
	.post-thumbnail{
		width: 120px;
		overflow: hidden;
		display: block;
		@include border-radius($border-radius);
	}
	.entry-title{
		font-size: 17px;
		margin: 0 0 3px;
		display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
	}
	.inner-right{
		padding-left: 15px;
		@media(min-width: 1200px){
			padding-left: 38px;
		}
		i{
			margin-right: 5px;
		}
	}
	.event-address{
		margin-left: 12px;
	}
	.btn{
		white-space: nowrap;
		padding: 8px 25px;
		font-weight: 500;
		font-size: $font-size-base;
		@include opacity(0);
		i{
			margin-left: 10px;
		}
	}
	.more{
		padding: 0 10px;
	}
	&:hover{
		.btn{
			@include opacity(1);
		}
	}
}
.event-list-small{
	margin-bottom: 20px;
	.entry-thumb{
		margin: 0;
		width: 65px;
		@include border-radius($border-radius);
		overflow: hidden;
	}
	.entry-title{
		font-size: 15px;
		font-weight: 500;
		margin: 0 0 3px;
		display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
	}
	.startdate{
		font-size: 13px;
	}
	.inner-right{
		padding-left: 15px;
	}
}
.event-list-v2{
	background: #fff;
	padding: 20px;
	@include border-radius($border-radius);
	@include box-shadow(0 10px 30px 0 rgba(#01213A,0.05));
	@include transition(all 0.3s ease-in-out 0s);
	@media(min-width: 1200px){
		margin-bottom: $theme-margin;
		@include box-shadow(0 25px 60px 0 rgba(#01213A,0.07));
	}
	.entry-title{
		font-size: 17px;
		margin: 0;
		display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
	}
	.startdate{
		padding: 10px 15px;
		line-height: 1.3;
		text-align: center;
		display: ilnine-block;
		@include border-radius($border-radius);
		color: #fff;
		background: $body-link;
		font-size: 15px;
		text-transform: uppercase;
		font-weight: 500; 
		.day{
			font-size: 17px;
		}
	}
	.inner-right{
		padding-left: 20px;
	}
	.event-address{
		line-height: 1;
		font-size: 14px;
		margin-top: 18px;
		i{
			margin-right: 5px;
			font-size: 1rem;
		}
	}
	&:hover{
		@include box-shadow(0 25px 60px 0 rgba(#01213A,0.12));
	}
}
.event-header{
	margin-bottom: $theme-margin;
	.orderby{
		@media(min-width: 576px){
			margin-left: auto;
		}
		label{
			font-weight: 500;
			font-size: 14px;
			color: $body-link;
			margin-right: 15px;
		}
	}
	select.orderby{
			cursor: pointer;
			color: $body-color;
			font-size: 14px;
      width: 100%;
      padding: 8px 50px 8px 15px;
      height: 50px;
      border: 0;
      @include border-radius($border-radius);
      -webkit-appearance: none;
      -moz-appearance: none;
      -o-appearance: none;
      background: url("#{$image-theme-path}select.png") #EEF2F6 right 15px center no-repeat;
  }
  .results-count{
  	color: $body-link;
  }
}
.header-single-envent{
	max-width: 1500px;
	margin-left: auto;
	margin-right: auto;
	background-color: $body-link;
	overflow: hidden;
	color: #fff;
	padding: $theme-padding 0;
	margin-bottom: $theme-margin;
	position: relative;
	z-index: 1;
	@media(min-width: 1200px){
		padding: 140px 0;
		@include border-radius($border-radius);
	}
	&:before{
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		@include size(100%,100%);
		background: rgba(#140342,0.7);
		z-index: -1;
	}
	.entry-title{
		color: #fff;
		font-size: 25px;
		@media(min-width: 1200px){
			font-size: 30px;
		}
		margin: 15px 0 10px;
	}
	.detail-time-location{
		font-size: 14px;
		i{
			font-size: 1rem;
			margin-right: 5px;
		}
		.event-address{
			margin-left: 12px;
		}
	}
}
.box-event-detail{
	max-width: 850px;
}
.single-envent-content{
	@media(min-width: 1350px){
		#main-content.col-lg-9{
			width: 70%;
		}
		.sidebar-wrapper.col-lg-3{
			width: 30%;
		}
	}
	@media(min-width: 1200px){
		.sidebar-wrapper {
			margin-top: -100px;
			z-index: 1;
			position: relative;
		}
	}
}
.apus_event_product{
	text-align: center;
	padding: 20px;
	@media(min-width: 1200px){
		padding: $theme-padding;
	}
	background: #fff;
	border: 1px solid $border-color;
	@include border-radius($border-radius);
	@include box-shadow(0 20px 30px 0 rgba(#19192E,0.04));
	.event-meta-price{
		margin-bottom: 15px;
		font-size: 24px;
		font-weight: 500;
		color: $body-link;
		del{
			font-size: $font-size-base;
			color: $body-color;
		}
	}
	.btn{
		width: 100%;
	}
	.apus-social-share{
		margin-top: 20px;
		strong{
			display: none;
		}
	}
}
.envent-participant{
	margin-top: $theme-margin;
	.heading{
		font-size: 20px;
		font-weight: 500;
		margin: 0 0 $theme-margin;
	}
}
.participant-item{
	text-align: center;
	.image{
		@include size(180px,180px);
		@include border-radius(50%);
		overflow: hidden;
		margin: 0 auto 20px;
	}
	.name{
		font-size: 17px;
		font-weight: 500;
		margin: 0;
	}
	.job{
		margin: 5px 0 0;
	}
}
.widget-events{
	@media(min-width: 1350px){
		.slick-list{
			overflow: visible;
		}
	}
	&.nofullscreen{
		.slick-list{
			overflow: hidden;
		}
		@media(min-width: 1350px){
			margin-left: -15px;
			margin-right: -15px;
			.slick-list{
				padding: 0 15px;
			}
		}
		@media(min-width: 1200px){
			.simple_event{
				margin-bottom: 60px;
			}
			.slick-dots{
				padding: 0;
			}
		}
	}
	&.p-left{
		.slick-carousel .slick-dots{
			text-align: left;
			padding-left: 10px;
		}
	}
	&.p-right{
		.slick-carousel .slick-dots{
			text-align: right;
			padding-right: 10px;
		}
	}
	.item:not(.slick-active) .event-list-v2{
		@include box-shadow(none);
	}
}
.filter-top-sidebar{
	padding-bottom: 10px;
	> *{
		margin: 0 0 15px;
		@media(min-width: 576px){
			display: inline-block;
			vertical-align: top;
			margin: 0 20px 20px 0;
		}
	}
	select{
		cursor: pointer;
		color: var(--educrat-text-color);
		font-size: 14px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		background: url("../images/select.png") #EEF2F6 right 10px center no-repeat;
		padding: 11px 12px;
		margin: 0;
		min-width: 125px;
		border: 1px solid #EEF2F6;
		@include border-radius($border-radius);
		@media(max-width: 575px){
			width: 100%;
		}
	}
}
.tutor-color-secondary,
.tutor-wrap{
	color: $body-color;
}
.tutor-color-black{
	color: $body-link;
}

.tutor-accordion{
	.tutor-accordion-item{
		cursor: pointer;
		border:1px solid $border-color;
		@include border-radius($border-radius);
		background: #F7F8FB !important;
		overflow: hidden;
		+ .tutor-accordion-item{
			margin-top: 10px;
		}
		.section-desc{
			color: $body-color;
			font-style: normal;
			margin: 10px 0 0;
		}
		&:not(.closed){
			.section-toggle:before{
				content: "\e648" !important;
			}
		}
	}
	.tutor-accordion-item-header{
		height: auto !important;
		border: 0;
		padding: .75rem 1.25rem !important;
		line-height: 1;
		@media(min-width: 1200px){
			padding: 21px $theme-margin !important;
		}
		font-size: 1rem;
		font-weight: 500;
		color: $headings-color;
		background: #F7F8FB !important;
		&:after{
			font-size: 13px;
			color: inherit;
			@include transform(translateY(-50%) rotate(90deg));
			right: 1.25rem;
			@media(min-width: 1200px){
				right: $theme-margin ;
			}
		}
		&.is-active{
			color: $theme-color;
			&:after{
				@include transform(translateY(-50%) rotate(-90deg));
			}
		}
	}
	.tutor-accordion-item-body{
		margin: 0;
		background: #fff;
		.tutor-accordion-item-body-content{
			border: 0;
			padding: 1rem 1.25rem;
			@media(min-width: 1200px){
				padding: 25px $theme-margin;
			}
		}
		.tutor-course-content-list-item-icon{
			font-size: 11px;
			color: $theme-color;
			background: var(--educrat-theme-color-007);
			@include size(25px,25px);
			min-width: 25px;
			line-height: 25px;
			text-align: center;
			@include border-radius(50%);
			margin: 0;
		}
		.tutor-course-content-list{
			margin: 0;
		}
		.tutor-course-content-list-item{
			margin: 0 0 20px;
			padding: 0;
			background: transparent !important;
			&:last-child{
				margin-bottom: 0;
			}
			.tutor-course-content-list-item-status{
				color: $success;
			}
			.tutor-icon-lock-line{
				color: $danger;
			}
			.tutor-course-content-list-item-title{
				font-size: $font-size-base;
				font-weight: 400;
				@include transition(all 0.3s ease-in-out 0s);
				padding: 0 12px;
				@include transition(all 0.2s ease-in-out 0s);
			}
			&:hover{
				.tutor-course-content-list-item-title{
					color: $theme-color;
				}
			}
		}
	}
}
.comment-list {
	.tutor-ratings-stars {
		font-size: 11px;
		margin: 5px 0 0;
	}
	.tutor-avatar{
		@include box-shadow(none);
		@include size(100%,100%);
	}
}
.comment-form-theme{
	.tutor-ratings-stars{
		font-size: $font-size-base;
	}
}
.widget-courses-related{
	margin: $theme-margin 0 0;
	@media(min-width: 1200px){
		margin-top: 60px;
	}
	.course-layout-item{
		background: transparent;
	}
}
// filter
.tutor-widget-search{
	.tutor-form-control{
		color: var(--educrat-text-color);
		font-size: .9375rem;
		height: 50px;
		padding: .6rem 1.29rem;
		border:1px solid $border-color;
		@include border-radius($border-radius);
		&:focus{
			border-color: $input-focus-border-color;
		}
	}
	.tutor-icon-search {
		color: $body-link;
		left: 2px;
		&:focus,&:hover{
			color: $theme-color;
		}
	}
}
.tutor-list-item{
	font-size: $font-size-base;
	color: $body-link;
	&:not(:last-child){
		margin-bottom: 6px;
	}
	.tutor-form-check-input{
		@include size(15px,15px);
		margin: 0 10px 0 0 !important;
		border: 2px solid #6A7A99;
		@include border-radius(0);
		@include box-shadow(none);
		&:checked{
			background-color: $body-link;
			border-color: $body-link;
			background-size: 10px;
		}
	}
}
.tutor-form-control{
	font-size: $font-size-base;
	padding: 13px 20px;
}
.tutor-form-select{
	padding: 13px 40px 13px 20px;
	&.is-active{
		border-color: $body-link;
	}
}
.tutor-filter-top-sidebar{
	margin-bottom: $theme-margin;
	width: 100% !important;
	display: none;
	.tutor-form{
		margin-left: -($theme-margin / 2);
		margin-right: -($theme-margin / 2);
	}
	.widget{
		width: 100%;
		@media(min-width: 576px){
			width: 33.33%;
		}
		@media(min-width: 1200px){
			width: 20%;
		}
		float: left;
		padding-left: $theme-padding / 2;
		padding-right: $theme-padding / 2;
	}
	.tutor-widget-course-filter{
		clear: both;
		width: 100%;
		padding: 0 ($theme-margin / 2);
	}
}
@media(max-width: 575px){
	.tutor-pagination-wrapper{
		.filter-offcanvas-btn,
		.filter-top-btn{
			margin: 15px 0 0;
		}
	}
}
.tutor-user-public-profile .photo-area .pp-area .profile-name h3{
	font-weight: 500;
	font-size: 30px;
	text-transform: capitalize;
}
.tutor-dashboard .tutor-frontend-dashboard-header{
	margin-top: $theme-margin;
}
#tutor-registration-from{
	margin-bottom: $theme-margin;
	@media(min-width: 1200px){
		margin-bottom: 80px;
	}
	max-width: 700px;
	margin-left: auto;
	margin-right: auto;
}
.tutor-login-wrap{
	margin-top: $theme-margin;
	margin-bottom: $theme-margin;
	@media(min-width: 1200px){
		margin-top: 80px;
		margin-bottom: 80px;
	}
}
.tutor-pagination{
	@include border-radius($border-radius);
	padding: 15px 20px;
	border-color: $border-color;
	ul.tutor-pagination-numbers{
		gap: 0;
		.page-numbers{
			color: $body-link;
			font-weight: 400 !important;
			font-size: $font-size-base !important;
			@include size(40px,40px);
			display: ilnine-block;
			text-align: center;
			line-height: 40px;
			background: #fff;
			@include border-radius(50%);
			margin: 0 2px !important;
			&:before{
				display: none !important;
			}
			&:hover,
			&:focus,
			&.next,
			&.prev,
			&.prev:hover,
			&.next:hover,
			&.current{
				@include size(40px,40px);
				line-height: 40px;
				@include border-radius(50%);
				color: #fff;
				background: $theme-color;
			}
		}
	}
}