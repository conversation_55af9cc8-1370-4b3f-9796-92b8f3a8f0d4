/*------------------------------------------------------------------
[Table of contents]
1. base
2. elements
3. form
4. layout
5. menu
6. pages 
7. post
8. effect 
9. utilities
10. widgets layout
11. widgets 
12. responsive
-------------------------------------------------------------------*/
// Configuration
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins";
@import "bootstrap/scss/utilities";
@import "vars/vars-global";
@import "vars/template-vars";
@import "template/utilities";

/* 1. base */
@import "template/base";

/* 2. elements */
@import "template/widgets-layout";

/* 3. form */
@import "template/form";

/* 4. layout */
@import "template/layout";

/* 5. menu */
@import "template/menu";

/* 6. pages */
@import "template/pages";

/* 7. post */
@import "template/post";

/* 8. effect */
@import "template/theme-effect";

/* 10. widgets layout */

@import "template/elements";

/* 11. widgets */
@import "template/widgets";

@import "template/listings";

/* 12. responsive */

@import "template/responsive";