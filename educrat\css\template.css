/*------------------------------------------------------------------
[Table of contents]
1. base
2. elements
3. form
4. layout
5. menu
6. pages 
7. post
8. effect 
9. utilities
10. widgets layout
11. widgets 
12. responsive
-------------------------------------------------------------------*/
/**
* Web Application Prefix Apply For Making Owner Styles
*/
/**
 *   Blocks Layout Selectors
 */
/********* LAYOUT **************/
/* carousel-controls-v1 */
/* carousel-controls-v2 */
/* carousel-controls-v3 */
@keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes fl-x {
  0% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  50% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
  100% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
}

@keyframes fl-x {
  0% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  50% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
  100% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
}

@-webkit-keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

@keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

.animate-fl-x {
  -webkit-animation-name: fl-x;
  animation-name: fl-x;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.animate-fl-y {
  -webkit-animation-name: fl-y;
  animation-name: fl-y;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

/* 1. base */
html {
  overflow-x: hidden;
}

body {
  overflow-x: initial !important;
  font-size: var(--educrat-main-font-size);
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  margin-top: 25px;
  margin-bottom: 25px;
}

i {
  display: inline-block;
}

.sticky-top {
  z-index: 2;
}

.wp-block-image,
.wp-block-gallery {
  margin-bottom: 28px;
}

figcaption {
  margin-top: 15px;
}

button, .btn,
a {
  outline: none !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.mfp-container {
  overflow-x: hidden;
}

fieldset {
  clear: both;
  overflow: hidden;
}

textarea {
  resize: none;
}

p {
  line-height: 1.8;
}

.hidden {
  display: none !important;
  visibility: hidden !important;
}

.form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  opacity: 0.7;
  filter: alpha(opacity=70);
  color: var(--educrat-text-color);
}

.form-control::-moz-placeholder {
  /* Firefox 19+ */
  opacity: 0.7;
  filter: alpha(opacity=70);
  color: var(--educrat-text-color);
}

.form-control:-ms-input-placeholder {
  /* IE 10+ */
  opacity: 0.7;
  filter: alpha(opacity=70);
  color: var(--educrat-text-color);
}

.form-control:-moz-placeholder {
  /* Firefox 18- */
  opacity: 0.7;
  filter: alpha(opacity=70);
  color: var(--educrat-text-color);
}

ol ol, ol ul,
ul ol,
ul ul {
  padding-left: 20px;
}

.post-password-form input {
  height: 43px;
  padding: 5px 10px;
}

.post-password-form input[type="submit"] {
  background: var(--educrat-theme-color);
  color: #fff;
  border: none;
  padding: 5px 25px;
}

a:focus, .btn:focus {
  outline: none !important;
}

.list,
.list-no {
  list-style: none;
  padding: 0;
  margin: 0;
}

.media-body {
  width: 10000px;
}

.pswp__item {
  cursor: move;
}

.no-border {
  border: none !important;
}

.no-border:before {
  display: none !important;
}

.wpb_widgetised_column {
  margin: 0;
}

.topmenu-menu-line {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topmenu-menu-line li {
  display: inline-block;
  vertical-align: middle;
}

.topmenu-menu-line li .space {
  margin: 0 3px;
}

.top-menu-mobile .title {
  font-size: 20px;
  padding: 0 15px;
  margin: 0 0 15px;
}

.top-menu-mobile .navbar-nav > li > a {
  padding: 2px 15px;
}

.mfp-content {
  text-align: left;
}

.login-popup .mfp-content {
  max-width: 810px;
}

.login-popup .mfp-content .msg-inner {
  padding: 15px;
}

@media (min-width: 1200px) {
  .login-popup .mfp-content .msg-inner {
    padding: 1.875rem;
  }
}

.login-popup.mfp-ready .mfp-content {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  opacity: 1;
  filter: alpha(opacity=100);
}

.login-popup .mfp-close {
  border: 0;
  background-color: transparent !important;
  top: 18px;
  right: 15px;
  color: #006c70;
  font-size: 30px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.login-popup .mfp-close:hover, .login-popup .mfp-close:focus {
  color: #f33066;
}

.mfp-wrap .mfp-content {
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
  -webkit-transform: translateY(-25%);
  -ms-transform: translateY(-25%);
  -o-transform: translateY(-25%);
  transform: translateY(-25%);
}

.mfp-wrap.mfp-ready .mfp-content {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.apus-mfp-zoom-in .mfp-content {
  max-width: 550px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content {
    padding: 40px;
  }
}

.apus-mfp-zoom-in.login-popup .mfp-content {
  height: auto;
}

.apus-mfp-zoom-in.login-popup .form-login-register-inner {
  padding: 0;
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.apus-mfp-zoom-in .advance-search-wrapper {
  height: 100%;
}

.mfp-bg {
  background-color: #222;
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.apus-quickview .mfp-inline-holder .mfp-content {
  position: relative;
  max-width: 100%;
  width: 90%;
  margin: 0 auto;
  background: #fff;
}

@media (min-width: 1200px) {
  .apus-quickview .mfp-inline-holder .mfp-content {
    width: 1200px;
    min-height: 400px;
  }
}

.apus-quickview .mfp-inline-holder .mfp-content .details-product {
  padding: 35px 20px 20px;
  overflow: hidden;
  margin: 0 !important;
}

@media (min-width: 992px) {
  .apus-quickview .mfp-inline-holder .mfp-content .details-product {
    padding: 0;
  }
}

.apus-quickview .mfp-inline-holder .mfp-content .woocommerce-product-details__short-description-wrapper {
  overflow: auto;
}

.apus-quickview .mfp-inline-holder .mfp-content .information-wrapper {
  padding-top: 30px;
  padding-bottom: 30px;
  overflow: hidden;
}

@media (min-width: 992px) {
  .apus-quickview .mfp-inline-holder .mfp-content .wrapper-img-main {
    padding: 1.875rem 0 1.875rem 1.875rem;
  }
  .apus-quickview .mfp-inline-holder .mfp-content .information {
    padding: 0 1.875rem 0 0;
  }
}

@media (max-width: 767px) {
  .apus-quickview .mfp-inline-holder .mfp-content .details-product .information {
    padding-top: 10px;
  }
}

.apus-quickview .mfp-inline-holder .mfp-content .mfp-close {
  background: transparent !important;
  color: var(--educrat-text-color);
  font-size: 30px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-quickview .mfp-inline-holder .mfp-content .mfp-close:hover, .apus-quickview .mfp-inline-holder .mfp-content .mfp-close:active {
  color: #f33066;
}

.apus-quickview .mfp-inline-holder .mfp-content .wrapper-thumbs {
  margin-top: 16px;
}

.apus-quickview .mfp-inline-holder .mfp-content .slick-carousel {
  margin-left: -8px;
  margin-right: -8px;
}

.apus-quickview .mfp-inline-holder .mfp-content .slick-carousel .slick-slide {
  padding-left: 8px;
  padding-right: 8px;
}

.action .caret {
  width: 8px;
  height: 8px;
  position: relative;
}

.action[aria-expanded="true"] b {
  border: none;
}

.action[aria-expanded="true"] b:before {
  font-family: FontAwesome;
  content: "\f00d";
  position: absolute;
  top: 0;
  left: 0;
  font-size: 12px;
}

ins {
  text-decoration: none;
}

img {
  border: 0;
  vertical-align: top;
  max-width: 100%;
  height: auto;
}

.video-responsive {
  height: 0;
  padding-top: 0;
  padding-bottom: 56.25%;
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
}

.video-responsive embed, .video-responsive iframe, .video-responsive object, .video-responsive video {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
}

.audio-responsive iframe {
  width: 100%;
  height: 126px;
}

ul.list-square {
  padding: 0;
  margin: 0;
  list-style: none;
}

ul.list-square > li {
  line-height: 35px;
  font-size: 14px;
  margin: 0;
}

ul.list-square > li.active > a, ul.list-square > li:hover > a {
  color: var(--educrat-theme-color);
}

ul.list-square > li.active > a:before, ul.list-square > li:hover > a:before {
  background: var(--educrat-theme-color);
}

ul.list-square > li > a {
  display: block;
  padding-left: 20px;
  position: relative;
}

ul.list-square > li > a:before {
  content: '';
  background: var(--educrat-link-color);
  width: 8px;
  height: 8px;
  left: 0;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.breadcrumb > a + li:before,
.breadcrumb > li + a:before,
.breadcrumb > li + li:before {
  display: inline-block;
  content: "";
  color: var(--educrat-text-color);
  line-height: 1;
  vertical-align: middle;
  margin: 0 10px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #6A7A99;
}

.apus-breadscrumb {
  background-color: #F5F7FE;
  margin-bottom: 20px;
  position: relative;
}

@media (min-width: 1200px) {
  .apus-breadscrumb {
    margin-bottom: 60px;
  }
}

.apus-breadscrumb .breadcrumb {
  background: transparent;
  margin: 0;
  padding: 0;
}

.apus-breadscrumb .active {
  color: var(--educrat-link-color);
}

.apus-breadscrumb a {
  color: var(--educrat-text-color);
}

.apus-breadscrumb a:hover, .apus-breadscrumb a:focus {
  color: var(--educrat-link-color);
}

.apus-breadscrumb .wrapper-breads {
  padding: 15px 0;
}

.apus-breadscrumb .bread-title {
  text-transform: capitalize;
  font-size: 22px;
  margin: 0 0 .3rem;
  line-height: 1.1;
}

@media (min-width: 1200px) {
  .apus-breadscrumb .bread-title {
    font-size: 36px;
  }
}

.apus-breadscrumb.has_bg {
  z-index: 1;
  background-position: center;
  background-size: cover;
  border: 0 !important;
}

.apus-breadscrumb.has_bg:before {
  z-index: -1;
  position: absolute;
  content: '';
  background: #052044;
  opacity: 0.8;
  filter: alpha(opacity=80);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.apus-breadscrumb.has_bg li:before,
.apus-breadscrumb.has_bg a,
.apus-breadscrumb.has_bg .bread-title {
  color: #fff !important;
}

.breadcrumbs-simple {
  padding: 15px 0;
}

.breadcrumbs-simple .breadcrumb {
  margin: 0;
  padding: 0;
  background-color: transparent;
}

.breadcrumbs-simple .breadcrumb a {
  color: var(--educrat-text-color);
}

.breadcrumbs-simple .breadcrumb a:hover, .breadcrumbs-simple .breadcrumb a:focus {
  color: var(--educrat-link-color);
}

.breadcrumbs-simple .breadcrumb .active {
  color: var(--educrat-link-color);
}

.search-form input,
.search-form .btn {
  background: #ebedee;
  border-color: #ebedee;
  color: var(--educrat-link-color);
}

.search-form .btn {
  padding: 1.875rem 15px;
}

.ui-autocomplete.ui-widget-content {
  padding: 15px;
  margin: 0;
  list-style: none;
  background: #fff;
  width: 300px !important;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-box-shadow: 0 10px 30px 0 rgba(13, 38, 59, 0.05);
  box-shadow: 0 10px 30px 0 rgba(13, 38, 59, 0.05);
}

.ui-autocomplete.ui-widget-content li {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #EDEDED;
  cursor: pointer;
}

.ui-autocomplete.ui-widget-content li:hover .team-agent-list-label, .ui-autocomplete.ui-widget-content li:focus .team-agent-list-label {
  color: var(--educrat-theme-color);
}

.ui-autocomplete.ui-widget-content li:last-child {
  border: none;
  margin: 0;
  padding: 0;
}

.add-fix-top {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: fixed;
  z-index: 4;
  bottom: 8px;
  right: 8px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 0.9375rem;
  -webkit-transform: translateY(20px);
  -ms-transform: translateY(20px);
  -o-transform: translateY(20px);
  transform: translateY(20px);
  display: inline-block;
  border-radius: 8px;
  text-align: center;
  opacity: 0;
  filter: alpha(opacity=0);
  background: var(--educrat-theme-color);
  color: #fff;
}

@media (min-width: 1200px) {
  .add-fix-top {
    right: 20px;
    bottom: 20px;
  }
}

.add-fix-top.active {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.7;
  filter: alpha(opacity=70);
}

@media (min-width: 1200px) {
  .add-fix-top.active {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.add-fix-top.active:focus, .add-fix-top.active:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background: var(--educrat-theme-hover-color);
  color: #fff;
}

.menu {
  padding: 0;
  margin: 0;
}

.menu li {
  list-style: none;
  margin-bottom: 8px;
}

.menu li:last-child {
  margin-bottom: 0;
}

.menu ul {
  padding-left: 20px;
  margin: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes -webkit-spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

.apus-page-loading {
  top: 0;
  left: 0;
  position: fixed;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 9999;
}

.apus-loader-inner {
  margin: 0 auto;
  width: 80px;
  height: 80px;
  text-align: center;
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  background-size: cover;
  background-repeat: no-repeat;
}

.apus-loader-inner > div {
  width: 8px;
  height: 100%;
  display: inline-block;
  float: left;
  margin-left: 2px;
  -webkit-animation: delay 0.8s infinite ease-in-out;
  animation: delay 0.8s infinite ease-in-out;
}

.apus-loader-inner .loader1 {
  background-color: #e39505;
}

.apus-loader-inner .loader2 {
  background-color: #ff5395;
  -webkit-animation-delay: -0.7s;
  animation-delay: -0.7s;
}

.apus-loader-inner .loader3 {
  background-color: #84b813;
  -webkit-animation-delay: -0.6s;
  animation-delay: -0.6s;
}

.apus-loader-inner .loader4 {
  background-color: #f38ca3;
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}

.apus-loader-inner .loader5 {
  background-color: #da5800;
  -webkit-animation-delay: -0.4s;
  animation-delay: -0.4s;
}

@-webkit-keyframes delay {
  0%, 40%, 100% {
    -webkit-transform: scaleY(0.05);
    transform: scaleY(0.05);
  }
  20% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
}

@keyframes delay {
  0%, 40%, 100% {
    transform: scaleY(0.05);
    -webkit-transform: scaleY(0.05);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}

.tab-product-center .nav-tabs {
  border: none;
  margin: 0 0 30px;
  text-align: center;
}

.tab-product-center .nav-tabs > li {
  display: inline-block;
  float: none;
  margin: 0 !important;
}

.tab-product-center .nav-tabs > li > a {
  border: none !important;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  padding: 0 30px;
  color: var(--educrat-link-color);
  outline: none !important;
}

.tab-product-center .nav-tabs > li:hover a, .tab-product-center .nav-tabs > li.active a {
  color: var(--educrat-theme-color);
}

.tab-product-center .tab-content {
  position: relative;
}

.tab-product-center .tab-content.loading:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9) url(../images/loading-quick.gif) no-repeat scroll center 100px/50px auto;
}

.page-links {
  overflow: hidden;
  margin: 0 0 30px;
}

.page-links .page-links-title {
  font-weight: normal;
  color: var(--educrat-link-color);
}

.page-links > span:not(.page-links-title),
.page-links > a {
  display: inline-block;
  line-height: 1;
  margin: 0 3px;
  padding: 10px 13px;
  border-radius: 2px;
  border: 1px solid #EDEDED;
  color: var(--educrat-link-color);
}

.page-links > span:not(.page-links-title):hover, .page-links > span:not(.page-links-title):active,
.page-links > a:hover,
.page-links > a:active {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.page-links > span:not(.page-links-title) {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

option {
  padding: 5px;
}

.image-lazy-loading .image-wrapper {
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  max-height: 100%;
}

.image-lazy-loading .image-wrapper img {
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.image-lazy-loading .image-wrapper.image-loaded {
  background: none;
}

.image-lazy-loading .image-wrapper.image-loaded img {
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-header .wpml-ls-legacy-dropdown a.wpml-ls-item-toggle {
  border: none !important;
  padding: 4px 25px 6px 0;
  background: transparent !important;
  color: #707070 !important;
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  background: #fff;
  border: none;
  border: 1px solid #EDEDED;
  min-width: 114px;
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li {
  border-bottom: 1px solid #EDEDED;
  padding: 9px 10px;
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li a {
  border: none !important;
  background: transparent !important;
  padding: 0;
  color: var(--educrat-link-color);
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li a:hover, .apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li a:focus {
  color: var(--educrat-link_hover_color);
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li:last-child {
  border: none;
}

.apus-header .wpml-ls-legacy-dropdown {
  width: auto;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency,
.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency {
  padding: 0;
  border: none;
  background: transparent;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency {
  padding: 5px 10px;
  border-bottom: 1px solid #EDEDED;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency:last-child {
  border: none;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency {
  font-weight: 400;
  color: #707070;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency a:hover, .apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency.active a {
  font-weight: 400;
  color: var(--educrat-theme-color);
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency {
  min-width: 80px;
  text-align: inherit;
  z-index: 99;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency::after {
  font-size: 11px;
}

.apus_socials {
  list-style: none;
  padding: 0;
  margin: 0;
}

.apus_socials li {
  display: inline-block;
  margin-right: 5px;
}

.apus_socials li:last-child {
  margin-right: 0;
}

.apus_socials a {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: inline-block;
  color: var(--educrat-text-color);
  font-size: 14px;
}

.apus_socials a:hover, .apus_socials a:focus {
  color: var(--educrat-theme-color);
  background: #F7F8FB;
}

.select2-container {
  outline: none !important;
}

.select2-container.select2-container--default .select2-search--dropdown {
  padding: 0 20px;
}

.select2-container.select2-container--default .select2-search--dropdown .select2-search__field {
  outline: none !important;
  border-color: #EDEDED;
  border-width: 0 0 1px;
  border-radius: 0;
  height: 40px;
  font-size: 0.9375rem;
  padding: 5px 0;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.select2-container.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  border-color: #1A064F;
}

.select2-results__option {
  padding: 5px 10px;
  outline: none !important;
}

.select2-dropdown {
  border: 1px solid #EDEDED !important;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 8px !important;
  min-width: 160px;
}

.select2-container--default .select2-results > .select2-results__options {
  max-height: 215px !important;
  scrollbar-width: thin;
}

.select2-results > .select2-results__options {
  padding: 10px 20px;
}

.select2-results .select2-results__option {
  color: var(--educrat-text-color);
  padding: 6px 0;
  background-color: transparent;
  position: relative;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .select2-results .select2-results__option {
    padding: 8px 0;
  }
}

.select2-results .select2-results__option:last-child {
  border-bottom: 0;
}

.select2-container--default.select2-container .select2-selection--single {
  height: calc(1.8em + (1.2rem + 2px));
  background: #fff;
  outline: none !important;
  border-radius: 8px !important;
  border: 1px solid #EDEDED;
  margin: 0;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 5px 12px;
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__rendered {
  padding-top: 5px;
  padding-bottom: 5px;
  color: var(--educrat-text-color);
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__placeholder {
  color: var(--educrat-text-color);
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__arrow {
  top: 12px;
  right: 11px;
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__clear {
  line-height: 24px;
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__clear:hover, .select2-container--default.select2-container .select2-selection--single .select2-selection__clear:focus {
  color: #f33066;
}

.select2-container--default.select2-container .select2-selection--multiple {
  height: calc(1.8em + (1.2rem + 2px));
  outline: none !important;
  border-radius: 8px;
  border: 1px solid #EDEDED;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.select2-container--default.select2-container .select2-selection--multiple .select2-selection__choice {
  border: 0;
  color: var(--educrat-theme-color);
  background-color: var(--educrat-theme-color-007);
}

.select2-container--default.select2-container.select2-container--open .select2-selection--multiple {
  border-color: var(--educrat-theme-color);
}

.select2-container--default.select2-container.select2-container--open .select2-selection--single {
  border-color: #e0e0e0;
}

.select2-container.select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[data-selected] {
  color: var(--educrat-theme-color);
  background-color: #fff;
}

.select2-container.select2-container--default .select2-results__option[aria-selected="true"], .select2-container--default .select2-results__option[data-selected="true"] {
  color: var(--educrat-theme-color);
  background-color: #fff;
}

@media (min-width: 992px) {
  .customizer-search.customizer-search-halpmap > .select2-dropdown {
    margin-top: 10px;
  }
}

.select2-container.orderby .select2-dropdown {
  margin-top: 5px;
}

.nav-tabs-custom {
  border: 1px solid #e8ebef;
  margin: 47px 0 0;
}

.nav-tabs-custom > .nav-tabs {
  margin-top: -47px;
  margin-left: -1px;
}

.nav-tabs-custom .tab-content > .tab-pane {
  padding: 0 1em;
}

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eee;
}

section#medical .col-md-3.col-sm-4,
.fontawesome-icon-list .col-md-3.col-sm-4 {
  padding: 10px;
}

.bs-glyphicons {
  padding: 0;
}

.bs-glyphicons li {
  width: 24.5%;
  height: 115px;
  padding: 10px;
  margin: 0 -1px -1px 0;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  border: 1px solid #e8edef;
  display: inline-block;
}

.bs-glyphicons .glyphicon {
  margin-top: 5px;
  margin-bottom: 10px;
  font-size: 24px;
}

.bs-glyphicons .glyphicon-class {
  display: block;
  text-align: center;
  word-wrap: break-word;
}

.apus-social-share .share-action {
  margin-right: 5px;
  font-weight: 500;
}

.apus-social-share a {
  text-align: center;
  line-height: 40px;
  display: inline-block;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #4F547B;
  background: #fff;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-social-share a:hover, .apus-social-share a:focus {
  color: #4F547B;
  background: #EEF2F6;
}

.apus-social-share a + a {
  margin-left: 2px;
}

.box-account .title {
  margin: 0 0 20px;
  font-size: 25px;
}

.space-height-20 {
  height: 20px;
  width: 100%;
  overflow: hidden;
}

.filter-scroll {
  height: 100%;
}

.tooltip {
  z-index: 4;
}

.flaticon-repeat:before {
  content: "\f129";
  font-family: "Flaticon";
}

.affix {
  position: fixed !important;
}

.tooltip.top .tooltip-arrow {
  border-top-color: #24324a;
}

.tooltip.top .tooltip-inner {
  padding: 5px 15px;
  background-color: #24324a;
  color: #fff;
  border-radius: 8px;
}

.apus-results {
  margin-top: 10px;
}

.apus-results .apus-results-reset {
  display: inline-block;
  padding: 6px 15px;
  background: #f33066;
  color: #fff;
  white-space: nowrap;
  font-weight: 400;
  font-size: 15px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-results .apus-results-reset:hover, .apus-results .apus-results-reset:active {
  color: #fff;
  background: #e30d48;
}

.ajax-pagination {
  text-align: center;
  margin: 10px 0;
}

.ajax-pagination.apus-loader .apus-loadmore-btn {
  display: none;
}

.ajax-pagination.apus-loader:after {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  background-position: center center;
  background-repeat: no-repeat;
  content: "";
  width: 100%;
  height: 50px;
  display: block;
}

.ajax-pagination .apus-loadmore-btn + .apus-allproducts {
  display: none;
}

.ajax-pagination .apus-loadmore-btn.hidden + .apus-allproducts {
  display: block;
  color: #f33066;
}

.ajax-listings-pagination {
  text-align: center;
}

.ajax-listings-pagination .apus-loadmore-btn {
  display: none;
}

.ajax-listings-pagination:not(.all-listings-loaded) .apus-loadmore-btn {
  display: inline-block;
}

.ajax-listings-pagination:not(.all-listings-loaded) .apus-allproducts {
  display: none;
}

.wp-block-button .wp-block-button__link {
  border-radius: 8px;
}

.wp-block-button.is-style-squared .wp-block-button__link {
  border-radius: 0;
}

.wp-block-button .wp-block-button__link:not(.has-background) {
  background-color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.wp-block-button .wp-block-button__link:not(.has-background):hover, .wp-block-button .wp-block-button__link:not(.has-background):focus {
  color: #fff;
  background-color: var(--educrat-theme-hover-color);
  border-color: var(--educrat-theme-hover-color);
}

.wp-block-button.is-style-outline .wp-block-button__link {
  background-color: transparent;
  border-color: var(--educrat-theme-color);
  color: var(--educrat-theme-color);
}

pre {
  display: block;
  padding: 11.5px;
  margin: 0 0 12px;
  line-height: 1.8;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
}

dl {
  margin-bottom: 24px;
}

table {
  width: 100%;
  border: 1px solid #EDEDED;
  margin-bottom: 24px;
}

table th, table td {
  border: 1px solid #EDEDED;
  padding: 8px 15px;
}

table.wp-calendar-table th, table.wp-calendar-table td {
  padding: 6px 12px;
}

.font-20 {
  font-size: 20px;
}

.font-medium {
  font-weight: 500;
}

.breadcrumb {
  display: block;
}

.breadcrumb li {
  display: inline;
}

/* 2. elements */
/* block */
.widget {
  margin-bottom: 1.875rem;
  position: relative;
  padding: 0px;
  background: transparent;
}

.widget .wp-block-group__inner-container > h1,
.widget .wp-block-group__inner-container > h2,
.widget .wp-block-group__inner-container > h3,
.widget .wp-block-group__inner-container > h4,
.widget .wp-block-group__inner-container > h5,
.widget .wp-block-group__inner-container > h6 {
  margin: 0 0 15px;
  font-weight: 500;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .widget .wp-block-group__inner-container > h1,
  .widget .wp-block-group__inner-container > h2,
  .widget .wp-block-group__inner-container > h3,
  .widget .wp-block-group__inner-container > h4,
  .widget .wp-block-group__inner-container > h5,
  .widget .wp-block-group__inner-container > h6 {
    margin: 0 0 25px;
  }
}

.widget .wp-block-group__inner-container > h1 + ul,
.widget .wp-block-group__inner-container > h2 + ul,
.widget .wp-block-group__inner-container > h3 + ul,
.widget .wp-block-group__inner-container > h4 + ul,
.widget .wp-block-group__inner-container > h5 + ul,
.widget .wp-block-group__inner-container > h6 + ul {
  margin: 0;
  padding-left: 0;
}

.widget .wp-block-group__inner-container > h1 + ul li,
.widget .wp-block-group__inner-container > h2 + ul li,
.widget .wp-block-group__inner-container > h3 + ul li,
.widget .wp-block-group__inner-container > h4 + ul li,
.widget .wp-block-group__inner-container > h5 + ul li,
.widget .wp-block-group__inner-container > h6 + ul li {
  list-style: none;
  padding: 0 0 8px;
}

.widget .wp-block-group__inner-container > h1 + ul li:last-child,
.widget .wp-block-group__inner-container > h2 + ul li:last-child,
.widget .wp-block-group__inner-container > h3 + ul li:last-child,
.widget .wp-block-group__inner-container > h4 + ul li:last-child,
.widget .wp-block-group__inner-container > h5 + ul li:last-child,
.widget .wp-block-group__inner-container > h6 + ul li:last-child {
  padding-bottom: 0;
}

.widget .wp-block-group__inner-container > h1 + ul li:hover > a,
.widget .wp-block-group__inner-container > h1 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h1 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h2 + ul li:hover > a,
.widget .wp-block-group__inner-container > h2 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h2 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h3 + ul li:hover > a,
.widget .wp-block-group__inner-container > h3 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h3 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h4 + ul li:hover > a,
.widget .wp-block-group__inner-container > h4 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h4 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h5 + ul li:hover > a,
.widget .wp-block-group__inner-container > h5 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h5 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h6 + ul li:hover > a,
.widget .wp-block-group__inner-container > h6 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h6 + ul li.current-cat > a {
  color: var(--educrat-theme-color);
}

.widget .wp-block-group__inner-container > h2 {
  font-size: 18px;
}

@media (min-width: 1200px) {
  .widget .wp-block-group__inner-container > h2 {
    font-size: 20px;
  }
}

.widget .wc-block-price-filter__title,
.widget .widget-title, .widget .widgettitle, .widget .widget-heading {
  font-weight: 500;
  margin: 0 0 15px;
  font-size: 18px;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .widget .wc-block-price-filter__title,
  .widget .widget-title, .widget .widgettitle, .widget .widget-heading {
    margin: 0 0 25px;
    font-size: 20px;
  }
}

.widget .wc-block-price-filter__title .urgent,
.widget .wc-block-price-filter__title .featured,
.widget .widget-title .urgent,
.widget .widget-title .featured, .widget .widgettitle .urgent,
.widget .widgettitle .featured, .widget .widget-heading .urgent,
.widget .widget-heading .featured {
  font-size: 0.75rem;
}

.sidebar .widget,
.apus-sidebar .widget {
  margin: 0 0 0.9375rem;
  padding: 0 0 0.9375rem;
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .sidebar .widget,
  .apus-sidebar .widget {
    margin: 0 0 1.875rem;
    padding: 0 0 1.875rem;
  }
}

.sidebar .widget:last-child,
.apus-sidebar .widget:last-child {
  border-bottom: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}

.sidebar .widget.widget_apus_search,
.apus-sidebar .widget.widget_apus_search {
  border: 0;
  margin: 0;
}

[class*="apus_footer"] .wpml-ls-legacy-dropdown .wpml-ls-sub-menu,
.apus-footer .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  bottom: 100%;
  top: inherit;
  -webkit-transform: translateY(-8px);
  -ms-transform: translateY(-8px);
  -o-transform: translateY(-8px);
  transform: translateY(-8px);
}

[class*="apus_footer"] .wpml-ls-legacy-dropdown .wpml-ls-current-language:hover .wpml-ls-sub-menu,
.apus-footer .wpml-ls-legacy-dropdown .wpml-ls-current-language:hover .wpml-ls-sub-menu {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

[class*="apus_footer"] .wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:after,
.apus-footer .wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:after {
  bottom: 100%;
  top: inherit;
}

[class*="apus_footer"] .widget .widget-title, [class*="apus_footer"] .widget .widgettitle, [class*="apus_footer"] .widget .widget-heading,
.apus-footer .widget .widget-title,
.apus-footer .widget .widgettitle,
.apus-footer .widget .widget-heading {
  font-size: 17px;
}

/* 3. form */
.btn {
  border-width: 2px;
  white-space: nowrap;
}

.btn-action-icon {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 1rem;
  text-align: center;
  color: var(--educrat-text-color);
  background: #F4F4F4;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.btn-action-icon:hover, .btn-action-icon:focus {
  color: #fff;
  background: var(--educrat-theme-color);
}

.btn-action-icon.rejec:hover, .btn-action-icon.rejec:focus {
  color: #fff;
  background: #ff9b20;
}

.btn-action-icon.rejec.rejected {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.btn-action-icon.download:hover, .btn-action-icon.download:focus {
  color: #fff;
  background: #222;
}

.btn-action-icon[class*="remove"]:hover, .btn-action-icon[class*="remove"]:focus {
  color: #fff;
  background: #f33066;
}

.btn-action-icon:before {
  line-height: 30px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  line-height: 35px;
  opacity: 0;
  filter: alpha(opacity=0);
  color: var(--educrat-link-color);
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.btn-action-icon.loading {
  background: rgba(255, 255, 255, 0.8) !important;
  color: transparent !important;
}

.btn-action-icon.loading:before {
  opacity: 0.8;
  filter: alpha(opacity=80);
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.list-action [class*="btn"] i {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  background-color: #f4f4f4;
  color: #717171;
  font-size: 1.0625rem;
  height: 45px;
  line-height: 45px;
  width: 45px;
  text-align: center;
  border-radius: 50%;
  margin-right: .5rem;
}

.list-action [class*="btn"]:hover i {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.list-action [class*="added"] i {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.list-action [class*="added"]:hover i:before {
  content: "\e646";
  font-family: 'themify';
  font-weight: 400;
}

.list-action [class*="btn"].loading i:before {
  display: inline-block;
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

@media (min-width: 1200px) {
  .list-action .review {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
    min-width: 200px;
    text-align: center;
  }
}

.list-action > * {
  display: inline-block;
  margin-right: 0.625rem;
}

@media (min-width: 1200px) {
  .list-action > * {
    margin-right: 1.875rem;
  }
}

.list-action > *:last-child {
  margin-right: 0 !important;
}

.view_all {
  font-weight: 500;
  display: inline-block;
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.view_all i {
  display: inline-block;
  margin-left: 10px;
}

.view_all:hover {
  -webkit-animation: slideIcon 0.6s linear 0s 1 normal;
  /* Safari 4.0 - 8.0 */
  animation: slideIcon 0.6s linear 0s 1 normal;
}

.pre {
  margin-right: 0.4rem;
}

@media (min-width: 1200px) {
  .pre {
    margin-right: 0.625rem;
  }
}

.next {
  margin-left: 0.4rem;
}

@media (min-width: 1200px) {
  .next {
    margin-left: 0.625rem;
  }
}

.btn-candidate-alert:before,
.btn-job-alert:before {
  content: "\f142";
  font-family: "Flaticon";
  margin-right: 10px;
  font-size: 25px;
  line-height: 0;
  vertical-align: sub;
  display: inline-block;
}

.btn-outline.btn-primary {
  background: transparent;
  border-color: #0d6efd;
  color: #0d6efd;
}

.btn-outline.btn-primary:hover {
  color: #fff;
  border-color: #0d6efd;
  background: #0d6efd;
}

.btn-outline.btn-success {
  background: transparent;
  border-color: #27b737;
  color: #27b737;
}

.btn-outline.btn-success:hover {
  color: #FFFFFF;
  border-color: #27b737;
  background: #27b737;
}

.btn-outline.btn-info {
  background: transparent;
  border-color: #0dcaf0;
  color: #0dcaf0;
}

.btn-outline.btn-info:hover {
  color: #FFFFFF;
  border-color: #0dcaf0;
  background: #0dcaf0;
}

.btn-outline.btn-danger {
  background: transparent;
  border-color: #f33066;
  color: #f33066;
}

.btn-outline.btn-danger:hover {
  color: #FFFFFF;
  border-color: #f33066;
  background: #f33066;
}

.btn-outline.btn-warning {
  background: transparent;
  border-color: #ff9b20;
  color: #ff9b20;
}

.btn-outline.btn-warning:hover {
  color: #FFFFFF;
  border-color: #ff9b20;
  background: #ff9b20;
}

.btn-inverse.btn-primary:hover {
  color: #0d6efd;
  background: #FFFFFF;
}

.btn-inverse.btn-success:hover {
  color: #27b737;
  background: #FFFFFF;
}

.btn-inverse.btn-info:hover {
  color: #0dcaf0;
  background: #FFFFFF;
}

.btn-inverse.btn-danger:hover {
  color: #f33066;
  background: #FFFFFF;
}

.btn-inverse.btn-warning:hover {
  color: #ff9b20;
  background: #FFFFFF;
}

.btn-inverse.btn-theme:hover {
  color: var(--educrat-theme-color);
  background: #FFFFFF;
}

.view-more-btn i {
  margin-left: 12px;
}

.reamore {
  font-size: 14px;
  font-weight: 500;
  color: var(--educrat-theme-color) !important;
  text-transform: uppercase;
  padding: 0 0 4px;
  border-bottom: 2px solid var(--educrat-theme-color);
}

.reamore i {
  margin-left: 8px;
}

.btn-browse {
  text-transform: uppercase;
  font-size: 12px;
  padding: 10px 15px;
  border: 1px solid #eaeff5;
  border-radius: 50px;
  line-height: 1.42857143;
}

.btn-browse:hover, .btn-browse:focus {
  background: var(--educrat-theme-color);
  color: #fff;
  border-color: var(--educrat-theme-color);
}

.apus-loadmore-btn {
  display: inline-block;
  padding: 10px 30px;
  border: 1px solid #24324A;
  text-transform: capitalize;
  font-weight: 600;
  color: #24324A;
  background-color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 8px;
  position: relative;
}

@media (min-width: 1200px) {
  .apus-loadmore-btn {
    padding: 10px 40px;
  }
}

.apus-loadmore-btn:hover, .apus-loadmore-btn:focus {
  color: #fff;
  border-color: #24324A;
  background-color: #24324A;
}

.apus-loadmore-btn.loading {
  border-color: transparent !important;
  background-color: transparent !important;
  color: transparent !important;
}

.apus-loadmore-btn.loading:after {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  background-position: center center;
  background-repeat: no-repeat;
  content: "";
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.viewmore-products-btn {
  position: relative;
}

.viewmore-products-btn:before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  z-index: 2;
  opacity: 0;
  filter: alpha(opacity=0);
  background: rgba(255, 255, 255, 0.9) url(../images/loading-quick.gif) no-repeat scroll center center/20px auto;
}

.viewmore-products-btn.loading:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

button:focus,
.btn:focus {
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.radius-0 {
  border-radius: 0 !important;
}

.radius-circle {
  border-radius: 100px !important;
}

.read-more {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--educrat-theme-color);
}

.btn-theme.btn-white {
  background: #fff;
  color: var(--educrat-theme-color) !important;
  border-color: #fff;
}

.btn-theme.btn-white:active, .btn-theme.btn-white:hover {
  background-color: var(--educrat-theme-color);
  color: #fff !important;
  border-color: var(--educrat-theme-color);
}

.btn-purple {
  background: #bc7cbf;
  color: #fff;
  border-color: #bc7cbf;
}

.btn-purple:active, .btn-purple:hover {
  color: #fff;
  background: #b36bb7;
  border-color: #b36bb7;
}

.btn-orange {
  background: #E8543E;
  color: #fff;
  border-color: #E8543E;
}

.btn-orange:active, .btn-orange:hover {
  color: #fff;
  background: #e54027;
  border-color: #e54027;
}

.btn-brown {
  background: transparent;
  color: #c0c0c0;
  border-color: #4e4f4f;
}

.btn-brown:focus, .btn-brown:hover {
  color: #fff;
  background: transparent;
  border-color: #fff;
}

.btn-back {
  padding: 8px 15px;
  border-radius: 2px;
  background: rgba(255, 58, 114, 0.1);
  color: #ff7c39;
  border-color: #ff7c39;
}

.btn-back:focus, .btn-back:hover {
  color: #fff;
  background: #ff3a72;
  border-color: #ff7c39;
}

.btn-white.btn-br-white {
  background: #fff;
  color: var(--educrat-link-color);
  border-color: #fff;
}

.btn-white.btn-br-white:active, .btn-white.btn-br-white:hover {
  color: var(--educrat-link-color);
  background: #d9d9d9;
  border-color: #d9d9d9;
}

.btn.btn-readmore {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  color: var(--educrat-theme-hover-color);
  font-size: 15px;
  padding: 6px 20px;
  border: 0;
  background: rgba(100, 64, 251, 0.07);
  color: var(--educrat-theme-color);
}

@media (min-width: 1200px) {
  .btn.btn-readmore {
    padding: 11px 30px;
  }
}

.btn.btn-readmore:hover, .btn.btn-readmore:focus {
  background: var(--educrat-theme-color);
  color: #fff;
}

.btn-lighten {
  border-color: #fff;
  color: #fff;
  background: transparent;
}

.btn-lighten:hover {
  color: #fff;
  background: transparent;
  border-color: #fff;
}

.btn-outline.btn-white {
  background: transparent;
  color: #fff;
  border-color: #fff;
}

.btn-outline.btn-white:active, .btn-outline.btn-white:hover {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.btn-primary.btn-inverse:active, .btn-primary.btn-inverse:hover {
  background: #fff !important;
  color: #0d6efd !important;
  border-color: #0d6efd !important;
}

.btn-theme {
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.btn-theme:active, .btn-theme:hover {
  background: var(--educrat-theme-hover-color);
  border-color: var(--educrat-theme-hover-color);
  color: #fff;
}

.btn-theme-rgb7 {
  background: var(--educrat-theme-color-007);
  color: var(--educrat-theme-color);
}

.btn-theme-rgb7:active, .btn-theme-rgb7:hover {
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.btn-theme.btn-outline {
  color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  background: transparent;
}

.btn-theme.btn-outline:hover, .btn-theme.btn-outline:active {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.btn-outline.btn-green {
  color: #00FF84;
  border-color: #00FF84;
  background: transparent;
}

.btn-outline.btn-green:hover, .btn-outline.btn-green:active {
  color: #fff;
  background: #00FF84;
  border-color: #00FF84;
}

/* Search
------------------------------------------------*/
.search-popup .dropdown-menu {
  padding: 10px;
}

.searchform .input-search {
  padding: 15px;
  border-right: 0;
  line-height: 1.5;
}

.searchform .btn-search {
  vertical-align: top;
  color: #adafac;
  padding: 12px 0.6rem;
}

.searchform .input-group-btn {
  line-height: 100%;
}

.search-category .btn {
  margin-left: 10px !important;
  border-radius: 4px !important;
}

.search-category .wpo-search-inner label.form-control {
  border: none;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}

.search-category select {
  border: none;
  text-transform: capitalize;
  font-weight: 500;
}

/* comment form
------------------------------------------------*/
.chosen-container {
  width: 100% !important;
}

.input-group-form {
  border-radius: 3px;
  background: transparent;
  margin: 0 0 5px 0;
}

.input-group-form .form-control-reversed {
  border: 0px;
  background: #e5e5e5;
  color: #cccccc;
  font-size: 14px;
  height: 34px;
}

.input-group-form .form-control-reversed:hover, .input-group-form .form-control-reversed:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.input-group-form .input-group-addon {
  border: 0;
  background: #e5e5e5;
  border-radius-left: 4px;
}

.btn-compare,
.btn-favorites {
  font-size: 21px;
  line-height: 1;
}

.btn-compare .count,
.btn-favorites .count {
  font-size: 13px;
  display: inline-block;
  font-weight: 600;
  color: #fff;
  background-color: var(--educrat-theme-color);
  border-radius: 50%;
  padding: 4px 7px;
  vertical-align: top;
  margin-top: -10px;
  margin-left: -14px;
}

.btn-underline {
  text-decoration: underline;
}

.btn-underline:hover {
  color: var(--educrat-theme-color);
}

.btn-view-all-photos {
  background-color: #fff;
}

.btn-view-all-photos i {
  display: inline-block;
  margin-right: 8px;
}

@media (max-width: 991px) {
  .btn-view-all-photos {
    padding: 5px 10px;
  }
}

.btn-view i,
.view-my-listings i {
  display: inline-block;
  margin-left: 5px;
  font-size: 10px;
}

.btn-view {
  font-size: 16px;
  font-weight: 700;
  white-space: nowrap;
}

.btn-show-filter i {
  display: inline-block;
  margin-left: 10px;
  line-height: 1;
  vertical-align: middle;
  font-size: 16px;
}

.btn-app {
  line-height: 1;
  color: #fff;
  background-color: #1A064F;
  padding: 7px 20px;
  border: 2px solid #1A064F;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  text-align: left;
}

@media (min-width: 1200px) {
  .btn-app {
    padding: 11px 25px;
    min-width: 200px;
  }
}

.btn-app:hover, .btn-app:focus {
  color: #1A064F;
  background-color: #fff;
  border-color: #1A064F;
}

.btn-app .app-icon {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-size: 20px;
  line-height: 1;
  width: 20px;
}

@media (min-width: 1200px) {
  .btn-app .app-icon {
    width: 28px;
    font-size: 28px;
  }
}

.btn-app .inner {
  padding-left: 15px;
  flex: 1;
  -ms-flex: 1;
  -webkit-box-flex: 1;
}

.btn-app .name-app {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: block;
  font-size: 15px;
  font-weight: 500;
  margin-top: 7px;
}

.btn-app .sub {
  font-size: 13px;
}

.btn-app.st_white {
  background: rgba(255, 255, 255, 0.1);
}

.btn-app.st_white:hover, .btn-app.st_white:focus {
  background-color: #fff;
  border-color: #fff;
}

.btn-app.st_normal {
  background: #EEF2F6;
  border-color: #EEF2F6;
  border-radius: 60px;
  color: var(--educrat-text-color);
}

.btn-app.st_normal .app-icon,
.btn-app.st_normal .name-app {
  color: var(--educrat-link-color);
}

.btn-app.st_normal:hover, .btn-app.st_normal:focus {
  background: var(--educrat-link-color);
  border-color: var(--educrat-link-color);
}

.btn-app.st_normal:hover .app-icon, .btn-app.st_normal:hover,
.btn-app.st_normal:hover .name-app, .btn-app.st_normal:focus .app-icon, .btn-app.st_normal:focus,
.btn-app.st_normal:focus .name-app {
  color: #fff;
}

.btn-light-theme {
  border: 0;
  text-transform: uppercase;
  background-color: var(--educrat-theme-color-010);
  color: var(--educrat-theme-color);
  padding: 11px 35px;
}

.btn-light-theme:hover, .btn-light-theme:focus {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.filter {
  padding: 8px 20px;
  border: 0;
  background-color: #F4F4F4;
  color: var(--educrat-text-color);
}

@media (min-width: 1200px) {
  .filter {
    padding: 8px 1.875rem;
  }
}

.filter i {
  margin-right: 10px;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.filter:hover, .filter:focus {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.save-search-btn,
.reset-search-btn {
  white-space: nowrap;
}

.save-search-btn i,
.reset-search-btn i {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
}

.mobile-menu-icon {
  display: inline-block;
  position: relative;
  width: 25px;
  height: 12px;
  line-height: 1;
  border-top: 2px solid #fff;
}

.mobile-menu-icon:after {
  content: '';
  position: absolute;
  background-color: #fff;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 2px;
}

.icon-vertical {
  display: inline-block;
  width: 20px;
  height: 2px;
  margin: 8px 0;
  background: #00FF84;
  position: relative;
}

.icon-vertical:before {
  content: '';
  position: absolute;
  top: -8px;
  left: 5px;
  width: 15px;
  height: 2px;
  background: #00FF84;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.icon-vertical:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 15px;
  height: 2px;
  background: #00FF84;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.action-vertical {
  display: inline-block;
  line-height: 1;
  cursor: pointer;
}

.action-vertical:hover .icon-vertical:before, .action-vertical:focus .icon-vertical:before {
  left: 0;
  width: 20px;
}

.action-vertical:hover .icon-vertical:after, .action-vertical:focus .icon-vertical:after {
  width: 20px;
}

.btn.btn-more {
  font-size: 0.9375rem;
  padding: 11px 30px;
  border: 0;
}

/* 4. layout */
/*------------------------------------*\
    Header
\*------------------------------------*/
body.page-template-page-dashboard #apus-header,
body.fix-header #apus-header {
  position: fixed;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100%;
}

.together-sidebar-account {
  font-size: 20px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.together-sidebar-account:hover {
  color: var(--educrat-theme-color);
}

.top-wrapper-menu {
  position: relative;
  display: inline-block;
}

.top-wrapper-menu:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 15px;
}

.top-wrapper-menu .space {
  margin: 0 7px;
  opacity: 0.2;
  filter: alpha(opacity=20);
}

.top-wrapper-menu .inner-top-menu {
  margin-top: 15px;
  padding: 20px 30px;
  position: absolute;
  top: 100%;
  right: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  background: #fff;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  z-index: 4;
  min-width: 250px;
  -webkit-box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
}

.top-wrapper-menu .inner-top-menu:before {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  content: '';
  position: absolute;
  top: -5px;
  right: 15px;
  width: 10px;
  height: 10px;
  background-color: #ffffff;
}

.top-wrapper-menu:hover .inner-top-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
}

.top-wrapper-menu .header_customer_login {
  margin-top: 10px;
  position: absolute;
  top: 100%;
  right: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  min-width: 320px;
  z-index: 9;
}

.top-wrapper-menu:hover .header_customer_login {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
  background: #fff;
  -webkit-box-shadow: 0 5px 10px -5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 5px 10px -5px rgba(0, 0, 0, 0.15);
}

.top-wrapper-menu .infor-account .avatar-wrapper {
  width: 50px;
  height: 50px;
  overflow: hidden;
  background: #fff;
  border-radius: 16px;
  float: left;
  display: -webkit-flex;
  /* Safari */
  -webkit-align-items: center;
  /* Safari 7.0+ */
  display: flex;
  align-items: center;
}

.top-wrapper-menu .infor-account .avatar-wrapper img {
  margin: 0;
}

.top-wrapper-menu .infor-account .avatar-wrapper + .name-acount {
  padding-left: 10px;
  flex: 1;
  -webkit-box-flex: 1;
  -ms-flex: 1;
}

.top-wrapper-menu .btn-login {
  padding: 5px 30px;
  font-size: 0.9375rem;
  font-weight: 400;
  border-radius: 8px;
}

.header_customer_login {
  padding: 1.875rem;
}

.header_customer_login .title {
  margin: 0 0 10px;
  font-size: 25px;
}

.header-default {
  background: #fff;
  padding: 25px 0;
}

.header-mobile {
  padding: 15px 0;
  -webkit-transition: all 0.1s ease 0s;
  -o-transition: all 0.1s ease 0s;
  transition: all 0.1s ease 0s;
  z-index: 5;
  background: #140342;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.main-sticky-header {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  top: 0;
}

.admin-bar .sticky-header {
  top: 30px;
}

.sticky-header {
  position: fixed !important;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-box-shadow: 0 5px 30px rgba(0, 22, 84, 0.1);
  box-shadow: 0 5px 30px rgba(0, 22, 84, 0.1);
  background: #fff;
}

.sticky-header.sticky-header-hidden {
  -webkit-transform: translateY(-110%);
  -ms-transform: translateY(-110%);
  -o-transform: translateY(-110%);
  transform: translateY(-110%);
}

.header_transparent .apus-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 7;
}

.header_transparent:not(.fix-header) .main-sticky-header:not(.sticky-header) section.elementor-element:not(.no-transparent) {
  background: transparent !important;
}

.header_transparent:not(.fix-header) .main-sticky-header:not(.sticky-header) section.elementor-element:not(.no-transparent) .apus-user-register,
.header_transparent:not(.fix-header) .main-sticky-header:not(.sticky-header) section.elementor-element:not(.no-transparent) .apus-user-login {
  color: #fff;
}

.header_transparent:not(.fix-header) .no_keep_header section.elementor-element:not(.no-transparent) {
  background: transparent !important;
}

body.header_fixed .apus-header {
  position: fixed;
  z-index: 2;
  width: 100%;
  background: #fff;
  top: 0;
  left: 0;
}

.header-button-woo > div {
  margin-left: 25px;
}

.header-button-woo > div:last-child {
  margin-left: 0;
}

.header-sidebar {
  position: fixed;
  width: 100px;
  z-index: 91;
  left: 0;
  top: 0;
  min-height: 100vh;
  background: #000;
  color: #fff;
}

.header-sidebar a {
  color: #fff;
}

.header-sidebar a:hover, .header-sidebar a:active {
  color: var(--educrat-theme-color);
}

.header-sidebar .show-main-menu {
  width: 100px;
  height: 100px;
  line-height: 100px;
  border-width: 0 0 1px;
  border-color: #EDEDED;
  color: #fff;
  background: #000000;
}

.header-sidebar .apus-topcart {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px 0;
  border-top: 1px solid #333333;
}

.header-sidebar .apus-topcart .count {
  color: #fff;
  font-size: 12px;
  text-transform: uppercase;
}

.header-sidebar .apus-topcart .dropdown-menu {
  bottom: 0;
  top: inherit;
  left: 100%;
}

.header-sidebar .service {
  color: #999999;
  white-space: nowrap;
  position: absolute;
  top: 50%;
  z-index: 9;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 14px;
  left: 50px;
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
  transform-origin: 0 11px;
}

.header-sidebar .service > * {
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}

.header-sidebar .service p {
  margin: 0;
}

.over-dark {
  cursor: not-allowed;
  display: block;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 6;
  width: 100%;
  height: 100%;
  background: rgba(24, 24, 26, 0.7);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.over-dark.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

/*------------------------------------*\
    Breadcrumb
\*------------------------------------*/
.apus-breadcrumb {
  margin: 0 0 1.875rem;
  text-align: left;
  font-size: 0.9375rem;
}

.apus-breadcrumb .breadcrumb-title {
  margin: 0;
  font-weight: 300;
  font-size: 48px;
}

.apus-breadcrumb nav {
  text-align: left;
  line-height: 30px;
}

.apus-breadcrumb nav a {
  color: #000;
}

/*------------------------------------*\
    Content
\*------------------------------------*/
.apus-content {
  background: #fff;
}

/*------------------------------------*\
    Pagination
\*------------------------------------*/
.navigation {
  display: block;
  clear: both;
}

.pagination,
.pagination-links,
.apus-pagination {
  width: 100%;
  padding: 0.625rem 0;
  margin: 0;
  text-align: center;
  line-height: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
}

@media (min-width: 1200px) {
  .pagination,
  .pagination-links,
  .apus-pagination {
    padding: 1.875rem 0;
  }
}

.pagination li,
.pagination-links li,
.apus-pagination li {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}

.pagination li > span, .pagination li > a,
.pagination-links li > span,
.pagination-links li > a,
.apus-pagination li > span,
.apus-pagination li > a {
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  margin: 0 2px;
  display: inline-block;
  float: none;
  color: var(--educrat-link-color);
  border-radius: 50% !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  min-width: 35px;
  line-height: 35px;
  padding: 0 5px;
  border: 0;
  background: #fff;
  position: relative;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .pagination li > span, .pagination li > a,
  .pagination-links li > span,
  .pagination-links li > a,
  .apus-pagination li > span,
  .apus-pagination li > a {
    min-width: 45px;
    line-height: 45px;
  }
}

.pagination li > span:focus, .pagination li > span:hover, .pagination li > span.current, .pagination li > a:focus, .pagination li > a:hover, .pagination li > a.current,
.pagination-links li > span:focus,
.pagination-links li > span:hover,
.pagination-links li > span.current,
.pagination-links li > a:focus,
.pagination-links li > a:hover,
.pagination-links li > a.current,
.apus-pagination li > span:focus,
.apus-pagination li > span:hover,
.apus-pagination li > span.current,
.apus-pagination li > a:focus,
.apus-pagination li > a:hover,
.apus-pagination li > a.current {
  color: #fff;
  background: var(--educrat-theme-color);
}

.pagination > span, .pagination > a,
.pagination-links > span,
.pagination-links > a,
.apus-pagination > span,
.apus-pagination > a {
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  margin: 0 2px;
  display: inline-block;
  float: none;
  color: var(--educrat-link-color);
  border-radius: 50% !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  min-width: 35px;
  line-height: 35px;
  padding: 0 5px;
  border: 0;
  background: #fff;
  position: relative;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .pagination > span, .pagination > a,
  .pagination-links > span,
  .pagination-links > a,
  .apus-pagination > span,
  .apus-pagination > a {
    min-width: 45px;
    line-height: 45px;
  }
}

.pagination > span:focus, .pagination > span:hover, .pagination > span.current, .pagination > a:focus, .pagination > a:hover, .pagination > a.current,
.pagination-links > span:focus,
.pagination-links > span:hover,
.pagination-links > span.current,
.pagination-links > a:focus,
.pagination-links > a:hover,
.pagination-links > a.current,
.apus-pagination > span:focus,
.apus-pagination > span:hover,
.apus-pagination > span.current,
.apus-pagination > a:focus,
.apus-pagination > a:hover,
.apus-pagination > a.current {
  color: #fff;
  background: var(--educrat-theme-color);
}

.pagination ul.page-numbers,
.pagination-links ul.page-numbers,
.apus-pagination ul.page-numbers {
  margin: 0;
  padding: 0;
  list-style: none;
}

.pagination i,
.pagination-links i,
.apus-pagination i {
  font-weight: 400;
}

/*------------------------------------*\
    Footer
\*------------------------------------*/
.apus-footer-mobile {
  display: none;
}

.apus-footer {
  background: transparent;
  position: relative;
  color: var(--educrat-text-color);
  font-size: 0.9375rem;
  font-weight: 400;
}

.apus-footer a:not([class]) {
  color: var(--educrat-link-color);
}

.apus-footer a:not([class]):hover, .apus-footer a:not([class]):focus, .apus-footer a:not([class]):active {
  color: var(--educrat-link_hover_color);
}

.apus-header {
  background: transparent;
  position: relative;
  z-index: 3;
  color: var(--educrat-text-color);
  font-size: -0.0625rem;
  font-weight: 400;
}

/*------------------------------------*\
    Copyright
\*------------------------------------*/
.apus-copyright {
  color: var(--educrat-text-color);
  font-size: 0.9375rem;
  font-weight: 400;
  background: #ffffff;
  padding-top: 20px;
  padding-bottom: 20px;
  position: relative;
}

.apus-copyright a {
  color: var(--educrat-link-color);
}

.apus-copyright a:hover, .apus-copyright a:focus, .apus-copyright a:active {
  color: var(--educrat-theme-color);
}

/*------------------------------------*\
    Top bar
\*------------------------------------*/
.header-offcanvas {
  padding: 18px 5px;
  border-bottom: 1px solid #EDEDED;
}

.header-offcanvas-bottom {
  padding: 20px;
  border-top: 1px solid #EDEDED;
  font-size: 15px;
  line-height: 32px;
}

.header-offcanvas-bottom .title {
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 5px;
}

.header-offcanvas-bottom aside + aside {
  margin-top: 15px;
}

.header-offcanvas-bottom .apus_socials {
  margin-left: -15px;
}

.apus-offcanvas {
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 330px;
  background: #fff;
  height: 100vh;
  overflow-x: auto;
  display: -webkit-flex;
  /* Safari */
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
}

.apus-offcanvas .apus-offcanvas-body {
  position: relative;
  height: 100%;
}

.apus-offcanvas .offcanvas-bottom,
.apus-offcanvas .offcanvas-top {
  height: 20%;
}

.apus-offcanvas .offcanvas-middle {
  height: 60%;
  padding: 20px 0;
  overflow-x: hidden;
}

.apus-offcanvas.active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
  -webkit-box-shadow: 2px 0 5px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 2px 0 5px 0 rgba(0, 0, 0, 0.15);
}

.apus-offcanvas .elementor-column {
  width: 100% !important;
}

.apus-offcanvas .elementor-column .elementor-column-wrap, .apus-offcanvas .elementor-column .elementor-widget-wrap {
  padding: 0 !important;
}

.apus-offcanvas .elementor-column .widget {
  margin-bottom: 10px;
}

@media (max-width: 991px) {
  .topbar-mobile {
    padding: 10px;
  }
  .topbar-mobile .btn {
    margin-right: 10px;
    padding: 6px 10px;
  }
  .topbar-mobile .top-cart .dropdown-menu {
    left: 0;
    right: inherit;
  }
  .topbar-mobile .top-cart .dropdown-menu:after, .topbar-mobile .top-cart .dropdown-menu:before {
    display: none;
  }
}

.open-text {
  color: #27b737;
}

.close-text {
  color: #f33066;
}

#mobile-offcanvas-sidebar {
  position: fixed;
  z-index: 999;
  top: 0px;
  width: 270px;
  height: 100%;
  max-width: 80%;
  background: #fff;
}

#mobile-offcanvas-sidebar.mobile-offcanvas-left {
  left: 0;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
}

#mobile-offcanvas-sidebar.mobile-offcanvas-left > .mobile-sidebar-btn {
  left: 100%;
}

#mobile-offcanvas-sidebar.mobile-offcanvas-right {
  right: 0;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}

#mobile-offcanvas-sidebar.mobile-offcanvas-right > .mobile-sidebar-btn {
  right: 100%;
}

#mobile-offcanvas-sidebar .mobile-sidebar-wrapper {
  display: none;
  height: 100%;
  width: 100%;
  padding: 0 15px;
}

#mobile-offcanvas-sidebar.active > .mobile-sidebar-wrapper {
  display: block;
}

#mobile-offcanvas-sidebar > .mobile-sidebar-btn {
  position: absolute;
  top: 100px;
}

.mobile-sidebar-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -5;
  width: 100%;
  height: 100%;
  background: rgba(34, 34, 34, 0.6);
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
}

.mobile-sidebar-panel-overlay.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  z-index: 5;
  cursor: not-allowed;
}

.apus-footer-mobile {
  position: fixed;
  z-index: 999;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
}

.apus-footer-mobile .footer-search-mobile {
  position: absolute;
  z-index: 999;
  left: 0;
  top: -60px;
  width: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-footer-mobile .footer-search-mobile.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-footer-mobile > ul {
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.apus-footer-mobile > ul > li {
  text-transform: uppercase;
  display: inline-block;
  padding: 0 25px;
  text-align: center;
  position: relative;
}

.apus-footer-mobile > ul > li span {
  display: block;
  font-size: 10px;
  line-height: 1;
}

.apus-footer-mobile > ul > li .wishlist-icon, .apus-footer-mobile > ul > li .mini-cart {
  line-height: 1.8;
}

.apus-footer-mobile > ul > li .wrapper-morelink {
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  position: absolute;
  right: 0;
  bottom: 40px;
  padding: 20px;
  background: #fff;
  -webkit-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
}

.apus-footer-mobile > ul > li .wrapper-morelink .footer-morelink {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 10px;
}

.apus-footer-mobile > ul > li .wrapper-morelink li {
  padding: 3px 0;
  white-space: nowrap;
  display: block;
  width: 100%;
  text-align: left;
}

.apus-footer-mobile > ul > li .wrapper-morelink.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-footer-mobile .mini-cart i {
  font-size: 15px;
}

.apus-footer-mobile .mini-cart .count {
  top: 2px;
}

.apus-footer-mobile .apus-search-form {
  min-width: 300px;
  padding: 10px 30px;
  background: rgba(255, 255, 255, 0.9);
}

.apus-footer-mobile .apus-search-form .select-category {
  display: none;
}

.apus-footer-mobile .apus-search-form form {
  border: none;
  margin: 0;
}

.apus-footer-mobile .apus-search-form form .form-control {
  border: 1px solid #EDEDED;
}

.close-sidebar-btn,
.mobile-sidebar-btn {
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 14px;
  display: inline-block;
}

.mobile-sidebar-btn {
  padding: 8px;
  margin: 0;
  color: #fff !important;
  background: var(--educrat-theme-color);
  position: fixed;
  top: 30%;
  z-index: 6;
  font-size: 18px;
  line-height: 1;
  opacity: 0.7;
  filter: alpha(opacity=70);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.mobile-sidebar-btn:hover, .mobile-sidebar-btn:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.mobile-sidebar-btn.btn-left {
  right: 0;
  border-radius: 4px 0 0 4px;
}

.mobile-sidebar-btn.btn-right {
  border-radius: 0 4px 4px 0;
  left: 0;
}

.close-sidebar-btn:active, .close-sidebar-btn:hover {
  color: #f33066;
}

.close-sidebar-btn {
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-bottom: 1px solid #EDEDED;
  width: 100%;
  text-align: center;
  color: #f33066;
}

@media (max-width: 991px) {
  .sidebar-wrapper:not(.sidebar-course-single) .sidebar {
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    z-index: 8;
    top: 0px;
    width: 330px;
    height: 100vh;
    max-width: 90%;
    position: fixed;
    padding: 15px;
    background: #fff;
    overflow-y: auto;
  }
  .sidebar-wrapper:not(.sidebar-course-single) .sidebar.sidebar-left {
    left: 0;
    -webkit-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    -o-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  .sidebar-wrapper:not(.sidebar-course-single) .sidebar.sidebar-left.active {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
  .sidebar-wrapper:not(.sidebar-course-single) .sidebar.sidebar-right {
    right: 0;
    -webkit-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
  }
  .sidebar-wrapper:not(.sidebar-course-single) .sidebar.sidebar-right.active {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
}

.apus-header .wrapper-topmenu:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 10px;
  background: transparent;
  z-index: 9;
}

.apus-header .wrapper-topmenu .dropdown-menu-right {
  top: calc(100% + 10px);
}

.apus-topbar .wrapper-topmenu:hover > a {
  color: #fff;
}

.wrapper-top-cart .overlay-dropdown-menu-right {
  position: fixed;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  z-index: 98;
}

.wrapper-top-cart .overlay-dropdown-menu-right.active {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}

.wrapper-top-cart > .dropdown-menu-right {
  max-width: 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
  position: fixed;
  z-index: 999;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  top: 0;
  right: 0;
  background: #fff;
  width: 420px;
  height: 100%;
  padding: 1.875rem;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}

.wrapper-top-cart > .dropdown-menu-right .widget_shopping_cart_heading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex: 0 0 auto;
  -webkit-flex: 0 0 auto;
}

.wrapper-top-cart > .dropdown-menu-right .widget_shopping_cart_heading > h3 {
  margin: 0 0 20px;
  font-size: 22px;
  padding: 0 0 20px;
  border-bottom: 1px solid #EDEDED;
  width: 100%;
  cursor: pointer;
  color: #f33066;
}

.wrapper-top-cart > .dropdown-menu-right .widget_shopping_cart_content_wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex: 1 1 auto;
  -webkit-flex: 1 1 auto;
  overflow-x: hidden;
  overflow-y: auto;
}

.wrapper-top-cart > .dropdown-menu-right .shopping_cart_content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
  height: 100%;
}

.wrapper-top-cart > .dropdown-menu-right .shopping_cart_content .cart_list {
  flex: 1 1 auto;
  -webkit-flex: 1 1 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
}

.wrapper-top-cart > .dropdown-menu-right .shopping_cart_content .cart-bottom {
  flex-direction: column;
  -webkit-flex-direction: column;
  flex: 0 0 auto;
  -webkit-flex: 0 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}

.wrapper-top-cart > .dropdown-menu-right.active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.wrapper-top-cart .cart_list .variation {
  margin: 0;
}

.wrapper-top-cart .cart_list .variation > * {
  display: inline-block;
  vertical-align: middle;
}

.wrapper-top-cart .cart_list .variation > * p {
  margin: 0;
}

.wrapper-top-cart .buttons .btn-block {
  margin-bottom: 10px;
}

/* 5. menu */
.apus-megamenu {
  padding: 0;
}

.megamenu {
  padding: 0;
  float: none;
}

.megamenu .menu-item-description {
  font-size: 14px;
  text-transform: capitalize;
}

.megamenu > li {
  display: inline-block;
  padding: 0;
  margin: 0 5px 0 0;
  vertical-align: top;
  float: none;
  position: relative;
}

.megamenu > li:before {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 20px;
  top: 100%;
  left: 0;
}

.megamenu > li:last-child {
  margin: 0;
}

.megamenu > li > a {
  display: inline-block;
  font-size: 0.9375rem;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
  text-transform: capitalize;
  padding: 7px 15px;
  border-radius: 8px;
  background: transparent;
}

.megamenu > li > a .fa, .megamenu > li > a img {
  max-width: 50px;
  margin-left: 3px;
}

.megamenu > li > a:hover, .megamenu > li > a:active, .megamenu > li > a:focus {
  color: var(--educrat-link_hover_color);
  background: rgba(255, 255, 255, 0.15);
}

.megamenu > li:hover > a, .megamenu > li.active > a {
  color: var(--educrat-link_hover_color);
  background: rgba(255, 255, 255, 0.15);
}

.megamenu > li.aligned-left > .dropdown-menu {
  left: 0;
}

.megamenu > li.aligned-right > .dropdown-menu {
  left: auto;
  right: 0;
}

.megamenu > li > .dropdown-menu {
  min-width: 240px;
  margin-top: 18px;
}

.megamenu > li > .dropdown-menu:before {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  background-color: #ffffff;
  content: "";
  width: 10px;
  height: 10px;
  left: 20px;
  top: -5px;
  position: absolute;
}

.megamenu > li .dropdown-toggle::after {
  content: "\e64b";
  font-family: 'themify';
  border: 0;
  font-size: 11px;
  vertical-align: middle;
  margin-left: 3px;
}

.megamenu .dropdown-menu {
  border-radius: 8px !important;
  -webkit-box-shadow: 0px 25px 70px 0px rgba(1, 33, 58, 0.07);
  box-shadow: 0px 25px 70px 0px rgba(1, 33, 58, 0.07);
  padding: 15px 1.875rem;
  border: 0;
}

.megamenu .dropdown-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--educrat-theme-color);
}

.megamenu .dropdown-menu .text-label.label-hot {
  color: #f33066;
}

.megamenu .dropdown-menu .text-label.label-new {
  color: #27b737;
}

.megamenu .dropdown-menu .current-menu-item > a {
  color: #fff;
}

.megamenu .dropdown-menu li > a {
  -webkit-transition: all 0.25s ease-in-out 0s;
  -o-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  background: transparent !important;
  position: relative;
  text-transform: capitalize;
  padding: 6px 0;
  width: 100%;
  display: inline-block;
  font-size: 0.9375rem;
  color: var(--educrat-link-color);
  white-space: nowrap;
}

.megamenu .dropdown-menu li > a:hover, .megamenu .dropdown-menu li > a:active {
  color: var(--educrat-link_hover_color);
  text-decoration: underline;
}

.megamenu .dropdown-menu li > a b {
  display: none;
}

.megamenu .dropdown-menu li > a:after {
  margin: 0;
  position: absolute;
  top: 11px;
  right: 10px;
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

.megamenu .dropdown-menu li:hover > a, .megamenu .dropdown-menu li.current-menu-item > a, .megamenu .dropdown-menu li.open > a, .megamenu .dropdown-menu li.active > a {
  color: var(--educrat-link_hover_color);
  text-decoration: underline;
}

.megamenu .dropdown-menu .widget-title,
.megamenu .dropdown-menu .widgettitle {
  margin: 0 0 10px;
  font-size: 17px;
  padding: 0;
  text-align: left;
}

.megamenu .dropdown-menu .widget-title:before, .megamenu .dropdown-menu .widget-title:after,
.megamenu .dropdown-menu .widgettitle:before,
.megamenu .dropdown-menu .widgettitle:after {
  display: none;
}

.megamenu .dropdown-menu .dropdown-menu {
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  transform-origin: 0 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: rotateX(-90deg);
  -ms-transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  transform: rotateX(-90deg);
  -webkit-box-shadow: 0px 25px 70px 0px rgba(1, 33, 58, 0.07);
  box-shadow: 0px 25px 70px 0px rgba(1, 33, 58, 0.07);
  position: absolute;
  display: block;
  left: 100%;
  top: -15px;
  background: #fff;
  min-width: 225px;
  margin: 0;
}

.megamenu .dropdown-menu li:hover > .dropdown-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -o-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.megamenu .dropdown-menu .widget-nav-menu .menu li {
  margin: 0;
}

.megamenu .dropdown-menu .widget-nav-menu .menu li a {
  padding: 6px 0;
}

.megamenu .dropdown-menu .widget-nav-menu .menu li a:before {
  display: none;
}

.megamenu .apus-container {
  padding-right: 15px;
  padding-left: 15px;
  width: 100%;
}

.megamenu li.aligned-fullwidth > .dropdown-menu {
  padding: 0;
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.megamenu li.aligned-fullwidth > .dropdown-menu:before {
  left: 45px;
}

.megamenu > li > a > .text-label {
  font-size: 11px;
  padding: 0px 5px;
  background: #0dcaf0;
  color: #fff;
  position: absolute;
  right: -15px;
  top: -10px;
  line-height: 2;
  display: inline-block;
  text-transform: capitalize;
  border-radius: 2px;
}

.megamenu > li > a > .text-label.label-hot {
  background: #f33066;
}

.megamenu > li > a > .text-label.label-hot:before {
  border-color: #f33066 transparent transparent #f33066;
}

.megamenu > li > a > .text-label.label-new {
  background: #27b737;
}

.megamenu > li > a > .text-label.label-new:before {
  border-color: #27b737 transparent transparent #27b737;
}

.megamenu > li > a > .text-label:before {
  content: '';
  position: absolute;
  z-index: 9;
  top: 100%;
  letter-spacing: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: #0dcaf0 transparent transparent #0dcaf0;
}

.megamenu.effect1 > li > .dropdown-menu {
  display: block;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  transform-origin: 0 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: rotateX(-90deg);
  -ms-transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  transform: rotateX(-90deg);
  position: absolute;
  top: 100%;
}

.megamenu.effect1 > li:hover > .dropdown-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -o-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.megamenu.effect2 > li > .dropdown-menu {
  display: block;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  transform-origin: 0 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: rotateX(-90deg);
  -ms-transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  transform: rotateX(-90deg);
  position: absolute;
  top: 100%;
  margin-top: 10px;
}

.megamenu.effect2 > li > .dropdown-menu > li {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(5px);
  -ms-transform: translateY(5px);
  -o-transform: translateY(5px);
  transform: translateY(5px);
}

.megamenu.effect2 > li:hover > .dropdown-menu {
  margin-top: 0;
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -o-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.megamenu.effect2 > li:hover > .dropdown-menu > li {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
}

.megamenu.effect3 > li > .dropdown-menu {
  display: block;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-box-shadow: none;
  box-shadow: none;
  position: absolute;
  top: 100%;
  -webkit-animation: fadeleft 0.3s ease-in-out 0s;
  animation: fadeleft 0.3s ease-in-out 0s;
}

.megamenu.effect3 > li:hover > .dropdown-menu {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
  -webkit-animation: faderight 0.3s ease-in-out 0s;
  animation: faderight 0.3s ease-in-out 0s;
}

.navbar-offcanvas {
  padding: 0;
  font-size: 1rem;
  display: block;
}

.navbar-offcanvas .sliding-menu__panel {
  padding: 0;
  margin: 0;
}

.navbar-offcanvas .sliding-menu {
  margin: 20px;
}

.navbar-offcanvas .sliding-menu li a, .navbar-offcanvas .sliding-menu li .sliding-menu__nav {
  font-size: 0.9375rem;
  font-weight: 400;
  background-color: #fff;
  color: var(--educrat-link-color);
  padding: 10px 20px;
  border-radius: 8px;
}

.navbar-offcanvas .sliding-menu li .sliding-menu__back {
  padding: 18px 20px;
  margin-bottom: 12px;
  font-size: 1rem;
  font-weight: 500;
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
}

.navbar-offcanvas .sliding-menu li .sliding-menu__back:before {
  margin-left: 0;
  content: "\e64a";
}

.navbar-offcanvas .sliding-menu .sliding-menu__nav::before {
  font-family: 'themify';
  content: "\e649";
  font-size: 0.8125rem;
  font-weight: 700;
}

.navbar-offcanvas .sliding-menu .active .sliding-menu__nav,
.navbar-offcanvas .sliding-menu .active a {
  color: var(--educrat-theme-color);
}

.navbar-offcanvas .sliding-menu__panel-root > li > .sliding-menu__nav,
.navbar-offcanvas .sliding-menu__panel-root > li > a {
  padding: 18px 20px;
  font-size: 1rem;
  font-weight: 500;
}

.navbar-offcanvas .sliding-menu__panel-root > li.active > .sliding-menu__nav,
.navbar-offcanvas .sliding-menu__panel-root > li.active > a {
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
}

.navbar-offcanvas .dropdown-menu {
  margin: 0;
}

.navbar-offcanvas .dropdown-menu > li a {
  background: transparent !important;
}

.navbar-offcanvas .dropdown-menu > li.active > a,
.navbar-offcanvas .dropdown-menu > li > a:hover,
.navbar-offcanvas .dropdown-menu > li > a:focus {
  color: var(--educrat-link-color);
  text-decoration: underline;
}

.navbar-offcanvas .dropdown-menu [class*="col-sm"] {
  width: 100%;
}

.navbar-offcanvas .dropdown-menu .dropdown-menu-inner {
  padding: 0 1.875rem;
}

.navbar-offcanvas .dropdown-menu .widgettitle {
  font-weight: 500;
  margin: 0 0 10px;
}

.navbar-offcanvas .dropdown-menu .dropdown-menu {
  left: 100%;
  top: 0;
}

.navbar-offcanvas li:hover .dropdown-menu {
  display: block;
}

.navbar-offcanvas .aligned-fullwidth > .dropdown-menu {
  width: 100%;
}

.mobile-vertical-menu .navbar-nav li {
  border-bottom: 1px dashed #EDEDED;
}

.mobile-vertical-menu .navbar-nav li:last-child {
  border-bottom: 0;
}

.mobile-vertical-menu .navbar-nav li > a {
  padding: 5px 0;
}

.mobile-vertical-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--educrat-theme-color);
  font-family: var(--educrat-heading-font);
}

.mobile-vertical-menu .text-label.label-hot {
  color: #f33066;
}

.mobile-vertical-menu .text-label.label-new {
  color: #27b737;
}

#apus-mobile-menu .btn-toggle-canvas {
  color: #f33066;
  font-size: 1rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
}

#apus-mobile-menu .btn-toggle-canvas:hover, #apus-mobile-menu .btn-toggle-canvas:active {
  color: #f33066;
}

#apus-mobile-menu .offcanvas-head strong {
  margin: 0 5px;
}

#apus-mobile-menu .widget-nav-menu .menu li {
  margin-bottom: 5px;
}

.main-mobile-menu {
  float: none;
}

.main-mobile-menu > li {
  float: none;
}

.main-mobile-menu .has-submenu > .sub-menu {
  padding-left: 1.875rem;
  list-style: none;
  display: none;
}

.main-mobile-menu .has-submenu > .sub-menu li > .icon-toggle {
  top: 1px;
  padding: 0 5px;
}

.main-mobile-menu .has-submenu > .sub-menu li a {
  font-size: 15px;
  padding: 2px 0;
}

.main-mobile-menu .widget .widget-title, .main-mobile-menu .widget .widgettitle, .main-mobile-menu .widget .widget-heading {
  margin: 0 0 10px;
  font-size: 16px;
  padding: 0 0 8px;
  text-align: inherit;
}

.main-mobile-menu .sub-menu {
  max-width: 100%;
}

.main-mobile-menu .shop-list-small {
  margin-bottom: 10px;
}

.main-mobile-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--educrat-theme-color);
  font-family: var(--educrat-heading-font);
}

.main-mobile-menu .text-label.label-hot {
  color: #f33066;
}

.main-mobile-menu .text-label.label-new {
  color: #27b737;
}

.menu-setting-menu-container .apus-menu-top {
  margin: 0;
  padding: 0;
  list-style: none;
  line-height: 2;
}

.menu-setting-menu-container .apus-menu-top li a {
  padding: 0 15px;
  width: 100%;
}

.menu-setting-menu-container .apus-menu-top ul {
  padding-left: 15px;
}

.wrapper-topmenu .dropdown-menu {
  border-radius: 0;
}

.topmenu-menu {
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
}

.topmenu-menu i {
  margin-right: 10px;
  display: inline-block;
  vertical-align: middle;
}

@media (min-width: 1200px) {
  .topmenu-menu i {
    margin-right: 15px;
    font-size: 18px;
  }
}

.topmenu-menu > li {
  float: none;
  white-space: nowrap;
  margin-bottom: 2px;
  font-size: 0.9375rem;
  font-weight: 500;
}

.topmenu-menu > li > a {
  background: transparent;
  padding: 6px 15px;
  display: inline-block;
  width: 100%;
  border-radius: 12px;
}

@media (min-width: 1200px) {
  .topmenu-menu > li > a {
    padding: 9px 20px;
  }
}

.topmenu-menu > li > a:hover, .topmenu-menu > li > a:focus {
  color: #fff;
  background: #1A064F;
}

.topmenu-menu > li.active > a {
  color: #fff;
  background: #1A064F;
}

.mm-menu {
  background: #fff;
  color: var(--educrat-text-color);
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  border: none;
  border-radius: 0;
}

.mm-menu .mm-listview .mm-next::before {
  border: none;
}

.mm-menu .elementor-column-wrap {
  padding: 0 !important;
}

.mm-menu .mm-panel {
  width: 100% !important;
}

.mm-menu .mm-panel .dropdown-menu-inner {
  padding-top: 20px;
  margin: -20px -20px 20px;
}

.mm-menu .menu li {
  line-height: 50px;
  margin: 0 !important;
  border-bottom: 1px solid #EDEDED;
}

.mm-menu .menu li:last-child {
  border-bottom: 0;
}

.mm-menu .menu li a {
  padding: 0 !important;
}

.mm-menu .menu li a:before {
  display: none !important;
}

.mm-menu .menu li.active > a {
  color: var(--educrat-theme-color);
}

.mm-menu .elementor-widget-wrap {
  padding-right: 20px !important;
  padding-left: 20px !important;
}

.mm-menu .mm-listview > li > a {
  color: var(--educrat-link-color);
  background: transparent !important;
  line-height: 2.2;
}

.mm-menu .mm-listview > li > a:hover, .mm-menu .mm-listview > li > a:focus {
  color: var(--educrat-theme-color);
}

.mm-menu .mm-listview > li > a .text-label {
  font-size: 11px;
  padding: 0px 5px;
  background: #0dcaf0;
  position: absolute;
  right: 50px;
  top: 0;
  line-height: 2;
  display: inline-block;
  text-transform: capitalize;
  border-radius: 2px;
}

.mm-menu .mm-listview > li > a .text-label.label-hot {
  background: #f33066;
}

.mm-menu .mm-listview > li > a .text-label.label-hot:before {
  border-color: #f33066 transparent transparent #f33066;
}

.mm-menu .mm-listview > li > a .text-label.label-new {
  background: #27b737;
}

.mm-menu .mm-listview > li > a .text-label.label-new:before {
  border-color: #27b737 transparent transparent #27b737;
}

.mm-menu .mm-listview > li > a .text-label:before {
  content: '';
  position: absolute;
  z-index: 9;
  top: 100%;
  left: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: #0dcaf0 transparent transparent #0dcaf0;
}

.mm-menu .mm-listview .menu-item-description {
  font-size: 12px;
}

.mm-menu .mm-listview > li:after {
  display: none;
}

.mm-menu .mm-listview > li .mm-next:after {
  border-color: var(--educrat-link-color);
}

.mm-menu .mm-listview > li .mm-next:hover:after, .mm-menu .mm-listview > li .mm-next:focus:after {
  border-color: var(--educrat-theme-color);
}

.mm-menu .mm-listview > li.active > a {
  color: var(--educrat-theme-color);
}

.mm-menu .mm-listview > li.active > .mm-next:after {
  border-color: var(--educrat-theme-color);
}

.mm-menu .mm-btn:before {
  border-color: var(--educrat-link-color);
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
}

.mm-menu .mm-btn:hover:before, .mm-menu .mm-btn:focus:before {
  border-color: var(--educrat-theme-color);
}

.mm-menu .mm-title {
  background: #E6E9EC;
  padding: 15px 0;
  font-weight: 600;
  font-size: 16px;
  height: auto;
  color: var(--educrat-link-color) !important;
}

.mm-menu .mm-navbar {
  padding: 0;
}

.mm-menu .mm-navbar .mm-btn {
  top: 7px;
}

.mm-menu .widget .widget-title, .mm-menu .widget .widgettitle, .mm-menu .widget .widget-heading {
  border: none;
  padding: 0;
  margin-bottom: 10px;
}

.mm-menu .widget .widget-title:before, .mm-menu .widget .widgettitle:before, .mm-menu .widget .widget-heading:before {
  display: none;
}

.mm-menu li.text-title {
  font-weight: 700;
  font-size: 15px;
  padding: 15px;
  color: var(--educrat-link-color);
  text-align: center;
  border: 0 !important;
}

.mm-menu li.text-title ~ li {
  font-size: 13px;
  padding-left: 20px;
}

.mm-menu li.text-title ~ li a {
  padding: 7px;
  text-transform: capitalize !important;
}

.mm-menu li.text-title ~ li i {
  margin-right: 7px;
}

.mm-panels > .mm-panel > .mm-listview {
  padding-top: 12px;
  font-size: 13px;
  padding-bottom: 40px;
}

.mm-panels > .mm-panel > .mm-listview > li {
  border-bottom: 1px solid #EDEDED;
}

.mm-panels > .mm-panel > .mm-listview > li:last-child {
  border-bottom: 0;
}

.mm-panels > .mm-panel > .mm-listview > li.space-20 {
  border: none;
}

.mobile-submit {
  display: block;
  position: absolute;
  z-index: 1;
  width: 100%;
  background: #fff;
  padding: 15px;
  bottom: 0;
  left: 0;
}

.top-menu > li > a {
  padding: 0 15px;
  text-transform: capitalize;
}

#mm-blocker {
  z-index: 999990;
  background-color: rgba(13, 38, 59, 0.7);
}

.mm-menu.mm-offcanvas {
  z-index: 999991;
  max-width: 65%;
}

.mm-menu.mm-offcanvas .social-top:after {
  display: none;
}

.mm-menu.mm-offcanvas .social-top a {
  display: inline-block;
  font-size: 16px;
}

.mm-menu.mm-offcanvas .social-top a:hover, .mm-menu.mm-offcanvas .social-top a:active {
  color: var(--educrat-theme-color);
}

.mm-menu.mm-offcanvas .widget {
  margin: 0;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper {
  padding: 10px;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper > * {
  margin-bottom: 15px;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper > *:last-child {
  margin: 0;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper:after {
  display: none;
}

.mm-menu.mm-offcanvas .woocommerce-currency-switcher-form ul.dd-options {
  margin-top: 0;
}

html.mm-opening .mm-menu ~ .mm-slideout {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}

.mm-listview .mm-next {
  padding: 0 !important;
}

.mm-menu.mm-offcanvas {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  border-right: 1px solid #EDEDED;
}

@media (max-width: 1200px) {
  .mm-menu.mm-offcanvas {
    display: block;
  }
}

.mm-menu.mm-offcanvas.mm-opened {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

#mm-blocker {
  cursor: not-allowed;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li > a {
  font-weight: 400;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li > a i {
  margin-right: 5px;
  min-width: 20px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .fa-minus {
  color: var(--educrat-theme-color);
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .sub-menu {
  max-width: 100%;
  display: none;
  padding: 0 15px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widgettitle,
.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widget-title {
  padding: 0;
  border: none;
  margin: 0 0 10px;
  font-size: 16px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widgettitle:before, .mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widgettitle:after,
.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widget-title:before,
.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widget-title:after {
  display: none;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .dropdown-menu-inner {
  padding-left: 20px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .menu li a {
  padding: 0;
  font-size: 14px;
}

.mobile-vertical-menu .widget {
  margin-bottom: 10px;
}

.wrapper-menu-dashboard {
  -webkit-transition: all 0.1s ease 0s;
  -o-transition: all 0.1s ease 0s;
  transition: all 0.1s ease 0s;
  background-color: #1D293E;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 3;
}

.menu-dashboard {
  overflow-x: auto;
}

.menu-dashboard a {
  display: inline-block;
  white-space: nowrap;
  width: 100%;
  background-color: transparent;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 1rem;
  padding: 10px;
  color: #fff !important;
}

@media (min-width: 1200px) {
  .menu-dashboard a {
    padding: 15px 10px;
  }
}

.menu-dashboard a i {
  display: inline-block;
  margin-right: 7px;
  font-size: 18px;
}

.menu-dashboard li {
  flex: 1 1 auto;
  text-align: center;
  margin-right: 2px;
}

.menu-dashboard li:last-child {
  margin-right: 0;
}

.menu-dashboard li:hover > a, .menu-dashboard li.active > a {
  background-color: rgba(255, 255, 255, 0.15);
}

/* 6. pages */
.home-page-default {
  padding-top: 1.875rem;
  padding-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .home-page-default {
    padding-top: 70px;
    padding-bottom: 70px;
  }
}

.main-page .page-links {
  clear: both;
  overflow: hidden;
  padding: 1.875rem 0;
  margin: 0;
}

.main-page #comments {
  padding-top: 30px;
  clear: both;
}

.main-content {
  padding-top: 0.9375rem;
  padding-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .main-content {
    padding-top: 1.875rem;
    padding-bottom: 80px;
  }
}

.main-content.only_main .mobile-sidebar-btn {
  display: none;
}

body.no-footer #apus-footer {
  display: none !important;
}

div.wpcf7-validation-errors {
  margin: 0;
  padding: 15px;
}

.contact-form-content {
  padding: 1.875rem;
  background: white;
  min-height: 260px;
}

.contact-form-content .rounded {
  margin-right: 10px;
  color: #fff;
  width: 40px;
  height: 40px;
  background: #cccccc;
}

.contact-form-content .rounded .fa, .contact-form-content .rounded .icon {
  font-size: 16px;
  margin: 13px;
}

.page-404 {
  position: relative;
  background-color: #FEFBF4;
}

.page-404 .not-found {
  padding: 50px 0;
}

@media (min-width: 1200px) {
  .page-404 .not-found {
    padding: 150px 0;
  }
}

.page-404 .description {
  font-size: 1rem;
}

.page-404 .title-404 {
  font-size: 25px;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .page-404 .title-404 {
    font-size: 35px;
    margin: 0 0 20px;
  }
}

.page-404 .page-content {
  margin-top: 25px;
}

.page-404 .top-image {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .page-404 .top-image {
    margin-bottom: 40px;
  }
}

@media (min-width: 1200px) {
  .page-404 .content-inner {
    padding-left: 90px;
  }
}

.top_profile {
  padding: 30px 30px 25px;
  border-bottom: 1px solid #dee6ed;
}

.top_profile .user-logo {
  margin: 0 0 25px;
}

.top_profile .logo-inner {
  width: 150px;
  height: 150px;
  padding: 5px;
  border-radius: 50%;
  background: #fff;
  overflow: hidden;
  margin: auto;
}

.top_profile .title {
  font-size: 18px;
  margin: 0;
}

.top_profile .categories a {
  color: var(--educrat-text-color);
}

.top_profile .categories a:hover, .top_profile .categories a:focus {
  color: var(--educrat-link-color);
}

@media (min-width: 992px) {
  .main-page-sidebar.left-main {
    padding-left: 300px;
  }
  .main-page-sidebar.left-main .sidebar-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 300px;
  }
  .main-page-sidebar.left-main .main-page {
    width: 100%;
    border-left: 1px solid #EDEDED;
  }
  .main-page-sidebar.main-right {
    padding-right: 300px;
  }
  .main-page-sidebar.main-right .sidebar-wrapper {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
  }
  .main-page-sidebar.main-right .main-page {
    width: 100%;
    border-right: 1px solid #EDEDED;
  }
  .main-page-sidebar .sidebar-wrapper {
    padding-top: 1.875rem;
  }
}

.main-page-sidebar .main-page {
  padding: 0.9375rem;
}

@media (min-width: 1200px) {
  .main-page-sidebar .main-page {
    padding: 1.875rem;
  }
}

.main-page-sidebar .main-page.st-dark {
  border-color: #FFFFFF1C;
}

/* 7. post */
/*
* General Post Style using for all with naming class entry
*/
.post.no-results {
  text-align: center;
  margin: 0 0 30px;
}

.post.no-results .widget_search {
  margin: 25px auto 0;
  max-width: 600px;
}

.post.no-results .title-no-results {
  color: var(--educrat-text-color);
  margin: 0 0 10px;
  color: var(--educrat-link-color);
  font-size: 25px;
}

@media (min-width: 1200px) {
  .post.no-results .title-no-results {
    font-size: 30px;
  }
}

@media (min-width: 1200px) {
  .post.no-results {
    margin: 0 0 50px;
  }
}

.entry-title {
  margin: 0;
  word-wrap: break-word;
  word-break: break-word;
  font-size: 18px;
  font-weight: 500;
}

@media (min-width: 1200px) {
  .entry-title {
    font-size: 20px;
  }
}

.detail-title {
  margin: 10px 0;
  font-size: 28px;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .detail-title {
    font-size: 40px;
  }
}

.entry-create {
  margin: 0 0 15px;
}

.entry-create > * {
  margin-right: 2px;
}

.entry-create .author {
  font-style: italic;
  text-transform: capitalize;
}

.comment-form-cookies-consent [type="checkbox"] {
  margin-right: 7px;
}

.entry-link {
  margin-top: 20px;
}

.entry-link .readmore {
  color: var(--educrat-theme-color);
  text-transform: capitalize;
  font-weight: 500;
  font-size: -1.0625rem;
}

.entry-link .readmore:hover {
  color: #000;
}

.entry-meta {
  margin: 0;
}

.entry-meta .fa, .entry-meta .icon {
  margin-right: 3px;
}

.wp-block-quote,
blockquote {
  margin: 0.9375rem 0;
  position: relative;
  border-radius: 0;
  font-weight: 400;
  background: #fff;
  font-size: 1.0625rem;
  font-style: italic;
  padding: 15px 15px 15px 50px;
  border: 0;
  border-left: 5px solid var(--educrat-theme-color);
  color: var(--educrat-link-color);
}

@media (min-width: 1200px) {
  .wp-block-quote,
  blockquote {
    padding: 28px 28px 28px 80px;
    margin: 1.875rem 0;
  }
}

.wp-block-quote cite,
blockquote cite {
  font-size: 18px;
  color: var(--educrat-link-color);
  display: inline-block;
  font-weight: 500;
  font-family: var(--educrat-heading-font);
  font-style: normal;
}

.wp-block-quote p,
blockquote p {
  margin-bottom: 12px;
}

.wp-block-quote p:last-child,
blockquote p:last-child {
  margin-bottom: 0;
}

.wp-block-quote:before,
blockquote:before {
  color: #e7edf2;
  font-size: 60px;
  line-height: 1;
  position: absolute;
  top: 15px;
  left: 5px;
}

@media (min-width: 1200px) {
  .wp-block-quote:before,
  blockquote:before {
    left: 10px;
    font-size: 90px;
  }
}

.entry-vote {
  z-index: 1;
  display: table;
  text-align: center;
  top: 20px;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  width: 44px;
  height: 44px;
  right: 20px;
}

.entry-vote .entry-vote-inner {
  color: #fff;
  display: table-cell;
  vertical-align: middle;
  font-weight: 700;
}

.entry-vote.vote-perfect .entry-vote-inner {
  color: #f33066;
}

.entry-vote.vote-good .entry-vote-inner {
  color: #ff9b20;
}

.entry-vote.vote-average .entry-vote-inner {
  color: #91e536;
}

.entry-vote.vote-bad .entry-vote-inner {
  color: #fd7e14;
}

.entry-vote.vote-poor .entry-vote-inner {
  color: #27b737;
}

.type-post {
  margin-bottom: 1.875rem;
}

.blog-title {
  margin-bottom: 1.875rem;
}

.layout-posts-list > .post {
  margin-bottom: 15px;
}

@media (min-width: 992px) {
  .layout-posts-list > .post {
    margin-bottom: 1.875rem;
  }
}

.categories-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.875rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  overflow: auto;
}

@media (min-width: 576px) {
  .categories-list {
    justify-content: center;
    margin-bottom: 50px;
  }
}

.categories-list li {
  margin: 0 2px;
}

.categories-list li a {
  display: inline-block;
  white-space: nowrap;
  font-weight: 500;
  padding: 6px 18px;
  color: var(--educrat-text-color);
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.categories-list li a:hover, .categories-list li a:focus, .categories-list li a.active {
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
}

.comment-form-theme {
  margin-bottom: 10px;
}

.comment-form-theme label {
  font-weight: 500;
  color: var(--educrat-link-color);
  margin: 0 0 5px;
}

.comment-form-theme .comment-form-cookies-consent {
  display: none;
  font-size: 14px;
}

.comment-form-theme .comment-form-cookies-consent label {
  margin-bottom: 0;
  display: inline;
}

.comment-form-theme .form-group {
  margin-bottom: 1.25rem;
  position: relative;
}

@media (min-width: 1200px) {
  .comment-form-theme .form-group {
    margin-bottom: 25px;
  }
}

.comment-form-theme .yourview {
  margin-bottom: 5px;
}

.comment-form-theme .form-control {
  margin: 0;
  border-width: 2px;
}

@media (min-width: 1200px) {
  .comment-form-theme .form-control {
    height: 55px;
  }
}

.comment-form-theme textarea.form-control {
  height: 150px;
  resize: none;
}

@media (min-width: 1200px) {
  .comment-form-theme textarea.form-control {
    height: 220px;
  }
}

.comment-form-theme .form-submit {
  margin-bottom: 0;
}

.comment-form-theme #cancel-comment-reply-link {
  color: #f33066;
}

.comment-form-theme .group-upload [class="hidden"] {
  display: none;
}

.comment-form-theme .group-upload button {
  border: 1px solid var(--educrat-theme-color);
  padding: 0.5rem 1rem;
  background-color: #fff;
  color: var(--educrat-theme-color);
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .comment-form-theme .group-upload button {
    padding: 1rem 2rem;
    min-width: 300px;
    text-align: center;
  }
}

.comment-form-theme .group-upload button:hover, .comment-form-theme .group-upload button:focus {
  background-color: var(--educrat-theme-color);
  color: #fff;
  border-color: var(--educrat-theme-color);
}

.comment-form-theme .group-upload button .upload-file-btn {
  margin-top: 5px;
}

.comment-form-theme .group-upload button i {
  margin-right: 5px;
}

/* Post type: List widget list*/
.posts-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.posts-list > li {
  margin: 0 0 20px;
}

.posts-list > li:last-child {
  margin: 0;
}

.posts-list .entry-title {
  line-height: 22px;
  font-weight: 500;
  font-size: 0.9375rem;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.posts-list .image {
  width: 80px;
  padding-right: 15px;
}

.posts-list .image + .inner {
  flex: 1;
  -webkit-box-flex: 1;
  -ms-flex: 1;
}

.posts-list .image .image-inner {
  max-height: 65px;
  overflow: hidden;
  border-radius: 8px;
}

.posts-list .date {
  font-size: 13px;
}

.post-layout {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  overflow: hidden;
}

.post-layout .post-sticky {
  background: #f33066;
  color: #fff;
  display: inline-block;
  padding: 0 15px;
  margin: 5px 0;
  font-size: 14px;
}

.post-layout .categories-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--educrat-theme-color);
  text-transform: uppercase;
}

.post-layout .entry-title {
  margin-top: 5px;
}

.post-layout .entry-title .stick-icon {
  display: inline-block;
  line-height: 1;
  margin-right: 5px;
}

.post-layout .top-image {
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  margin-bottom: 15px;
}

.post-layout .top-image img {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.post-layout .top-image .entry-thumb {
  margin: 0;
}

.post-layout iframe {
  max-width: 100%;
}

.post-layout .date {
  margin-top: 10px;
}

.post-layout:hover .top-image img {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.post-layout.sticky .entry-title a {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.post-grid .entry-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-grid.v2 .entry-title {
  font-size: 18px;
}

.blog-only-main.style-list {
  max-width: 1070px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1200px) {
  .has-sidebar .post-list-item .top-image {
    width: 400px;
  }
  .has-sidebar .post-list-item .top-image + .col-content {
    padding-left: 50px;
  }
}

.has-sidebar .post-list-item .btn-readmore {
  margin-top: 20px;
}

.post-list-item {
  margin-bottom: 40px;
}

@media (min-width: 1200px) {
  .post-list-item {
    margin-bottom: 50px;
  }
}

.post-list-item .top-image {
  width: 100%;
}

@media (min-width: 576px) {
  .post-list-item .top-image {
    width: 300px;
    margin: 0;
  }
}

@media (min-width: 1200px) {
  .post-list-item .top-image {
    width: 520px;
  }
}

@media (min-width: 576px) {
  .post-list-item .top-image + .col-content {
    padding-left: 30px;
  }
}

@media (min-width: 1200px) {
  .post-list-item .top-image + .col-content {
    padding-left: 90px;
  }
}

.post-list-item .date {
  margin: 0 0 0 12px;
}

.post-list-item .entry-title {
  margin-top: 10px;
  font-size: 20px;
}

@media (min-width: 1200px) {
  .post-list-item .entry-title {
    font-size: 24px;
    margin-top: 15px;
  }
}

.post-list-item .description {
  margin-top: 15px;
}

@media (min-width: 1200px) {
  .post-list-item .description {
    margin-top: 20px;
  }
}

.post-list-item .btn-readmore {
  margin-top: 20px;
}

@media (min-width: 1200px) {
  .post-list-item .btn-readmore {
    margin-top: 25px;
  }
}

.post-list-item:hover .btn.btn-readmore {
  background: var(--educrat-theme-color);
  color: #fff;
}

.post-list-item-small {
  margin-bottom: 0.9375rem;
}

@media (min-width: 768px) {
  .post-list-item-small {
    margin-bottom: 1.875rem;
  }
}

.post-list-item-small:last-child {
  margin-bottom: 0;
}

.post-list-item-small .top-image {
  width: 135px;
  margin: 0;
}

.post-list-item-small .col-content {
  padding-left: 18px;
}

.post-list-item-small .entry-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 17px;
}

.post-list-item-small .categories-name,
.post-list-item-small .date {
  font-size: 13px;
}

/* Post type: By Category */
.top-blog-info {
  padding: 25px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #EDEDED;
}

.top-blog-info i {
  margin-right: 10px;
}

.top-blog-info .categories {
  margin-right: 35px;
}

.top-blog-info .author a {
  color: var(--educrat-theme-color);
}

.top-blog-info a {
  color: var(--educrat-text-color);
}

.top-blog-info a:hover, .top-blog-info a:active {
  color: var(--educrat-theme-color);
}

.category-posts {
  position: relative;
}

.category-posts::after {
  content: "";
  top: 20px;
  position: absolute;
  right: 0;
  width: 1px;
  height: 1000px;
  background: #EDEDED;
}

.category-posts .post {
  border-bottom: 1px solid #EDEDED;
}

.category-posts .category-posts-label {
  padding: 1px 3px;
  border-radius: 0;
  background: var(--educrat-theme-color);
  font-weight: 400;
  text-transform: uppercase;
}

.category-posts .category-posts-label a {
  color: #fff;
}

.category-posts .entry-meta::after {
  display: none;
}

.category-posts .posts-more .post:last-child {
  border: 0px;
}

.category-posts .posts-more .entry-title a {
  color: #f8f9fa;
}

.category-posts .posts-more .entry-title a:hover {
  color: var(--educrat-theme-color);
}

/*------------------------------------*\
    Comment List
\*------------------------------------*/
.comment-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.comment-list .comment-respond {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .comment-list .comment-respond {
    margin-bottom: 40px;
  }
}

.comment-list .comment-respond small {
  margin-left: 5px;
  font-size: 14px;
  font-weight: 400;
}

.comment-list .comment-respond #submit {
  min-width: auto !important;
}

.comment-list .comment-respond textarea#comment {
  height: 120px;
}

.comment-list .comment-respond .btn {
  padding: 0.6rem 1.875rem;
  font-size: 0.9375rem;
}

.comment-list #cancel-comment-reply-link {
  color: #f33066;
}

.comment-list .comment-author {
  font-size: 0.875rem;
}

.comment-list .comment-author i {
  margin-right: 5px;
}

.comment-list .comment-author > * {
  margin-right: 15px;
}

.comment-list .comment-author > *:last-child {
  margin-right: 0;
}

.comment-list .comment-author + .comment-respond {
  margin: 15px 0 0;
}

.comment-list .name-comment {
  font-weight: 500;
  margin: 0;
  text-transform: capitalize;
  font-size: 1rem;
}

@media (min-width: 1200px) {
  .comment-list .name-comment {
    font-size: 17px;
  }
}

.comment-list .review-stars-rated-wrapper {
  margin-top: 5px;
}

.comment-list .date {
  font-size: 13px;
  margin-left: 5px;
  white-space: nowrap;
}

.comment-list .inner-left {
  padding-right: 20px;
}

.comment-list .children {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .comment-list .children {
    padding-left: 40px;
  }
}

.comment-list .comment-edit-link {
  color: #f33066;
  font-weight: 500;
}

.comment-list .comment-reply-link {
  font-weight: 500;
  color: #02ccad;
  white-space: nowrap;
}

.comment-list .comment-text {
  margin-top: 12px;
}

.comment-list .comment-text p:last-child {
  margin: 0;
}

.comment-list div.avatar {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  float: left;
}

@media (min-width: 1200px) {
  .comment-list div.avatar {
    width: 60px;
    height: 60px;
  }
}

.comment-list div.avatar img {
  margin: 0;
}

.comment-list div.avatar + .comment-box {
  overflow: hidden;
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .comment-list div.avatar + .comment-box {
    padding-left: 20px;
  }
}

.comment-list .the-comment {
  margin: 0 0 15px;
  padding: 0 0 15px;
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .comment-list .the-comment {
    margin: 0 0 30px;
    padding: 0 0 30px;
  }
}

.comment-list > li:last-child > .the-comment:last-child {
  border-bottom: 0;
  margin-bottom: 10px;
}

.logged-in-as a + a {
  color: #f33066;
}

/*------------------------------------*\
    Single post
\*------------------------------------*/
.social-networks li {
  padding-left: 10px;
  padding-right: 10px;
}

.social-networks li:last-child a {
  margin-right: 0;
}

.social-networks li a {
  font-size: 14px;
}

.social-networks li a:hover {
  color: var(--educrat-theme-color);
}

.post-navigation {
  position: relative;
}

@media (min-width: 1200px) {
  .post-navigation {
    padding: 5px 0;
  }
}

.post-navigation .screen-reader-text {
  display: none;
}

.post-navigation .nav-links {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: -15px;
  margin-right: -15px;
  position: relative;
}

.post-navigation .nav-links > * {
  width: 40%;
  float: left;
  padding-left: 15px;
  padding-right: 15px;
}

.post-navigation .nav-links > * i {
  font-weight: 400;
  font-size: 0.875rem;
}

.post-navigation .nav-links > *.nav-next {
  margin-left: auto;
  float: right;
  text-align: right;
}

.post-navigation .nav-links > *.nav-next .title-direct {
  float: right;
}

.post-navigation .nav-links > * > a:hover .navi {
  color: var(--educrat-theme-color);
}

.post-navigation .nav-links .title-direct {
  font-size: 0.875rem;
  color: var(--educrat-text-color);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-navigation .nav-links .navi {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-weight: 500;
  font-size: 17px;
  text-transform: capitalize;
}

.post-navigation .inner-right {
  padding-right: 35px;
  position: relative;
}

.post-navigation .inner-right i {
  position: absolute;
  top: 4px;
  right: 0;
}

.post-navigation .inner-left {
  padding-left: 35px;
  position: relative;
}

.post-navigation .inner-left i {
  position: absolute;
  top: 4px;
  left: 0;
}

.author-info .author-title {
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 10px;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .author-info .author-title {
    margin-bottom: 20;
  }
}

.author-info .avatar-img {
  width: 70px;
  height: 70px;
  overflow: hidden;
  border-radius: 50%;
}

.author-info .description {
  padding-left: 0.9375rem;
  flex: 1;
  -webkit-box-flex: 1;
  -ms-flex: 1;
}

@media (min-width: 1200px) {
  .author-info .description {
    padding-left: 1.875rem;
  }
}

.wrapper-posts-related {
  padding: 1.875rem 0;
  background: #F7F8FB;
}

@media (min-width: 1200px) {
  .wrapper-posts-related {
    padding: 90px 0;
  }
}

.wrapper-posts-related .type-post {
  margin-bottom: 0;
}

.related-posts .title {
  font-size: 25px;
  margin: 0 0 1.875rem;
  text-align: center;
}

@media (min-width: 1200px) {
  .related-posts .title {
    margin-bottom: 50px;
    font-size: 30px;
  }
}

.gallery {
  margin-left: -15px;
  margin-right: -15px;
  overflow: hidden;
}

.gallery .gallery-item {
  float: left;
  margin-bottom: 15px;
  padding-right: 15px;
  padding-left: 15px;
  position: relative;
}

.gallery .gallery-item figcaption {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  color: #fff;
  max-height: 50%;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  margin-left: 15px;
  margin-right: 15px;
  opacity: 0;
  filter: alpha(opacity=0);
  padding: 8px 15px;
}

.gallery .gallery-item:hover figcaption {
  opacity: 1;
  filter: alpha(opacity=100);
}

.gallery.gallery-columns-9 .gallery-item {
  width: 11%;
}

.gallery.gallery-columns-8 .gallery-item {
  width: 12.5%;
}

.gallery.gallery-columns-7 .gallery-item {
  width: 14%;
}

.gallery.gallery-columns-6 .gallery-item {
  width: 16.5%;
}

.gallery.gallery-columns-5 .gallery-item {
  width: 20%;
}

.gallery.gallery-columns-4 .gallery-item {
  width: 25%;
}

.gallery.gallery-columns-3 .gallery-item {
  width: 33%;
}

.gallery.gallery-columns-1 .gallery-item {
  width: 100%;
}

.gallery.gallery-columns-2 .gallery-item {
  width: 50%;
}

.comment-navigation {
  overflow: hidden;
  padding: 20px 0;
}

.comment-navigation .nav-links > div {
  display: inline-block;
}

.comment-navigation .nav-links > div + div {
  line-height: 1.1;
  margin-left: 15px;
  padding-left: 15px;
  border-left: 2px solid #EDEDED;
}

.top-detail-info {
  margin: 0 0 1.875rem;
  text-align: center;
}

@media (min-width: 1200px) {
  .top-detail-info {
    margin-bottom: 80px;
  }
}

.top-detail-info .list-categories {
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}

.top-detail-info .list-categories a {
  color: var(--educrat-theme-color);
}

.top-detail-info .date {
  font-size: 17px;
}

.main-content-detail .single-info .entry-thumb {
  margin-bottom: 1.875rem;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .main-content-detail .single-info .entry-thumb {
    margin-bottom: 50px;
  }
}

.main-content-detail.only-main .inner-detail {
  max-width: 870px;
  margin-left: auto;
  margin-right: auto;
}

.detail-post iframe {
  max-width: 100%;
}

.detail-post .tag-social {
  width: 100%;
  margin-top: 20px;
}

@media (min-width: 1200px) {
  .detail-post .tag-social {
    margin-top: 1.875rem;
  }
}

.detail-post .entry-tags-list {
  display: block;
  margin: 8px 0 0;
  position: relative;
}

.detail-post #comments,
.detail-post .post-navigation,
.detail-post .author-info {
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .detail-post #comments,
  .detail-post .post-navigation,
  .detail-post .author-info {
    padding-top: 1.875rem;
    margin-top: 1.875rem;
  }
}

.author-post .avatar {
  border-radius: 50%;
}

.author-post .avatar-img {
  padding-right: 8px;
  float: left;
}

.author-post .name-author {
  display: inline-block;
  margin-top: 9px;
}

.author-wrapper .avatar-img {
  overflow: hidden;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
}

.author-wrapper .avatar-img img {
  margin: 0 !important;
}

.author-wrapper .author-title {
  font-size: 0.9375rem;
  font-weight: 400;
  margin: 0;
  text-transform: capitalize;
}

.author-wrapper .author-title a {
  color: #777777;
}

.author-wrapper .author-title a:hover, .author-wrapper .author-title a:focus {
  color: var(--educrat-link-color);
}

.author-wrapper .right-inner {
  padding-left: 10px;
}

#respond .comment-reply-title {
  font-weight: 500;
  font-size: 18px;
  margin: 0 0 10px;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  #respond .comment-reply-title {
    margin-bottom: 15px;
    font-size: 20px;
  }
}

#respond .comment-reply-title #cancel-comment-reply-link {
  color: #f33066;
}

.comments-title {
  font-weight: 500;
  font-size: 18px;
  margin: 0 0 20px;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .comments-title {
    margin-bottom: 30px;
    font-size: 20px;
  }
}

/* 8. effect */
.effect-1 {
  position: relative;
}

.effect-1:after {
  content: '';
  display: block;
  width: 0px;
  height: 1px;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
  margin: auto;
}

.effect-1:hover:after {
  width: 100%;
  height: 1px;
  background: var(--educrat-theme-color);
}

.zoom-2 {
  overflow: hidden;
  display: block;
  border-radius: 3px;
}

.zoom-2 img {
  position: relative;
  width: 100%;
  height: auto;
  -webkit-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.zoom-2:hover img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.close .fa {
  -webkit-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}

.close:hover .fa {
  -webkit-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  transform: rotate(360deg);
}

.image-overlay-1:after, .image-overlay-1:before {
  content: "";
  display: block;
  position: absolute;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.image-overlay-1:after {
  top: -100%;
}

.image-overlay-1:before {
  bottom: -100%;
}

.image-overlay-1:hover:after {
  top: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

.image-overlay-1:hover:before {
  bottom: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

/* 10. widgets layout */
.vertical-wrapper {
  position: relative;
}

.vertical-wrapper .content-vertical {
  padding: 10px 0 0;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 3;
  margin-top: 15px !important;
  min-width: 300px;
}

.vertical-wrapper.show-always .content-vertical {
  display: block;
}

.vertical-wrapper .title {
  margin-left: 12px;
}

.apus-vertical-menu {
  padding: 20px 30px;
  background: #fff;
  margin: 0;
  list-style: none;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  position: relative;
  line-height: 1;
}

.apus-vertical-menu:before {
  content: '';
  width: 10px;
  height: 10px;
  background: #fff;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  left: 35px;
  top: -5px;
}

.apus-vertical-menu > li {
  display: block;
  width: 100%;
  position: static;
}

.apus-vertical-menu > li > a {
  display: inline-block;
  width: 100%;
  font-size: 0.9375rem;
  padding: 10px 0;
  background: transparent !important;
}

.apus-vertical-menu > li > a > i,
.apus-vertical-menu > li > a > img {
  font-size: 22px;
  margin-right: 15px;
  width: 18px;
  display: inline-block;
  color: var(--educrat-theme-color);
}

.apus-vertical-menu > li > a .caret {
  display: none;
}

.apus-vertical-menu > li .dropdown-toggle:after {
  border: 0;
  content: "\e649";
  font-family: 'themify';
  font-size: 12px;
  float: right;
  margin-top: 1px;
}

.apus-vertical-menu > li .apus-container {
  padding: 10px 35px;
}

.apus-vertical-menu > li:hover > a, .apus-vertical-menu > li.active > a {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.apus-vertical-menu li:hover > .dropdown-menu {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}

.apus-vertical-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--educrat-theme-color);
  font-family: var(--educrat-heading-font);
}

.apus-vertical-menu .text-label.label-hot {
  color: #f33066;
}

.apus-vertical-menu .text-label.label-new {
  color: #27b737;
}

.apus-vertical-menu .dropdown-menu {
  min-width: 240px;
  height: calc(100% + 2px);
  visibility: hidden;
  padding: 20px 30px;
  border-radius: 8px;
  display: block;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #EDEDED;
  -webkit-box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
}

.apus-vertical-menu .dropdown-menu > li > a {
  background: transparent !important;
  padding: 10px 0;
  display: inline-block;
}

.apus-vertical-menu .dropdown-menu > li > a:hover, .apus-vertical-menu .dropdown-menu > li > a:focus {
  text-decoration: underline;
}

.apus-vertical-menu .dropdown-menu > li > a > i,
.apus-vertical-menu .dropdown-menu > li > a > img {
  font-size: 20px;
  margin-right: 10px;
  width: 15px;
  display: inline-block;
}

.apus-vertical-menu .dropdown-menu .widget {
  margin: 0;
}

.apus-vertical-menu .dropdown-menu .widget .widget-title, .apus-vertical-menu .dropdown-menu .widget .widgettitle, .apus-vertical-menu .dropdown-menu .widget .widget-heading {
  margin: 0 0 10px;
  font-size: 18px;
}

.apus-vertical-menu .dropdown-menu .widget-nav-menu .menu li {
  margin: 0;
}

.apus-vertical-menu .dropdown-menu .widget-nav-menu .menu li a {
  display: inline-block;
  width: 100%;
  padding: 10px 0;
  color: var(--educrat-link-color);
}

.apus-vertical-menu .dropdown-menu .widget-nav-menu .menu li a:hover, .apus-vertical-menu .dropdown-menu .widget-nav-menu .menu li a:focus {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.apus-vertical-menu .dropdown-menu .widget-nav-menu .menu li.active > a {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.apus-vertical-menu .aligned-left > .dropdown-menu {
  top: -1px;
  left: calc(100% - 15px);
}

.apus-vertical-menu .aligned-right > .dropdown-menu {
  top: -1px;
  right: calc(100% - 15px);
  left: inherit;
}

.apus-vertical-menu-layout1 {
  display: block;
}

.apus-vertical-menu-layout1 .sub-menu {
  width: 100% !important;
  display: none;
  list-style: none;
  margin: 0;
  padding: 7px 0 0 1.875rem !important;
}

.apus-vertical-menu-layout1 .sub-menu a {
  position: relative;
}

.apus-vertical-menu-layout1 .sub-menu a:before {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #DEDCDC;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.apus-vertical-menu-layout1 li {
  position: relative;
}

.apus-vertical-menu-layout1 li .icon-toggle {
  font-size: 12px;
  position: absolute;
  top: 5px;
  right: 20px;
  z-index: 1;
  padding: 8px 0 8px 8px;
  cursor: pointer;
  color: var(--educrat-link-color);
}

.apus-vertical-menu-layout1 li a {
  display: inline-block;
  width: 100%;
  padding: 2px 20px;
  color: var(--educrat-text-color);
}

.apus-vertical-menu-layout1 li a:hover, .apus-vertical-menu-layout1 li a:focus {
  color: var(--educrat-theme-color);
}

.apus-vertical-menu-layout1 li a:hover:before, .apus-vertical-menu-layout1 li a:focus:before {
  background: var(--educrat-theme-color);
}

.apus-vertical-menu-layout1 li:hover > a, .apus-vertical-menu-layout1 li.active > a {
  color: var(--educrat-theme-color);
}

.apus-vertical-menu-layout1 li:hover > a:before, .apus-vertical-menu-layout1 li.active > a:before {
  background: var(--educrat-theme-color);
}

.apus-vertical-menu-layout1 > li > a {
  font-size: 1rem;
  font-weight: 500;
  padding: 8px 20px;
  border-radius: 50px;
  margin-bottom: 3px;
}

.apus-vertical-menu-layout1 > li:hover > a, .apus-vertical-menu-layout1 > li.active > a {
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
}

.apus-vertical-menu-layout1 .elementor-container {
  display: block;
}

.apus-vertical-menu-layout1 .elementor-container .elementor-element {
  width: 100%;
}

.apus-vertical-menu-layout1 .widget-nav-menu .menu li {
  margin: 0;
}

.apus-vertical-menu-layout1 .widget-nav-menu .menu li a {
  text-decoration: none !important;
}

.apus_custom_menu.center {
  text-align: center;
}

.apus_custom_menu.center li {
  display: inline-block;
  margin: 0 15px;
}

.apus_custom_menu.left {
  text-align: left;
}

.apus_custom_menu.right {
  text-align: right;
}

.apus_custom_menu.inline li {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 0;
  margin-right: 20px;
}

@media (min-width: 1200px) {
  .apus_custom_menu.inline li {
    margin-right: 40px;
  }
}

.apus_custom_menu.inline li:last-child {
  margin: 0;
}

.slick-carousel {
  position: relative;
  margin-right: -8px;
  margin-left: -8px;
}

@media (min-width: 1200px) {
  .slick-carousel {
    margin-right: -15px;
    margin-left: -15px;
  }
}

.slick-carousel .slick-arrow {
  background: #fff;
  color: #1A064F;
  padding: 0;
  border: 2px solid #1A064F;
  display: inline-block;
  font-size: 18px;
  width: 30px;
  height: 30px;
  line-height: 26px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  transform: translate(0, -50%);
  z-index: 2;
  border-radius: 50%;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-arrow {
    width: 50px;
    height: 50px;
    line-height: 46px;
  }
}

.slick-carousel .slick-arrow .textnav {
  display: none;
}

.slick-carousel .slick-arrow:hover, .slick-carousel .slick-arrow:focus {
  color: #fff;
  background: #1A064F;
  border-color: #1A064F;
}

.slick-carousel .slick-prev {
  left: 0;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-prev {
    left: -10px;
  }
}

.slick-carousel .slick-next {
  right: 0;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-next {
    right: -10px;
  }
}

.slick-carousel .slick-slide {
  outline: none !important;
  padding-left: 8px;
  padding-right: 8px;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-slide {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.slick-carousel.no-gap {
  margin: 0;
}

.slick-carousel.no-gap .slick-slide {
  padding-left: 0;
  padding-right: 0;
}

.slick-carousel.gap-10 {
  margin-left: -5px;
  margin-right: -5px;
}

.slick-carousel.gap-10 .slick-slide {
  padding-left: 5px;
  padding-right: 5px;
}

.slick-carousel.gap-2 {
  margin-left: -1px;
  margin-right: -1px;
}

.slick-carousel.gap-2 .slick-slide {
  padding-left: 1px;
  padding-right: 1px;
}

.slick-carousel.show-text .textnav {
  display: inline-block;
  margin: 0 2px;
}

.slick-carousel.show-text .slick-arrow {
  width: auto;
  height: auto;
  background: transparent !important;
  font-weight: 500;
  font-size: 12px;
  color: var(--educrat-link-color);
}

.slick-carousel.show-text .slick-arrow:hover, .slick-carousel.show-text .slick-arrow:active, .slick-carousel.show-text .slick-arrow:focus {
  color: var(--educrat-theme-color);
}

.slick-carousel.show-text .slick-prev {
  left: 0;
  right: inherit;
}

.slick-carousel.show-text .slick-next {
  right: 0;
  left: inherit;
}

.slick-carousel .slick-track {
  margin: inherit;
}

.slick-carousel .slick-dots {
  margin: 0 !important;
  padding: 1rem 0 0;
  text-align: center;
  list-style: none;
  line-height: 1;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-dots {
    padding: 1.875rem 0 0;
  }
}

.slick-carousel .slick-dots li {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
  display: inline-block;
  cursor: pointer;
  margin: 0 5px;
  border: 0;
  padding: 0;
  background: transparent;
}

.slick-carousel .slick-dots li button {
  border: none;
  display: block;
  text-indent: -9999em;
  width: 8px;
  height: 8px;
  padding: 0;
  background: #D4D4D4;
  border-radius: 50%;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.slick-carousel .slick-dots li.slick-active button {
  background: var(--educrat-link-color);
}

.slick-carousel.st_white .slick-dots li.slick-active,
.slick-carousel.st_white .slick-dots li button {
  background-color: #fff !important;
}

.widget-socials .social {
  padding: 0;
  list-style: none;
  margin: 0;
}

.widget-socials .social > li {
  padding: 0;
  display: inline-block;
  margin-right: 5px;
}

.widget-socials .social > li:last-child {
  margin: 0;
}

.widget-socials .social a {
  font-size: -0.0625rem;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--educrat-theme-color);
  color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .widget-socials .social a {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
}

.widget-socials .social a:hover, .widget-socials .social a:focus {
  color: #fff;
  background: var(--educrat-theme-hover-color);
}

.widget-socials.st_normal .social > li {
  margin-right: 1rem;
}

@media (min-width: 1200px) {
  .widget-socials.st_normal .social > li {
    margin-right: 1.5625rem;
  }
}

.widget-socials.st_normal .social > li:last-child {
  margin-right: 0;
}

.widget-socials.st_normal .social a {
  width: auto;
  height: auto;
  line-height: 1.8;
  background-color: transparent;
  color: var(--educrat-link-color);
}

.list-icon {
  margin-bottom: 0.75rem;
}

.list-icon:last-child {
  margin-bottom: 0;
}

.list-icon .title {
  margin: 0;
}

.list-icon .box-content {
  padding-left: 10px;
}

.vertical-icon {
  position: relative;
  display: block;
  width: 25px;
  height: 12px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
}

.vertical-icon:after, .vertical-icon:before {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  width: 20px;
  height: 2px;
  background-color: var(--educrat-link-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.vertical-icon:after {
  width: 25px;
  height: 2px;
  top: 0;
  bottom: inherit;
}

.vertical-icon:focus:before, .vertical-icon:hover:before {
  width: 100%;
}

.navbar-wrapper .close-navbar-sidebar {
  cursor: pointer;
  opacity: 0.8;
  filter: alpha(opacity=80);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  color: var(--educrat-link-color);
  background-color: var(--educrat-theme-color) !important;
  position: absolute;
  z-index: 1;
  text-align: center;
  border-radius: 50%;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  line-height: 30px;
}

@media (min-width: 1200px) {
  .navbar-wrapper .close-navbar-sidebar {
    font-size: 15px;
    top: 40px;
    right: 40px;
    width: 42px;
    height: 42px;
    line-height: 42px;
  }
}

.navbar-wrapper .close-navbar-sidebar:hover, .navbar-wrapper .close-navbar-sidebar:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.navbar-wrapper .navbar-sidebar-wrapper {
  z-index: 3;
  position: fixed;
  overflow-y: auto;
  scrollbar-width: thin;
  right: 0;
  top: 0;
  -webkit-transition: all 0.35s ease-in-out 0s;
  -o-transition: all 0.35s ease-in-out 0s;
  transition: all 0.35s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  width: 450px;
  max-width: 80%;
  height: 100vh;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}

.navbar-wrapper .navbar-sidebar-wrapper.active {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.navbar-wrapper .navbar-sidebar-overlay {
  background: rgba(10, 35, 87, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  cursor: no-drop;
  visibility: hidden;
  z-index: 2;
}

.navbar-wrapper .navbar-sidebar-overlay.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.navbar-wrapper.st_left .navbar-sidebar-wrapper {
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  right: initial;
  left: 0;
}

.navbar-wrapper.st_left .navbar-sidebar-wrapper.active {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.language-wrapper {
  font-size: 12px;
  display: inline-block;
  position: relative;
}

.language-wrapper:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 5px;
}

.language-wrapper .selected .language-current > img {
  margin: 0 10px 0 0;
  vertical-align: sub;
}

.language-wrapper .selected .language-current > i {
  margin-left: 10px;
}

.language-wrapper .dropdown-menu {
  background: #fff;
  display: block;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  font-size: 12px;
  margin-top: 5px;
  border-radius: 0;
  padding: 13px 18px;
  min-width: 130px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #EDEDED;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  visibility: hidden;
}

.language-wrapper:hover .dropdown-menu {
  visibility: visible;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  filter: alpha(opacity=100);
}

.language-wrapper .list-language {
  list-style: none;
  padding: 0;
  margin: 0;
}

.language-wrapper .list-language li {
  margin-bottom: 10px;
}

.language-wrapper .list-language li:last-child {
  margin-bottom: 0;
}

.language-wrapper .list-language a:hover, .language-wrapper .list-language a:focus {
  color: var(--educrat-theme-color);
}

.language-wrapper .list-language img {
  margin-right: 6px;
  margin-bottom: 0;
}

.social-link {
  display: inline-block;
  margin: 0 5px;
  padding: 0;
}

.social-link li {
  display: inline-block;
  margin: 0 5px;
}

.social-link li a {
  background: #f4f4f4 none repeat scroll 0 0;
  border-radius: 100%;
  color: var(--educrat-text-color);
  display: inline-block;
  height: 40px;
  line-height: 38px;
  text-align: center;
  width: 40px;
  border: 1px solid #EDEDED;
}

.social-link.lighten li a {
  background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
  border: 1px solid #ffffff;
  color: #ffffff;
}

.item-inner-features {
  text-align: center;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  padding: 1rem;
  background-color: #fff;
  border: 1px solid transparent;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .item-inner-features {
    padding: 50px 68px;
  }
}

.item-inner-features .title {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-size: 18px;
  margin: 0 0 12px;
}

@media (min-width: 1200px) {
  .item-inner-features .title {
    font-size: 24px;
  }
}

.item-inner-features .features-box-image {
  line-height: 1;
  font-size: 50px;
  margin-bottom: 10px;
  color: #00FF84;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .item-inner-features .features-box-image {
    font-size: 80px;
    margin-bottom: 20px;
  }
}

.item-inner-features .features-box-image img {
  display: block;
}

.item-inner-features.style2 {
  text-align: left;
  padding: 20px;
  background: #F7F8FB;
  border-radius: 16px;
}

@media (min-width: 1200px) {
  .item-inner-features.style2 {
    padding: 40px;
  }
}

.item-inner-features.style2 .features-box-image {
  background: #fff;
  border-radius: 50%;
  overflow: hidden;
  width: 70px;
  height: 70px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 1200px) {
  .item-inner-features.style2 .features-box-image {
    margin-bottom: 30px;
    font-size: 70px;
  }
}

.item-inner-features.style2 .title {
  font-weight: 500;
  margin: 0 0 5px;
  font-size: 18px;
}

@media (min-width: 1200px) {
  .item-inner-features.style2 .title {
    font-size: 20px;
  }
}

.item-inner-features.style2:hover .title {
  color: var(--educrat-theme-color);
}

.widget-testimonials .star {
  color: #E59819;
}

.widget-testimonials .star .inner {
  width: 79px;
  position: relative;
  margin-left: 10px;
  font-size: 10px;
  color: #c1cde4;
  letter-spacing: 5px;
}

.widget-testimonials .star .inner:before {
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.widget-testimonials .star .text {
  font-size: 14px;
  font-weight: 500;
}

.widget-testimonials .w-percent {
  color: #E59819;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.widget-testimonials .w-percent:before {
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

@media (min-width: 1350px) {
  .widget-testimonials.fullscreen .slick-list {
    overflow: visible;
  }
}

.widget-testimonials.style1.show_nav {
  padding-bottom: 60px;
}

@media (min-width: 1200px) {
  .widget-testimonials.style1.show_nav {
    padding-bottom: 110px;
  }
}

.widget-testimonials.style1 .slick-carousel .slick-arrow {
  top: 100%;
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
  margin-top: 1.875rem;
  opacity: 1;
  filter: alpha(opacity=100);
  border-color: #fff;
  background: transparent;
  color: #fff;
}

@media (min-width: 1200px) {
  .widget-testimonials.style1 .slick-carousel .slick-arrow {
    margin-top: 60px;
  }
}

.widget-testimonials.style1 .slick-carousel .slick-arrow:hover, .widget-testimonials.style1 .slick-carousel .slick-arrow:focus {
  color: var(--educrat-link-color);
  background: #fff;
  border-color: #fff;
}

.widget-testimonials.style1 .slick-carousel .slick-prev {
  right: 60px;
  left: inherit;
}

@media (min-width: 1200px) {
  .widget-testimonials.style1 .slick-carousel .slick-prev {
    right: 90px;
  }
}

.widget-testimonials.style1 .slick-carousel .slick-next {
  right: 15px;
  left: inherit;
}

.widget-testimonials.style1 .slick-carousel .slick-dots {
  text-align: inherit;
  padding-left: 10px;
}

@media (min-width: 1350px) {
  .widget-testimonials.style3 .slick-list {
    overflow: visible;
  }
}

.widget-testimonials.style4 .slick-carousel {
  margin: 0;
}

.widget-testimonials.style4 .slick-carousel .slick-slide {
  padding: 0;
  opacity: 0.2;
  filter: alpha(opacity=20);
  -webkit-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin: 0 -30px;
}

.widget-testimonials.style4 .slick-carousel .slick-current {
  position: relative;
  z-index: 1;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.testimonials-item {
  position: relative;
  padding: 0.9375rem;
  background: #fff;
  border-radius: 12px;
  border: 1px solid transparent;
}

@media (min-width: 1200px) {
  .testimonials-item {
    padding: 1.875rem 1.875rem 20px;
  }
}

.testimonials-item:before {
  position: absolute;
  top: 10px;
  right: 15px;
  z-index: 1;
  color: #E5F0FD;
  line-height: 1;
  font-size: 90px;
}

@media (min-width: 1200px) {
  .testimonials-item:before {
    top: 20px;
    right: 25px;
  }
}

.testimonials-item .avarta {
  overflow: hidden;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  background-color: #fff;
  position: relative;
}

.testimonials-item .name-client {
  font-weight: 500;
  font-size: 15px;
  margin: 0;
}

.testimonials-item .description {
  margin-top: 0.9375rem;
  font-size: 15px;
  font-weight: 500;
  color: var(--educrat-link-color);
}

.testimonials-item .job {
  color: var(--educrat-text-color);
  font-size: 13px;
}

.testimonials-item .star {
  display: inline-flex;
  align-items: center;
  padding: 0 7px;
  background: #ff9800;
  color: #ffffff;
  font-size: 13px;
  border-radius: 3px;
  margin-top: 3px;
}

.testimonials-item .star i {
  margin-right: 4px;
  font-size: 10px;
}

.testimonials-item .info-testimonials {
  flex-grow: 1;
  padding-left: 20px;
}

.testimonials-item .title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 18px;
}

.testimonials-item .inner-bottom {
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #EDEDED;
}

.testimonials-item2 .description {
  line-height: 1.5;
  font-size: 18px;
  font-weight: 500;
  color: var(--educrat-link-color);
  max-width: 610px;
  margin: 0 auto 1.875rem;
}

@media (min-width: 1200px) {
  .testimonials-item2 .description {
    margin-bottom: 50px;
    font-size: 24px;
  }
}

.testimonials-item2 .name-client {
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 2px;
}

.wrapper-testimonial-thumbnail {
  max-width: 500px;
  margin: 15px auto 0;
}

@media (min-width: 1200px) {
  .wrapper-testimonial-thumbnail {
    margin-top: 1.875rem;
  }
}

.wrapper-testimonial-thumbnail .slick-carousel {
  margin-left: -5px;
  margin-right: -5px;
}

.wrapper-testimonial-thumbnail .slick-carousel .slick-slide {
  padding-left: 5px;
  padding-right: 5px;
}

.wrapper-testimonial-thumbnail .avarta {
  cursor: pointer;
  max-width: 100%;
  width: 92px;
  height: 92px;
  overflow: hidden;
  border: 2px solid transparent;
  border-radius: 50%;
  padding: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.wrapper-testimonial-thumbnail .avarta:hover, .wrapper-testimonial-thumbnail .avarta:focus {
  border-color: var(--educrat-link-color);
}

.wrapper-testimonial-thumbnail .slick-current .avarta {
  border-color: var(--educrat-link-color);
}

.testimonials-item3 {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .testimonials-item3 {
    padding: 60px;
    margin-bottom: 1.875rem;
  }
}

.testimonials-item3 .wrapper-avarta {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

@media (min-width: 1200px) {
  .testimonials-item3 .wrapper-avarta {
    width: 170px;
    height: 170px;
  }
}

.testimonials-item3 .wrapper-avarta img {
  border-radius: 50%;
}

.testimonials-item3 .info-testimonials {
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .testimonials-item3 .info-testimonials {
    padding-left: 1.875rem;
  }
}

.testimonials-item3 .name-client {
  font-size: 15px;
  font-weight: 500;
  margin: 10px 0 0;
}

.testimonials-item3 .job {
  font-size: 13px;
}

.testimonials-item3 .star {
  margin: 0 0 10px;
}

.testimonials-item3 .description {
  font-weight: 500;
}

@media (min-width: 1200px) {
  .testimonials-item3 .description {
    font-size: 1rem;
  }
}

.testimonials-item3 .icon {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 50px;
  height: 50px;
  border: 5px solid #fff;
  border-radius: 50%;
  background: #EEF2F6;
  color: var(--educrat-theme-color);
  font-size: 60px;
  line-height: 1;
}

.testimonials-item3 .icon span {
  height: 20px;
}

.widget-nav-menu .menu li {
  margin: 0 0 5px;
}

@media (min-width: 1200px) {
  .widget-nav-menu .menu li {
    margin: 0 0 10px;
  }
}

.widget-nav-menu .menu li > a {
  position: relative;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: inline-block;
  color: var(--educrat-text-color);
}

.widget-nav-menu .menu li:hover > a, .widget-nav-menu .menu li.active > a, .widget-nav-menu .menu li.current-cat-parent > a, .widget-nav-menu .menu li.current-cat > a {
  color: var(--educrat-link_hover_color);
  text-decoration: underline;
}

.widget-nav-menu .menu li:last-child {
  margin: 0;
}

.widget-nav-menu .btn-url {
  font-weight: 500;
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.widget-nav-menu .wrapper-url {
  margin: 5px 0 0;
}

.widget-nav-menu.st_line .menu li {
  margin-bottom: 0;
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}

@media (min-width: 1200px) {
  .widget-nav-menu.st_line .menu li {
    margin-right: 20px;
  }
}

.widget-nav-menu.st_line .menu li:last-child {
  margin-right: 0;
}

.widget-nav-menu.st_circle li a {
  text-decoration: none !important;
}

.widget-nav-menu.st_circle li a:before {
  content: '';
  width: 6px;
  height: 6px;
  background-color: #CCCCCC;
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
  margin-right: 12px;
  margin-top: -3px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.widget-nav-menu.st_circle li a:hover:before, .widget-nav-menu.st_circle li a:focus:before {
  background-color: var(--educrat-link_hover_color);
}

.widget-nav-menu.st_circle li:hover > a:before, .widget-nav-menu.st_circle li.current-cat-parent > a:before, .widget-nav-menu.st_circle li.current-cat > a:before, .widget-nav-menu.st_circle li.active > a:before {
  background-color: var(--educrat-link_hover_color);
}

.widget-mailchimp.st1 .btn i {
  display: none;
}

.widget-mailchimp.st2 .btn .text {
  display: none;
}

form.mc4wp-form {
  padding: 5px;
  background: #fff;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  form.mc4wp-form {
    padding: 10px;
  }
}

form.mc4wp-form [type="email"] {
  outline: none;
  border-color: transparent !important;
}

form.mc4wp-form [type="submit"] {
  font-size: 15px;
  padding: 10px 30px;
  border-radius: 8px !important;
}

.wpcf7-form {
  position: relative;
}

.wpcf7-form label {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 5px;
  color: var(--educrat-link-color);
}

.wpcf7-form .form-control {
  margin-bottom: 1rem;
  height: 50px;
}

@media (min-width: 1200px) {
  .wpcf7-form .form-control {
    margin-bottom: 20px;
    height: 55px;
  }
}

.wpcf7-form textarea.form-control {
  height: 150px;
}

@media (min-width: 1200px) {
  .wpcf7-form textarea.form-control {
    height: 250px;
    margin-bottom: 1.875rem;
  }
}

.widget-brand .top-info {
  width: 100%;
}

@media (min-width: 1200px) {
  .widget-brand .top-info {
    flex-shrink: 0;
    width: 18%;
    padding-right: 20px;
    text-align: right;
  }
  .widget-brand .top-info + .content-inner {
    width: 82%;
    padding-left: 20px;
  }
}

@media (min-width: 1200px) {
  .widget-brand .content-inner {
    flex-grow: 1;
  }
}

.widget-brand .title {
  font-size: 1rem;
  margin: 0;
}

.widget-brand .brand-item {
  text-align: center;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.widget-brand .brand-item img {
  display: inline-block;
}

.widget-brand .slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
}

.widget-brand.st2 .brand-item {
  overflow: hidden;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
}

.widget-brand.st2 .brand-item:hover, .widget-brand.st2 .brand-item:focus {
  -webkit-box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.08);
  box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.08);
}

.widget-brand.st2 .slick-list {
  padding-bottom: 30px;
}

@media (min-width: 1200px) {
  .widget-brand.st2 .slick-list {
    padding-bottom: 50px;
  }
}

.widget-brand.st2 .slick-dots {
  padding: 0;
}

.widget-brand.st3 .brand-item {
  opacity: 0.4;
  filter: alpha(opacity=40);
}

.widget-brand.st3 .brand-item:hover, .widget-brand.st3 .brand-item:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.box-theme {
  padding: 15px;
  border-radius: 8px;
  background: #282664;
  color: #fff;
}

@media (min-width: 1200px) {
  .box-theme {
    padding: 40px 30px;
  }
}

.box-theme .list-check {
  color: #fff;
}

.times {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  text-align: center;
  font-size: 35px;
  line-height: 1.5;
  font-weight: 700;
}

@media (min-width: 1200px) {
  .times {
    font-size: 45px;
  }
}

.times > div ~ div {
  margin-left: 20px;
}

@media (min-width: 1200px) {
  .times > div ~ div {
    margin-left: 45px;
  }
}

.times .title {
  font-size: 15px;
  font-weight: 400;
}

.times span {
  display: block;
}

.elementor-accordion .elementor-accordion-item {
  margin-bottom: 20px;
  border-radius: 8px;
  background: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(20, 3, 66, 0.07);
  box-shadow: 0 1px 4px 0 rgba(20, 3, 66, 0.07);
}

.elementor-accordion .elementor-accordion-item:last-child {
  margin-bottom: 0;
}

.elementor-accordion .elementor-accordion-item .elementor-tab-title {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  width: 100%;
}

.elementor-accordion .elementor-accordion-item .elementor-tab-title .elementor-accordion-icon {
  margin-right: 25px;
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--educrat-theme-color);
  background: #E5F0FD;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border-radius: 50%;
}

.elementor-accordion .elementor-accordion-item .elementor-tab-title .elementor-accordion-icon:hover, .elementor-accordion .elementor-accordion-item .elementor-tab-title .elementor-accordion-icon:focus {
  color: #fff;
  background: var(--educrat-theme-color);
}

.elementor-accordion .elementor-accordion-item .elementor-tab-title.elementor-active .elementor-accordion-icon {
  color: #fff;
  background: var(--educrat-theme-color);
}

.text-theme {
  color: var(--educrat-theme-color) !important;
}

.text-white {
  color: #fff !important;
}

.text-green {
  color: #00FF84 !important;
}

.elementor-widget-button.w-100 .elementor-button {
  width: 100%;
}

.deleted_wpb_single_image {
  position: relative;
  overflow: hidden;
}

.deleted_wpb_single_image:before {
  position: absolute;
  z-index: 2;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  background: var(--educrat-theme-color);
  opacity: 0;
  filter: alpha(opacity=0);
  top: 0;
  left: 0;
}

.deleted_wpb_single_image:after {
  position: absolute;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  top: 1.875rem;
  left: 1.875rem;
  right: 1.875rem;
  bottom: 1.875rem;
  border: 1px solid #fff;
  z-index: 3;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.deleted_wpb_single_image:hover:before {
  opacity: 0.5;
  filter: alpha(opacity=50);
}

.deleted_wpb_single_image:hover:after {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.widget-team {
  text-align: center;
}

.widget-team .team-item {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.widget-team .team-item:before {
  content: '';
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: rgba(39, 42, 51, 0.7);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
}

.widget-team .team-item img {
  -webkit-transition: all 0.5s ease-in-out 0s;
  -o-transition: all 0.5s ease-in-out 0s;
  transition: all 0.5s ease-in-out 0s;
}

.widget-team .name-team {
  font-size: 1.125rem;
  margin: 1rem 0 5px;
}

@media (min-width: 1200px) {
  .widget-team .name-team {
    margin: 1.5625rem 0 5px;
  }
}

@media (min-width: 1200px) {
  .widget-team .listing {
    font-size: 0.9375rem;
  }
}

.widget-team .social {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 1;
  list-style: none;
  margin: 0;
  top: 50%;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.widget-team .social li {
  display: inline-block;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .widget-team .social li {
    margin-right: 20px;
  }
}

.widget-team .social li:last-child {
  margin-right: 0;
}

.widget-team .social li a {
  color: #fff !important;
}

.widget-team:hover .team-item img {
  -webkit-transform: scale(1.15) rotate(-1deg);
  -moz-transform: scale(1.15) rotate(-1deg);
  -ms-transform: scale(1.15) rotate(-1deg);
  -o-transform: scale(1.15) rotate(-1deg);
  transform: scale(1.15) rotate(-1deg);
}

.widget-team:hover .team-item:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget-team:hover .social {
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget_apus_vertical_menu {
  border-left: 4px solid #2e2d2d;
}

.widget_apus_vertical_menu .widget-title {
  font-size: 16px;
  font-weight: normal;
  margin: 0 0 10px;
  padding: 15px 1.875rem 0;
}

.widget_apus_vertical_menu .apus-vertical-menu {
  border: none;
}

.widget_apus_vertical_menu .apus-vertical-menu > li {
  margin-left: -4px;
}

.widget_apus_vertical_menu .apus-vertical-menu > li > a {
  border-left: 4px solid transparent;
  font-size: 16px;
  padding: 0 1.875rem;
}

.widget_apus_vertical_menu .apus-vertical-menu > li.active > a, .widget_apus_vertical_menu .apus-vertical-menu > li:hover > a {
  border-color: var(--educrat-theme-color);
}

body.single-product .apus-main-content {
  overflow: hidden;
}

.nav.tabs-product {
  border: none;
  margin: 0 0 1.5rem;
  justify-content: center;
  position: relative;
  padding: 0.9375rem 0 0;
}

@media (min-width: 1200px) {
  .nav.tabs-product {
    margin-bottom: 40px;
    padding: 1.875rem 0 0;
  }
}

.nav.tabs-product:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #EDEDED;
}

@media (min-width: 1200px) {
  .nav.tabs-product:before {
    left: -1000px;
    width: 3000px;
    height: 1px;
  }
}

.nav.tabs-product > li {
  display: inline-block;
  float: none;
  margin: 0 10px;
}

@media (min-width: 1200px) {
  .nav.tabs-product > li {
    margin: 0 20px;
  }
}

.nav.tabs-product > li > a {
  border: 0;
  font-size: 1rem;
  text-transform: capitalize;
  font-weight: 500;
  color: var(--educrat-link-color);
  display: inline-block;
  background: transparent;
  position: relative;
  padding: 0;
}

.nav.tabs-product > li > a:hover, .nav.tabs-product > li > a:focus {
  color: var(--educrat-theme-color);
}

.nav.tabs-product > li.active > a {
  color: var(--educrat-theme-color);
}

.updow:hover .top-img img,
.updow:hover .img img,
.updow:hover .image-wrapper img {
  -webkit-animation: updow 0.8s ease-in-out 0s infinite;
  animation: updow 0.8s ease-in-out 0s infinite;
}

.updow-infinite img {
  -webkit-animation: updow 1s ease-in-out 0s infinite;
  animation: updow 1s ease-in-out 0s infinite;
}

@-webkit-keyframes updow {
  50% {
    -webkit-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  0%, 100% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@keyframes updow {
  50% {
    -webkit-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  0%, 100% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@-webkit-keyframes fadeleft {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(-15px, 0);
    -ms-transform: translate(-15px, 0);
    -o-transform: translate(-15px, 0);
    transform: translate(-15px, 0);
  }
}

@keyframes fadeleft {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(-15px, 0);
    -ms-transform: translate(-15px, 0);
    -o-transform: translate(-15px, 0);
    transform: translate(-15px, 0);
  }
}

@-webkit-keyframes faderight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(15px, 0);
    -ms-transform: translate(15px, 0);
    -o-transform: translate(15px, 0);
    transform: translate(15px, 0);
  }
  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes faderight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(15px, 0);
    -ms-transform: translate(15px, 0);
    -o-transform: translate(15px, 0);
    transform: translate(15px, 0);
  }
  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

.mb0 {
  margin-bottom: 0px !important;
}

.width-full {
  width: 100% !important;
}

.custom-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-menu li {
  padding-left: 22px;
  margin-bottom: 18px;
  position: relative;
  line-height: 1.4;
}

.custom-menu li:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: #221f1f;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.custom-menu li:last-child {
  margin: 0;
}

.custom-menu li i {
  margin-right: 15px;
  font-size: 18px;
}

@media (min-width: 1200px) {
  .custom-menu li i {
    font-size: 23px;
  }
}

.custom-menu li a {
  color: var(--educrat-text-color);
}

.custom-menu li a:focus, .custom-menu li a:hover {
  color: var(--educrat-link-color);
}

.custom-menu li:hover:before, .custom-menu li.active:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.banner-item {
  min-height: 100px;
  position: relative;
  z-index: 0;
  overflow: hidden;
  border-radius: 8px;
  text-align: center;
  display: block;
}

.banner-item .title {
  position: absolute;
  z-index: 2;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 1.25rem;
  font-size: 1.125rem;
  color: #fff;
}

.banner-item-link {
  display: block;
  position: relative;
}

.banner-item-link:before {
  content: '';
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(39, 42, 51, 0.3);
}

.banner-item-link img {
  -webkit-transition: all 0.6s ease-in-out 0s;
  -o-transition: all 0.6s ease-in-out 0s;
  transition: all 0.6s ease-in-out 0s;
}

.banner-item-link:hover img, .banner-item-link:focus img {
  -webkit-transform: scale(1.1) rotate(-1deg);
  -moz-transform: scale(1.1) rotate(-1deg);
  -ms-transform: scale(1.1) rotate(-1deg);
  -o-transform: scale(1.1) rotate(-1deg);
  transform: scale(1.1) rotate(-1deg);
}

/*-----------------------------*\
        Widget video
\*-----------------------------*/
.video-wrapper-inner .popup-video {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 36px;
  font-size: 18px;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border: 2px solid var(--educrat-link-color);
  color: var(--educrat-link-color);
  text-align: center;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .video-wrapper-inner .popup-video {
    font-size: 20px;
    width: 60px;
    height: 60px;
    line-height: 56px;
  }
}

.video-wrapper-inner .popup-video:hover, .video-wrapper-inner .popup-video:focus {
  color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.video-wrapper-inner .title {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.video-wrapper-inner .title:hover, .video-wrapper-inner .title:focus {
  color: var(--educrat-theme-color);
}

.video-wrapper-inner:hover .title,
.video-wrapper-inner:hover .popup-video, .video-wrapper-inner:focus .title,
.video-wrapper-inner:focus .popup-video {
  color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.video-wrapper-inner.st2 .popup-video {
  background: #fff;
  border-color: #fff;
  -webkit-box-shadow: 0px 0px 0 7px rgba(255, 255, 255, 0.3);
  box-shadow: 0px 0px 0 7px rgba(255, 255, 255, 0.3);
}

.video-wrapper-inner.st2 .popup-video:hover, .video-wrapper-inner.st2 .popup-video:focus {
  -webkit-box-shadow: 0px 0px 0 12px rgba(255, 255, 255, 0.4);
  box-shadow: 0px 0px 0 12px rgba(255, 255, 255, 0.4);
}

.elementor-icon-box-title {
  margin-top: 0;
}

.row-20 {
  margin-left: -10px !important;
  margin-right: -10px !important;
}

.row-20 > [class*="col-"] {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.row-10 {
  margin-left: -5px !important;
  margin-right: -5px !important;
}

.row-10 [class*="col-"] {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.list-circle {
  list-style: none;
  padding: 0;
}

.list-circle li {
  margin-bottom: 7px;
  position: relative;
  padding-left: 20px;
}

@media (min-width: 1200px) {
  .list-circle li {
    margin-bottom: 12px;
  }
}

.list-circle li:before {
  content: '';
  background-color: #6A7A99;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  left: 0;
}

.list-circle.column2 {
  max-width: 600px;
}

.list-circle-check {
  list-style: none;
  padding: 0;
}

@media (min-width: 768px) {
  .list-circle-check {
    column-count: 2;
  }
}

.list-circle-check li {
  margin-bottom: 10px;
}

@media (min-width: 1200px) {
  .list-circle-check li {
    margin-bottom: 17px;
  }
}

.list-circle-check li:before {
  font-size: 8px;
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  display: inline-block;
  vertical-align: text-bottom;
  margin-right: 10px;
  background-color: #696969;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  line-height: 18px;
  text-align: center;
  color: #fff;
}

@media (min-width: 768px) {
  .column2 {
    column-count: 2;
  }
}

.m-770 {
  max-width: 770px;
  margin-right: auto;
  margin-left: auto;
}

.mb-per2-gutter {
  margin-bottom: 0.9375rem;
}

@media (min-width: 992px) {
  .mb-lg-gutter {
    margin-bottom: 1.875rem;
  }
}

.max-600 {
  max-width: 600px;
}

.mb-30 {
  margin-bottom: 1.875rem !important;
}

.list-check {
  margin: 0;
  padding: 0;
  list-style: none;
  font-weight: 500;
  color: var(--educrat-heading-color);
  font-size: 15px;
  text-transform: capitalize;
}

.list-check li {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 0.625rem;
}

@media (min-width: 1200px) {
  .list-check li {
    margin-bottom: 20px;
  }
}

.list-check li:last-child {
  margin-bottom: 0;
}

.list-check i {
  color: #fff;
  background: var(--educrat-theme-color);
  font-size: 8px;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.list-border-check {
  margin: 0;
  padding: 0;
  list-style: none !important;
}

.list-border-check i {
  color: #6A7A99;
  border: 2px solid #EDEDED;
  font-size: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-top: 2px;
}

.list-border-check li {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  margin-bottom: 0.625rem;
}

@media (min-width: 1200px) {
  .list-border-check li {
    margin-bottom: 20px;
  }
}

.list-border-check li:last-child {
  margin-bottom: 0;
}

.list-detail-course {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-detail-course i {
  font-size: 13px;
  margin-right: 10px;
}

.list-detail-course li {
  margin-bottom: 12px;
}

.widget-box .item.col-sm-12 {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .widget-box .item.col-sm-12 {
    margin-bottom: 25px;
  }
}

.widget-box .item.col-sm-12:last-child {
  margin-bottom: 0;
}

.item-box {
  -webkit-transition: all 0.25s ease-in-out 0s;
  -o-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
}

.item-box .features-box-content {
  padding-left: 0.9375rem;
}

@media (min-width: 1200px) {
  .item-box .features-box-content {
    padding-left: 1.875rem;
  }
}

.item-box .number {
  width: 37px;
  height: 37px;
  text-align: center;
  line-height: 37px;
  border-radius: 50%;
  background: var(--educrat-link-color);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
}

.item-box .title {
  -webkit-transition: all 0.25s ease-in-out 0s;
  -o-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 5px;
}

.item-box .features-box-image {
  -webkit-transition: all 0.25s ease-in-out 0s;
  -o-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  width: 50px;
  height: 50px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #E3EDFD;
  color: var(--educrat-theme-color);
  font-size: 30px;
  border-radius: 50%;
}

@media (min-width: 1200px) {
  .item-box .features-box-image {
    width: 80px;
    height: 80px;
    font-size: 40px;
  }
}

.item-box .features-box-image img,
.item-box .features-box-image i:before {
  vertical-align: middle;
}

.item-box .features-box-image a {
  display: inline-block;
  line-height: 0;
}

.item-box.st2 {
  align-items: center;
}

.item-box.st2 .title {
  font-size: 20px;
  margin: 0;
}

.item-box.st2 .features-box-image {
  line-height: 1;
  width: auto;
  height: auto;
  background: transparent;
  font-size: 50px;
}

@media (min-width: 1200px) {
  .item-box.st2 .features-box-content {
    padding-left: 20px;
  }
}

.item-box.st3 {
  flex-direction: column;
  text-align: center;
}

.item-box.st3 .features-box-content {
  padding: 15px 0 0;
}

@media (min-width: 1200px) {
  .item-box.st3 .features-box-content {
    padding: 25px 0 0;
  }
}

.item-box.st3 .number {
  position: absolute;
  top: -3px;
  left: -3px;
  z-index: 1;
}

.item-box.st3 .top-inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
}

.app-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 8px;
  background: #21242b;
}

@media (min-width: 1200px) {
  .app-wrapper {
    padding: 0.6rem 1.5rem;
  }
}

.app-wrapper .aps_ico {
  margin-right: 8px;
  width: 35px;
}

.app-wrapper .aps_ico img {
  margin: 0 !important;
}

.app-wrapper .aps_capt {
  color: #fff;
  font-size: 13px;
  font-weight: 600;
}

.app-wrapper h4 {
  color: #ffffff;
  margin: 0;
  font-size: 1rem;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .app-wrapper h4 {
    font-size: 21px;
  }
}

.app-wrapper:hover, .app-wrapper:focus {
  background: #0b0c0e;
}

.app-wrapper.bg-theme:hover, .app-wrapper.bg-theme:focus {
  background: var(--educrat-theme-hover-color);
}

.under-theme {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.text-underline {
  text-decoration: underline !important;
}

.space-b-20 {
  margin-bottom: 20px;
}

.box-white {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .box-white {
    padding: 50px 60px;
  }
}

.box-white .title {
  margin: 0;
  font-size: 30px;
}

@media (min-width: 1200px) {
  .box-white .title {
    font-size: 55px;
  }
}

.recommen-inner {
  position: relative;
}

.recommen-inner .form-select {
  background-color: #EEF2F6;
  border-radius: 60px;
  outline: none;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  height: 50px;
  min-width: 200px;
  color: var(--educrat-link-color);
  font-weight: 500;
  border: 2px solid #EEF2F6;
}

@media (min-width: 1200px) {
  .recommen-inner .form-select {
    height: 60px;
  }
}

.recommen-inner .form-select:focus {
  border-color: #d7e0ea;
}

.recommen-inner > *:not(.wpcf7-spinner) {
  display: block;
  margin-right: 0.9375rem;
}

@media (min-width: 1200px) {
  .recommen-inner > *:not(.wpcf7-spinner) {
    margin-right: 1.875rem;
  }
}

.recommen-inner > *:not(.wpcf7-spinner):last-child {
  margin-right: 0;
}

@media (max-width: 575px) {
  .recommen-inner > *:not(.wpcf7-spinner) {
    width: 100%;
    margin: 0 0 10px;
  }
}

.recommen-inner option {
  background: #fff;
}

.recommen-inner [type="submit"] {
  margin: 0;
  border-radius: 60px;
}

@media (min-width: 1200px) {
  .recommen-inner .wpcf7-spinner {
    position: absolute;
    top: 50%;
    left: 100%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

@media (max-width: 767px) {
  .recommen-inner .wpcf7-spinner {
    margin: 10px 0 0;
  }
}

.recommen-inner.st_white .form-select {
  background-color: #fff;
  border-color: #fff;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-mobile-title {
  display: none !important;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tabs-wrapper {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  overflow: auto;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tabs-wrapper:before {
  content: '';
  background: #EDEDED;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: 0;
  left: 0;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title {
  font-size: 15px;
  font-weight: 400;
  color: var(--educrat-text-color);
  padding: 15px 0;
  border: 0;
  margin: 0;
  position: relative;
  z-index: 1;
  display: block;
  white-space: nowrap;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: inherit;
  height: 2px !important;
  width: 0;
  background: var(--educrat-theme-color);
  border: 0 !important;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title:after {
  display: none !important;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title.elementor-active {
  color: var(--educrat-theme-color);
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title.elementor-active:before {
  width: 100% !important;
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title + .elementor-tab-desktop-title {
  margin-left: 15px;
}

@media (min-width: 1200px) {
  .elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tab-desktop-title + .elementor-tab-desktop-title {
    margin-left: 35px;
  }
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tabs-content-wrapper {
  border: 0;
  padding: 30px 0 0;
}

@media (min-width: 1200px) {
  .elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tabs-content-wrapper {
    padding: 50px 0 0;
  }
}

.elementor-tabs-view-horizontal.elementor-widget-tabs .elementor-tabs-content-wrapper .elementor-tab-content {
  padding: 0;
  border: 0;
}

/* 11. widgets */
/*------------------------------------*\
    Widget
\*------------------------------------*/
.widget label {
  font-weight: 400;
}

.widget .widget_sp_image-image-link {
  display: block;
  overflow: hidden;
  position: relative;
}

.widget.widget_text img {
  margin: 15px 0;
  height: auto;
}

.widget.widget_recent_comments ul li {
  background: none;
}

.widget.widget_recent_reviews ul.product_list_widget {
  list-style: none;
}

.widget.widget_recent_reviews ul.product_list_widget li {
  padding: 15px;
  overflow: hidden;
}

.widget.widget_recent_reviews ul.product_list_widget li a img {
  float: left;
  margin-right: 10px;
}

.widget.widget_product_search .woocommerce-product-search {
  padding: 20px 15px;
}

.widget.widget_product_search .woocommerce-product-search label.screen-reader-text {
  display: none;
}

.widget.yith-woocompare-widget .products-list {
  padding-top: 20px;
  padding-bottom: 20px;
}

.widget.yith-woocompare-widget a.clear-all {
  margin-bottom: 20px;
  margin-left: 15px;
}

.widget.yith-woocompare-widget a.compare {
  margin-bottom: 20px;
  margin-right: 15px;
}

.widget.widget_shopping_cart .widget_shopping_cart_content {
  padding: 20px 15px;
  overflow: hidden;
}

.widget.widget_recent_entries ul li a {
  display: block;
}

.widget.widget_calendar table {
  margin: 0;
  width: 100%;
}

.calendar_wrap caption {
  background: #212121;
  color: #fff;
  padding: 5px;
}

.calendar_wrap td, .calendar_wrap th {
  text-align: center;
}

.calendar_wrap tfoot {
  display: none;
}

.calendar_wrap #today {
  font-weight: normal;
  color: var(--educrat-theme-color);
}

.form-contact .title {
  font-size: 17px;
  margin: 11.5px 0 28px;
}

.form-contact input:not(.btn),
.form-contact textarea {
  padding: 10px 30px;
  width: 100%;
  color: var(--educrat-text-color);
}

.form-contact .contant-inner > * {
  margin: 0 0 20px;
}

.contact-topbar > * {
  margin-right: 1.875rem;
}

.contact-topbar > *:last-child {
  margin: 0;
}

.contact-topbar > * i {
  margin-right: 6px;
}

.widget_pages ul,
.widget_nav_menu ul,
.widget_meta ul,
.widget_archive ul,
.widget_recent_entries ul,
.widget_categories ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.widget_pages ul ul,
.widget_nav_menu ul ul,
.widget_meta ul ul,
.widget_archive ul ul,
.widget_recent_entries ul ul,
.widget_categories ul ul {
  padding-left: 20px;
  margin-top: 8px;
}

.widget_pages ul li,
.widget_nav_menu ul li,
.widget_meta ul li,
.widget_archive ul li,
.widget_recent_entries ul li,
.widget_categories ul li {
  list-style: none;
  margin-bottom: 8px;
}

.widget_pages ul li:last-child,
.widget_nav_menu ul li:last-child,
.widget_meta ul li:last-child,
.widget_archive ul li:last-child,
.widget_recent_entries ul li:last-child,
.widget_categories ul li:last-child {
  margin-bottom: 0;
}

.widget_pages ul li:hover > a,
.widget_pages ul li.current-cat-parent > a,
.widget_pages ul li.current-cat > a,
.widget_nav_menu ul li:hover > a,
.widget_nav_menu ul li.current-cat-parent > a,
.widget_nav_menu ul li.current-cat > a,
.widget_meta ul li:hover > a,
.widget_meta ul li.current-cat-parent > a,
.widget_meta ul li.current-cat > a,
.widget_archive ul li:hover > a,
.widget_archive ul li.current-cat-parent > a,
.widget_archive ul li.current-cat > a,
.widget_recent_entries ul li:hover > a,
.widget_recent_entries ul li.current-cat-parent > a,
.widget_recent_entries ul li.current-cat > a,
.widget_categories ul li:hover > a,
.widget_categories ul li.current-cat-parent > a,
.widget_categories ul li.current-cat > a {
  color: var(--educrat-theme-color);
}

ul#recentcomments,
ol.wp-block-latest-comments {
  padding: 0;
}

ul#recentcomments li,
ol.wp-block-latest-comments li {
  line-height: 1.8;
  padding: 8px 0;
  margin: 0;
}

ul#recentcomments li:first-child,
ol.wp-block-latest-comments li:first-child {
  padding-top: 0;
}

ul#recentcomments li:last-child,
ol.wp-block-latest-comments li:last-child {
  padding-bottom: 0;
}

ul#recentcomments a,
ol.wp-block-latest-comments a {
  text-decoration: underline;
}

.special .post-info {
  position: relative;
}

.special .post-info:before {
  border-width: 14px 20px;
  border-style: solid;
  border-color: #f4f4f4 transparent transparent;
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -10px;
  z-index: 2;
}

.special .special-items > div:nth-child(2n) .post-info {
  position: relative;
}

.special .special-items > div:nth-child(2n) .post-info:before {
  border-color: transparent transparent #f4f4f4;
  top: inherit;
  bottom: 100%;
}

/*------------------------------------*\
    Widget Contact Us
\*------------------------------------*/
.contact {
  margin: 0;
  padding: 0;
}

.contact::after {
  display: block;
  clear: both;
  content: "";
}

.contact dt {
  float: left;
  width: 30px;
  height: auto;
}

.contact dd {
  overflow: hidden;
  margin-bottom: 5px;
}

.contact .contact-icon {
  display: block;
  text-align: center;
  background: var(--educrat-theme-color);
  float: left;
  width: 22px;
  height: 22px;
  border-radius: 2px;
}

.contact .contact-icon .fa {
  color: #fff;
  font-size: 0.9375rem;
  margin: 0 0 0 4px;
}

/*------------------------------------*\
    Widget Mailchip
\*------------------------------------*/
.mail-form .input-group {
  width: 100%;
  margin: 0 0 10px;
}

/*------------------------------------*\
    Widget Sidebar
\*------------------------------------*/
.apus-sidebar select, .apus-sidebar table {
  width: 100%;
}

.apus-sidebar .post-widget .blog-title, .apus-sidebar .post-widget h6 {
  margin: 0 0 5px;
  line-height: 1.4;
  font-weight: 400;
  height: 40px;
  overflow: hidden;
  font-family: var(--bs-font-sans-serif);
}

/*------------------------------------*\
    search
\*------------------------------------*/
.apus-search-form .select-category {
  display: inline-block;
  float: left;
  overflow: hidden;
  position: relative;
  min-width: 200px;
  padding-right: 12px;
  outline: none !important;
}

.apus-search-form .select-category:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  background: #dddddd;
  width: 1px;
  height: 20px;
}

.apus-search-form .select-category .dropdown_product_cat {
  border: 0;
  outline: none !important;
  width: calc(100% + 38px);
  height: 48px;
  padding: 0 20px;
}

.apus-search-form .title-top-search {
  font-size: 24px;
  color: var(--educrat-link-color);
}

.apus-search-form .close-search-fix {
  font-size: 24px;
  color: #f33066;
  cursor: pointer;
}

.apus-search-form .close-search-fix:hover, .apus-search-form .close-search-fix:active {
  color: #e30d48;
}

.apus-search-form .select2-container .select2-selection--single {
  background: #fff;
  height: 48px;
  margin: 0;
  font-size: 16px;
  color: var(--educrat-link-color);
  outline: none !important;
}

.apus-search-form .select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 20px;
}

.apus-search-form form {
  border: 1px solid #EDEDED;
  display: table;
  width: 100%;
}

.apus-search-form form .main-search,
.apus-search-form form .btn,
.apus-search-form form > .select-category {
  display: table-cell !important;
  vertical-align: middle;
  float: none !important;
}

.apus-search-form form .btn {
  height: 50px;
  line-height: 1;
  margin-top: -1px;
  margin-bottom: -1px;
  margin-right: -1px;
}

.apus-search-form form .btn i {
  font-size: 18px;
}

.apus-search-form form .btn i + span {
  margin-left: 5px;
}

.apus-search-form form .btn.st_small {
  padding-left: 15px;
  padding-right: 15px;
}

.apus-search-form form .form-control {
  border: none;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 0;
}

.apus-search-form form .form-control {
  height: 48px;
}

.apus-search-form form .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #d1d1d1;
}

.apus-search-form form .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #d1d1d1;
}

.apus-search-form form .form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: #d1d1d1;
}

.apus-search-form form .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: #d1d1d1;
}

.apus-search-form form .form-control:focus {
  color: var(--educrat-link-color);
}

.apus-search-form .hidden-search {
  cursor: pointer;
  float: right;
  font-size: 35px;
  line-height: 1.4;
  color: #f33066;
  display: inline-block;
  margin-left: 30px;
}

.apus-search-form .main-search {
  width: 100%;
  position: relative;
}

.apus-search-form .main-search .autocomplete-list {
  text-align: left;
  margin-top: 1px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 8;
  background: #fff;
  -webkit-box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.15);
  max-height: 350px;
  overflow: auto;
}

.apus-search-form div.twitter-typeahead {
  width: 100%;
  position: relative;
}

.apus-search-form div.twitter-typeahead span.twitter-typeahead {
  vertical-align: top;
  width: 100%;
}

.apus-search-form div.twitter-typeahead:before {
  content: '';
  position: absolute;
  top: 13px;
  right: 10px;
  width: 18px;
  height: 100%;
  background: url(../images/loading-quick.gif) no-repeat scroll 0 0/18px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: 9;
}

.apus-search-form div.twitter-typeahead.loading:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-search-form .tt-menu {
  background: #fff;
  width: 100%;
  padding: 0;
  margin-top: 1px;
}

.apus-search-form .tt-menu > * {
  position: relative;
  z-index: 9;
}

.apus-search-form .tt-menu a.media {
  display: block;
  margin: 0;
  padding: 12px;
}

.apus-search-form .tt-menu a.media img {
  max-width: 60px;
}

.apus-search-form .tt-menu h4 {
  font-size: 14px;
  margin: 0;
}

.apus-search-form .tt-menu h4 strong {
  font-weight: normal;
  color: var(--educrat-theme-color);
}

.apus-search-form .tt-menu .price {
  font-size: 13px;
  margin: 0;
}

.apus-search-form .tt-menu .tt-dataset-search > *:first-child {
  display: none;
}

.apus-search-form.style2 form {
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  position: relative;
  padding-right: 40px;
}

.apus-search-form.style2 form .form-control {
  border: none !important;
  height: 45px;
  background-color: transparent;
  color: #fff;
}

.apus-search-form.style2 form .form-control:focus {
  color: #fff;
}

.apus-search-form.style2 form .btn {
  line-height: 32px;
  height: 35px;
  font-size: 16px;
  position: absolute;
  top: 6px;
  padding: 0 9px;
  right: 5px;
  border-radius: 50% !important;
}

.apus-search-form.style2 form .btn i {
  font-size: 16px;
}

.apus-search-form .autocomplete-list {
  padding: 15px;
  border-radius: 2px;
}

@media (min-width: 1200px) {
  .apus-search-form .autocomplete-list {
    padding: 20px;
  }
}

.apus-search-form .autocomplete-list-item {
  padding: 0 0 10px;
  margin: 0 0 10px;
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .apus-search-form .autocomplete-list-item {
    padding: 0 0 15px;
    margin: 0 0 15px;
  }
}

.apus-search-form .autocomplete-list-item:last-child {
  border: none;
  padding: 0;
  margin: 0;
}

.apus-search-form .autocomplete-list-item .autocompleate-media {
  display: block;
}

.apus-search-form .autocomplete-list-item .autocompleate-media:hover .name-product {
  color: var(--educrat-link-color);
}

.apus-search-form .autocomplete-list-item img {
  width: 60px;
  max-width: none;
}

.apus-search-form .autocomplete-list-item .price {
  color: var(--educrat-text-color);
}

.apus-search-form .autocomplete-list-item .name-product {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin: 0 0 3px;
  font-size: 15px;
  font-family: var(--educrat-main-font);
  font-weight: normal;
  color: var(--educrat-text-color);
  text-transform: capitalize;
}

.apus-search-form .autocomplete-list-item .name-product strong {
  color: #f33066;
}

.apus-search-nocategory {
  background: #f0f2f9;
  border-radius: 50px;
}

.apus-search-nocategory .form-control {
  background: #f0f2f9;
  border-color: #f0f2f9;
  color: #999591;
  border: none;
  max-width: 185px;
  font-size: 12px;
}

.apus-search-nocategory .btn {
  padding-left: 12px;
  padding-right: 12px;
  background: transparent;
  color: var(--educrat-link-color);
  font-size: 16px;
  border-radius: 50% !important;
  border: none;
}

.apus-search-nocategory .btn:hover, .apus-search-nocategory .btn:active {
  color: #fff;
  background: var(--educrat-theme-color);
}

.apus-search-nocategory {
  background: #f0f2f9;
  border-radius: 50px;
}

.apus-search-nocategory .form-control {
  background: #f0f2f9;
  border-color: #f0f2f9;
  color: #999591;
  border: none;
  max-width: 185px;
  font-size: 12px;
}

.apus-search-nocategory .btn {
  padding-left: 12px;
  padding-right: 12px;
  background: transparent;
  color: var(--educrat-link-color);
  font-size: 16px;
  border-radius: 50% !important;
  border: none;
}

.apus-search-nocategory .btn:hover, .apus-search-nocategory .btn:active {
  color: #fff;
  background: var(--educrat-theme-color);
}

.widget_search .form-control {
  border-color: transparent;
  background: transparent;
}

.widget_search .btn {
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1;
  font-size: 20px;
  line-height: 1;
  padding: 8px 18px;
  border: 0;
  background: transparent !important;
  color: var(--educrat-link-color);
  margin-top: 2px;
}

.widget_search .btn:hover, .widget_search .btn:focus {
  color: var(--educrat-theme-color);
}

.widget_search form {
  position: relative;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  padding-left: 32px;
}

/*------------------------------------*\
    Tags Widget
\*------------------------------------*/
.wp-block-tag-cloud,
.entry-tags-list,
.tagcloud {
  margin: 0;
  overflow: hidden;
}

.wp-block-tag-cloud a,
.entry-tags-list a,
.tagcloud a {
  text-transform: capitalize;
  margin-bottom: 10px;
  margin-right: 10px;
  font-size: 0.75rem !important;
  font-weight: 500;
  display: inline-block;
  float: left;
  padding: 4px 18px;
  -webkit-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  color: var(--educrat-link-color);
  background: #EEF2F6;
  border-radius: 30px;
}

.wp-block-tag-cloud a:hover, .wp-block-tag-cloud a:focus, .wp-block-tag-cloud a.active,
.entry-tags-list a:hover,
.entry-tags-list a:focus,
.entry-tags-list a.active,
.tagcloud a:hover,
.tagcloud a:focus,
.tagcloud a.active {
  background-color: var(--educrat-theme-color);
  color: #fff;
}

.wp-block-tag-cloud a:last-child,
.entry-tags-list a:last-child,
.tagcloud a:last-child {
  margin-right: 0;
}

.widget-logo {
  padding: 1.875rem;
}

.widget-logo .item-brand > a {
  display: block;
}

.widget-logo .carousel-control {
  opacity: 0;
  filter: alpha(opacity=0);
}

.widget-logo:hover .carousel-control {
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-search-top .button-show-search {
  font-size: 18px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: none;
  color: var(--educrat-text-color);
  line-height: 1;
  padding: 0 5px;
  background: transparent !important;
}

.apus-search-top .button-show-search:hover, .apus-search-top .button-show-search:active {
  color: var(--educrat-theme-color);
}

.apus-search-top .content-form {
  -webkit-box-shadow: none;
  box-shadow: none;
  margin: 0;
  border: none;
  padding: 0;
  min-width: 280px;
}

/*-----------------------------*\
    Widget Vertical Menu
\*-----------------------------*/
.vertical-menu {
  display: none !important;
  padding: 0;
  background: #fff;
  z-index: 999;
}

.vertical-menu > .nav {
  position: relative;
  width: 100%;
  height: auto;
}

.vertical-menu > .nav .open > a, .vertical-menu > .nav .open > a:hover, .vertical-menu > .nav .open > a:focus,
.vertical-menu > .nav .active > a,
.vertical-menu > .nav .active > a:hover,
.vertical-menu > .nav .active > a:focus {
  color: var(--educrat-theme-color);
}

.vertical-menu > .nav > li {
  float: none;
  position: static;
  border-bottom: 1px solid #EDEDED;
}

.vertical-menu > .nav > li.active > a {
  color: var(--educrat-theme-color);
  background: #fafafa;
}

.vertical-menu > .nav > li > a {
  color: #414141;
  padding: 15px 20px;
  font-weight: 500;
  font-size: 12px;
  text-transform: uppercase;
  white-space: nowrap;
}

.vertical-menu > .nav > li > a:hover {
  color: var(--educrat-theme-color);
  background: #fafafa;
}

.vertical-menu > .nav > li > a .fa {
  font-size: 15px;
  min-width: 15px;
  margin-right: 12px;
}

.vertical-menu > .nav > li .dropdown-menu {
  min-width: 230px;
  min-height: 100%;
  border-radius: 0;
}

.vertical-menu > .nav .product-block {
  padding: 0 !important;
  overflow: hidden;
  display: block;
}

.vertical-menu .dropdown-menu {
  margin: 0;
  padding: 1.875rem;
  border: none;
  top: 0;
}

.vertical-menu .dropdown-menu::after {
  display: block;
  clear: both;
  content: "";
}

.vertical-menu .dropdown-menu ul {
  padding: 0;
  list-style: none;
}

.vertical-menu .dropdown-menu ul li {
  line-height: 34px;
}

.vertical-menu .dropdown-menu ul li a {
  color: #414141;
}

.vertical-menu .dropdown-menu ul li a:hover, .vertical-menu .dropdown-menu ul li a.active {
  color: var(--educrat-theme-color);
}

.vertical-menu .dropdown-menu ul ul {
  padding-left: 15px;
}

.vertical-menu .dropdown-menu .widget-title {
  border: none;
  font-size: 16px;
  padding: 0 0 15px;
  color: var(--educrat-link-color);
}

.vertical-menu .dropdown-menu .woocommerce .product-wrapper {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.vertical-menu.menu-left .dropdown-menu {
  left: 100% !important;
  right: auto !important;
}

.vertical-menu.menu-right .dropdown-menu {
  left: auto !important;
  right: 100% !important;
}

.vertical-menu .icon-ver {
  margin-right: 10px;
}

#recentcomments {
  list-style: none;
}

#recentcomments > li {
  margin: 0 0 1em;
}

#recentcomments > li:last-child {
  margin: 0;
}

.widget_rss ul {
  list-style: none;
  padding: 0;
}

.widget_rss ul li {
  list-style: none;
  padding: 15px 0;
  margin: 0;
  border-bottom: 1px solid #EDEDED;
}

.widget_rss ul li:first-child {
  padding-top: 0;
}

.widget_rss ul li:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}

.widget-quicklink-menu {
  background-color: #f6f6f6;
  line-height: 35px;
}

.widget-quicklink-menu .quicklink-heading {
  background-color: #333333;
  color: #ffffff;
  display: inline-block;
  font-size: 10px;
  margin: 0 20px 0 0;
  padding: 12px 15px 12px 25px;
  position: relative;
  text-transform: uppercase;
  font-family: var(--educrat-main-font);
}

.woo-onsale .onsale {
  display: none;
}

.woo-onsale .product-sale-label {
  position: absolute;
  width: 36px;
  height: 36px;
  background-color: #fb4949;
  color: #fff;
  top: 10px;
  right: 10px;
  border-radius: 50%;
  line-height: 36px;
  font-size: 12px;
  font-weight: 400;
}

.widget-tabs .widget-title {
  display: inline-block;
}

.widget-tabs .nav-tabs {
  border: none;
  display: inline-block;
  vertical-align: middle;
  margin: 0 0 7px;
}

.widget-tabs .nav-tabs.tabs-list-v2 {
  margin: 0 0 15px;
}

.widget-tabs .carousel-controls {
  top: -42px;
}

.widget-infor .media .fa, .widget-infor .media .icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: var(--educrat-theme-color);
  background: #252525;
}

.contant-info .title {
  margin: 0 0 20px;
  font-size: 30px;
}

.contant-info .info-description {
  margin: 0 0 30px;
}

.contant-info .media-heading {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 5px;
}

.contant-info .media {
  margin-top: 30px;
}

.contant-info .media:hover .fa {
  border-color: #f33066;
  color: #f33066;
}

.contant-info .media-left {
  padding-right: 20px;
}

.contant-info .media-left .fa {
  border: 2px solid #0d6efd;
  border-radius: 50%;
  color: #0d6efd;
  font-size: 25px;
  height: 58px;
  line-height: 52px;
  text-align: center;
  width: 58px;
}

.widget_text select,
.widget_categories select,
.widget_archive select {
  width: 100%;
  padding: 8px 15px;
  height: 50px;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-appearance: none;
  -moz-appearance: none;
  -o-appearance: none;
  background: url("../images/select.png") #fff right 15px center no-repeat;
}

.widget-twitter .twitter-timeline {
  display: block !important;
}

.widget-twitter .timeline-Tweet-media {
  display: none;
}

.widget_apus_instagram {
  margin: 0;
}

.widget_apus_instagram .widget-title {
  font-size: 35px;
  font-weight: 300;
  margin: 0 0 60px;
  padding: 0;
  text-align: center;
  text-transform: inherit;
}

.widget_apus_instagram .widget-title a {
  font-weight: 400;
  color: var(--educrat-theme-color);
}

.widget_apus_instagram .instagram-pics a {
  display: block;
  position: relative;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}

.widget_apus_instagram .instagram-pics a:hover, .widget_apus_instagram .instagram-pics a:active {
  outline: 8px solid var(--educrat-theme-color);
  outline-offset: -8px;
}

.widget_apus_instagram .instagram-pics a:hover:before, .widget_apus_instagram .instagram-pics a:active:before {
  opacity: 0;
  filter: alpha(opacity=0);
}

.widget_apus_instagram .instagram-pics a:before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  filter: alpha(opacity=50);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.widget-ticket-pricing {
  background: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border: 2px dashed #EDEDED;
  border-radius: 50px;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .widget-ticket-pricing .product-block-pricing {
    max-width: 170px;
    margin: auto;
  }
}

.widget-ticket-pricing .column {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  overflow: hidden;
  border: 2px dashed #EDEDED;
  border-radius: 50px;
  margin: -2px 0;
  padding: 0 10px !important;
}

.widget-ticket-pricing .column:last-child, .widget-ticket-pricing .column:first-child {
  border: none;
  margin: 0;
}

.widget-ticket-pricing.style-style2 {
  border: 1px solid #EDEDED;
  overflow: visible;
}

.widget-ticket-pricing.style-style2 .column {
  border: 1px solid #EDEDED;
  margin: -1px 0;
}

.widget-ticket-pricing.style-style2 .column:last-child, .widget-ticket-pricing.style-style2 .column:first-child {
  margin: 0;
  border: none;
}

.widget-ticket-pricing.style-style2 .column:hover {
  border-color: var(--educrat-theme-color);
}

.widget-ticket-pricing.style-style2 .column:hover .product-block-pricing .wrapper-pricing .price {
  border: 1px solid var(--educrat-theme-color);
}

.widget-ticket-pricing.style-style2 .column:hover .product-block-pricing .wrapper-pricing:before {
  border-bottom: 1px solid var(--educrat-theme-color);
}

.widget-ticket-pricing.style-style2 .product-block-pricing .wrapper-pricing .price {
  border: 1px solid #EDEDED;
}

.widget-ticket-pricing.style-style2 .product-block-pricing .wrapper-pricing:before {
  border-bottom: 1px solid #EDEDED;
}

.widget-ticket-pricing.style-style3 {
  border: none;
  overflow: visible;
}

.widget-ticket-pricing.style-style3 .column {
  border: none;
  overflow: visible;
  margin: 20px 0;
}

.product-block-pricing .name {
  font-size: 22px;
  font-family: var(--educrat-main-font);
  margin: 37px 0 30px;
  font-weight: 400;
  text-align: center;
}

.product-block-pricing .wrapper-pricing {
  text-align: center;
  position: relative;
}

.product-block-pricing .wrapper-pricing:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 1000px;
  height: 2px;
  border-bottom: 2px dashed #EDEDED;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: -150px;
  z-index: 1;
  content: '';
}

.product-block-pricing .wrapper-pricing .price {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border: 2px dashed #EDEDED;
  border-radius: 50%;
  padding: 8px;
  background: #fff;
  display: inline-block;
  z-index: 2;
  position: relative;
}

.product-block-pricing .woocommerce-Price-amount {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 100px;
  height: 100px;
  line-height: 100px;
  display: inline-block;
  text-align: center;
  font-size: 30px;
  font-weight: normal;
  color: var(--educrat-link-color);
  background: #f1f1f1;
  border-radius: 50%;
}

.product-block-pricing .woocommerce-Price-amount > span {
  font-weight: 300;
}

.product-block-pricing .block-inner-content .desc {
  margin: 20px 0 35px;
}

.product-block-pricing .block-inner-content .desc strong {
  color: var(--educrat-link-color);
}

.product-block-pricing .block-inner-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.product-block-pricing .block-inner-content ul li {
  margin: 0 0 5px;
}

.product-block-pricing .block-inner-content ul i {
  margin-right: 15px;
  color: var(--educrat-theme-color);
}

.product-block-pricing .button {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.product-block-pricing .groups-button {
  margin: 40px 0 45px;
}

.product-block-pricing .groups-button .button.added {
  display: none;
}

.product-block-pricing .groups-button .added_to_cart.wc-forward {
  display: inline-block;
  padding: 3.875rem 0.6rem;
  white-space: nowrap;
  vertical-align: middle;
  font-size: 14px;
  font-weight: normal;
  text-transform: uppercase;
  border-radius: 25px;
  background: var(--educrat-theme-color);
  color: #fff;
}

.product-block-pricing .groups-button .added_to_cart.wc-forward:hover, .product-block-pricing .groups-button .added_to_cart.wc-forward:active {
  color: #fff;
  background: var(--educrat-theme-hover-color);
}

.product-block-pricing:hover .woocommerce-Price-amount {
  background-color: var(--educrat-theme-color);
  color: #fff;
}

.product-block-pricing:hover .button {
  color: #fff !important;
}

.product-block-pricing:hover .button:before {
  opacity: 0;
  filter: alpha(opacity=0);
}

.popupnewsletter-wrapper .mfp-content {
  width: 590px;
  max-width: 90%;
}

.popupnewsletter-wrapper .apus-mfp-close {
  background: #f33066;
  color: #fff;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 16px;
  line-height: 47px;
  border-radius: 50%;
  -webkit-transform: translate(22px, -22px);
  -ms-transform: translate(22px, -22px);
  -o-transform: translate(22px, -22px);
  transform: translate(22px, -22px);
  opacity: 1;
  filter: alpha(opacity=100);
}

.popupnewsletter-wrapper .apus-mfp-close:hover, .popupnewsletter-wrapper .apus-mfp-close:focus {
  background: #f21854;
}

.popupnewsletter-wrapper .modal-content {
  border-radius: 0;
  padding: 260px 60px 40px;
  text-align: center;
}

.popupnewsletter-wrapper .modal-content h3 {
  font-size: 20px;
  margin: 0 0 15px;
}

@media (min-width: 1200px) {
  .popupnewsletter-wrapper .modal-content h3 {
    font-size: 30px;
  }
}

.popupnewsletter-wrapper .modal-content .description {
  font-family: var(--educrat-main-font);
  font-size: 16px;
  margin: 0 0 20px;
}

.popupnewsletter-wrapper .modal-content form {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #EDEDED;
  width: 325px;
  background: #f5f6f7;
  clear: both;
  margin: 0 auto 20px;
  border-radius: 46px;
  position: relative;
  padding-right: 46px;
}

@media (min-width: 1200px) {
  .popupnewsletter-wrapper .modal-content form {
    margin: 0 auto 40px;
  }
}

.popupnewsletter-wrapper .modal-content form:hover {
  border-color: #d4d4d4;
}

.popupnewsletter-wrapper .modal-content form .form-control {
  background: #f5f6f7;
  width: 100%;
  border: none;
  border-radius: 46px;
  height: 44px;
  display: block;
}

.popupnewsletter-wrapper .modal-content form .input-group {
  position: static;
  width: 100%;
  display: block;
}

.popupnewsletter-wrapper .modal-content form .input-group > * {
  display: block;
  float: none;
  position: static;
}

.popupnewsletter-wrapper .modal-content form [type="submit"] {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 2 !important;
  top: -1px;
  right: -1px;
  z-index: 1;
  width: 46px;
  height: 46px;
  line-height: 46px;
  border-radius: 46px;
  display: inline-block;
  color: transparent;
  background: var(--educrat-theme-color);
}

.popupnewsletter-wrapper .modal-content form [type="submit"]:before {
  content: "\f1d8";
  font-family: 'FontAwesome';
  font-size: 18px;
  color: #fff;
  display: inline-block;
  width: 45px;
  text-align: center;
}

.popupnewsletter-wrapper .modal-content form [type="submit"]:hover, .popupnewsletter-wrapper .modal-content form [type="submit"]:focus {
  background-color: var(--educrat-theme-hover-color);
}

.popupnewsletter-wrapper .close-dont-show:hover, .popupnewsletter-wrapper .close-dont-show:focus {
  color: #f33066;
}

.form-login-register-inner {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.9375rem;
  border: 1px solid #EDEDED;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

@media (min-width: 1200px) {
  .form-login-register-inner {
    padding: 1.875rem;
  }
}

.form-login-register-inner .title {
  font-size: 18px;
  margin: 0 0 20px;
}

.form-login-register-inner #register-phone-cc + .select2 {
  width: 95px !important;
}

.form-login-register-inner #register-phone-cc + .select2 .select2-selection--single {
  border-radius: 4px 0 0 4px;
}

.form-login-register-inner #register-phone-cc + .select2 .select2-selection--single .select2-selection__rendered {
  padding: 10px 8px;
}

.form-login-register-inner #register-phone {
  width: calc(100% - 95px) !important;
  border-radius: 0 4px 4px 0;
  border-left: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.form-login-register-inner [for="user-remember-field"] {
  font-weight: 400;
}

.form-login-register-inner .lostpassword-link {
  margin-top: 15px;
}

.form-login-register-inner .top-login {
  background-color: #F4F4F4;
  border-radius: 8px 8px 0 0;
  padding: 10px 20px;
  margin: -20px -20px 20px;
}

@media (min-width: 1200px) {
  .form-login-register-inner .top-login {
    padding: 21px 40px;
    margin: -40px -40px 40px;
  }
}

.form-login-register-inner .nav-tabs {
  border: 0;
}

.form-login-register-inner .nav-tabs li + li {
  margin-left: 45px;
}

@media (min-width: 1200px) {
  .form-login-register-inner .nav-tabs li + li {
    margin-left: 25px;
  }
}

.form-login-register-inner .nav-tabs a {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--educrat-text-color);
}

.form-login-register-inner .nav-tabs a.active {
  color: var(--educrat-link-color);
}

@media (min-width: 576px) {
  .form-login-register-inner .forgot-password-text {
    text-align: right;
  }
}

@media (min-width: 1200px) {
  .form-login-register-inner [name="submitRegister"],
  .form-login-register-inner [name="submit"] {
    padding-top: 1rem;
    padding-bottom: 1rem;
    font-size: 1rem;
  }
}

.form-login-register-inner .close-magnific-popup {
  color: var(--educrat-link-color);
  font-size: 1rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.form-login-register-inner .close-magnific-popup:hover, .form-login-register-inner .close-magnific-popup:focus {
  color: #f33066;
}

.form-forgot-password-inner {
  display: none;
}

.register-form-otp {
  display: none;
}

.register-form-otp .resend {
  margin-top: 10px;
}

.register-form-otp .sent-txt {
  text-align: center;
  margin-bottom: 10px;
}

.register-form-otp .sent-txt strong {
  font-size: 18px;
}

.register-form-otp .sent-txt .no-change {
  color: #000;
  font-weight: 600;
  cursor: pointer;
}

.otp-input-cont {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  -webkit-justify-content: center;
  width: 100%;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
}

.otp-input-cont input {
  border-radius: 4px;
  border: 1px solid #EDEDED !important;
  -webkit-box-shadow: none;
  box-shadow: none;
  width: calc(20% - 20px);
  margin: 3px 10px 0;
  text-align: center;
  padding: 7px;
  outline: none;
}

.wrapper-tab-account {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #F6F8F9;
  border-radius: 8px 8px 0 0;
}

.wrapper-tab-account .nav-tabs {
  border: 0;
}

.wrapper-tab-account .nav-tabs > li {
  margin: 0;
}

.wrapper-tab-account .nav-tabs > li > a {
  border: 0 !important;
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 15px 20px;
  background-color: transparent;
  color: var(--educrat-link-color) !important;
}

@media (min-width: 1200px) {
  .wrapper-tab-account .nav-tabs > li > a {
    padding: 19px 30px;
  }
}

.wrapper-tab-account .nav-tabs > li.active > a:focus,
.wrapper-tab-account .nav-tabs > li.active > a {
  background-color: #fff;
}

.wrapper-tab-account .close-advance-popup {
  cursor: pointer;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 20px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 30px;
  height: 30px;
  color: var(--educrat-link-color);
  background-color: #E6E9EC;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  display: inline-block;
}

@media (min-width: 1200px) {
  .wrapper-tab-account .close-advance-popup {
    right: 30px;
  }
}

.wrapper-tab-account .close-advance-popup i {
  font-size: 10px;
}

.wrapper-tab-account .close-advance-popup:hover, .wrapper-tab-account .close-advance-popup:focus {
  background-color: #f33066;
  color: #fff;
}

.wrapper-tab-account + .tab-content {
  padding-top: 50px;
}

.login-form-wrapper .wp_listings_directory_candidate_show,
.register-form .wp_listings_directory_candidate_show {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.login-form-wrapper .form-group:last-child,
.register-form .form-group:last-child {
  margin-bottom: 0;
}

.login-form-wrapper .form-group > .required-field,
.register-form .form-group > .required-field {
  display: none;
}

.login-form-wrapper .back-link,
.register-form .back-link {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.login-form-wrapper .remember,
.register-form .remember {
  font-weight: 400;
}

.login-form-wrapper .select2-selection__arrow,
.register-form .select2-selection__arrow {
  display: none;
}

.login-form-wrapper [type="checkbox"],
.register-form [type="checkbox"] {
  margin-right: 2px;
}

.login-form-wrapper .info,
.register-form .info {
  font-size: 14px;
}

.login-form-wrapper .create-account,
.register-form .create-account {
  margin: 0 0 5px;
}

.login-form-wrapper .create-account .create,
.register-form .create-account .create {
  font-weight: 500;
}

.login-form-wrapper #forgot-password-form-wrapper,
.register-form #forgot-password-form-wrapper {
  display: none;
}

.login-form-wrapper #recaptcha-contact-form,
.register-form #recaptcha-contact-form {
  min-height: 88px;
}

.login-form-wrapper #recaptcha-contact-form > div,
.register-form #recaptcha-contact-form > div {
  margin: 0 auto 10px;
}

.sign-in-demo-notice {
  padding: 10px 15px;
  border-radius: 8px;
  background: #F6F8F9;
  line-height: 2;
}

@media (min-width: 1200px) {
  .sign-in-demo-notice {
    padding: 20px 30px;
  }
}

.sign-in-demo-notice strong {
  color: var(--educrat-link-color);
  font-weight: 600;
}

.forgotpassword-form,
.job-apply-email-form,
.change-password-form,
.delete-profile-form,
.register-form,
.login-form {
  position: relative;
}

.forgotpassword-form:before,
.job-apply-email-form:before,
.change-password-form:before,
.delete-profile-form:before,
.register-form:before,
.login-form:before {
  display: block;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9) url("../images/loading.gif") no-repeat center center/32px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: 2;
  visibility: hidden;
}

.forgotpassword-form.loading:before,
.job-apply-email-form.loading:before,
.change-password-form.loading:before,
.delete-profile-form.loading:before,
.register-form.loading:before,
.login-form.loading:before {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.inner-social {
  overflow: hidden;
  clear: both;
}

.inner-social > div {
  margin-bottom: 20px;
  text-align: center;
}

.inner-social > div a {
  display: inline-block;
  width: 100%;
  padding: 8px 20px;
  border-radius: 8px;
  border: 1px solid var(--educrat-theme-color);
  color: var(--educrat-theme-color);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .inner-social > div a {
    padding: 10px 30px;
  }
}

.inner-social > div a:hover {
  background-color: var(--educrat-theme-color);
  color: #fff !important;
}

.inner-social > div a > i {
  font-size: 16px;
  float: left;
  margin-top: 4px;
}

.inner-social > div a.facebook-login-btn {
  border-color: #1967D2;
  color: #1967D2;
}

.inner-social > div a.facebook-login-btn:hover, .inner-social > div a.facebook-login-btn:focus {
  background-color: #1967D2;
}

.inner-social > div a.google-login-btn {
  border-color: #D93025;
  color: #D93025;
}

.inner-social > div a.google-login-btn:hover, .inner-social > div a.google-login-btn:focus {
  background-color: #D93025;
}

.inner-social > div[class*="btn-wrapper"] {
  width: calc(50% - 10px);
  float: left;
}

.inner-social > div[class*="btn-wrapper"]:nth-child(2n) {
  margin-left: 20px;
}

.inner-social .line-header {
  position: relative;
  display: none;
}

.inner-social .line-header:before {
  content: '';
  position: absolute;
  top: 50%;
  width: 100%;
  height: 1px;
  background-color: #EDEDED;
  display: block;
}

.inner-social .line-header span {
  position: relative;
  z-index: 1;
  padding: 0 5px;
  background-color: #fff;
  text-transform: uppercase;
}

.wp-block-search .wp-block-search__label {
  font-size: 1.125rem;
  margin: 0 0 18px;
  text-transform: capitalize;
  color: var(--educrat-heading-color);
  font-weight: 500;
  line-height: 1.2;
}

.wp-block-search .wp-block-search__input {
  height: 50px;
  outline: none !important;
  padding: 5px 20px;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.wp-block-search .wp-block-search__input:hover, .wp-block-search .wp-block-search__input:focus {
  border-color: #bababa;
}

.wp-block-search .wp-block-search__button {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  height: 50px;
  padding: 0 20px;
  border: 0;
  background: var(--educrat-theme-color);
  color: #fff;
  border-radius: 8px;
}

.wp-block-search .wp-block-search__button:hover, .wp-block-search .wp-block-search__button:focus {
  background: var(--educrat-theme-hover-color);
}

.widget_icl_lang_sel_widget {
  display: inline-block;
  border-radius: 50px;
  padding: 4px 30px;
  background: #F7F8FB;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: relative;
}

.widget_icl_lang_sel_widget .title {
  margin: 0;
  font-weight: 400;
  font-size: 0.9375rem;
  color: var(--educrat-text-color);
}

.widget_icl_lang_sel_widget i {
  vertical-align: middle;
  color: var(--educrat-text-color);
  font-size: 20px;
  line-height: 1;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.widget_icl_lang_sel_widget > * {
  display: inline-block;
}

.widget_icl_lang_sel_widget:hover, .widget_icl_lang_sel_widget:focus {
  background: var(--educrat-link-color);
}

.widget_icl_lang_sel_widget:hover > i,
.widget_icl_lang_sel_widget:hover .title,
.widget_icl_lang_sel_widget:hover a.wpml-ls-item-toggle, .widget_icl_lang_sel_widget:focus > i,
.widget_icl_lang_sel_widget:focus .title,
.widget_icl_lang_sel_widget:focus a.wpml-ls-item-toggle {
  color: #fff !important;
}

.widget_icl_lang_sel_widget.st_white {
  background: rgba(255, 255, 255, 0.1);
}

.widget_icl_lang_sel_widget.st_white > i,
.widget_icl_lang_sel_widget.st_white .title,
.widget_icl_lang_sel_widget.st_white a.wpml-ls-item-toggle {
  color: #fff;
}

.widget_icl_lang_sel_widget.st_white:hover, .widget_icl_lang_sel_widget.st_white:focus {
  background: #fff;
}

.widget_icl_lang_sel_widget.st_white:hover > i,
.widget_icl_lang_sel_widget.st_white:hover .title,
.widget_icl_lang_sel_widget.st_white:hover a.wpml-ls-item-toggle, .widget_icl_lang_sel_widget.st_white:focus > i,
.widget_icl_lang_sel_widget.st_white:focus .title,
.widget_icl_lang_sel_widget.st_white:focus a.wpml-ls-item-toggle {
  color: var(--educrat-link-color) !important;
}

.wpml-ls-legacy-dropdown {
  width: auto;
}

.wpml-ls-legacy-dropdown > ul {
  list-style: none;
  padding: 0;
  margin: 0;
  position: static;
}

.wpml-ls-legacy-dropdown a {
  display: block;
  padding: 3px 30px;
  border: 0;
  background-color: transparent;
  line-height: 1.8;
}

.wpml-ls-legacy-dropdown a span {
  vertical-align: baseline;
}

.wpml-ls-legacy-dropdown .wpml-ls-current-language:hover .wpml-ls-sub-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  position: absolute;
  z-index: 2;
  left: 0;
  top: 100%;
  width: 100%;
  min-width: 120px;
  background-color: #fff;
  -webkit-box-shadow: 0px 25px 70px 0px rgba(1, 33, 58, 0.07);
  box-shadow: 0px 25px 70px 0px rgba(1, 33, 58, 0.07);
  border: 1px solid #EDEDED;
  border-radius: 12px;
  font-size: 0.9375rem;
  list-style: none;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin: 4px 0;
  padding: 8px 0;
  -webkit-transform: translateY(8px);
  -ms-transform: translateY(8px);
  -o-transform: translateY(8px);
  transform: translateY(8px);
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  padding: 3px 0 3px 6px;
  background-color: transparent;
  position: relative;
  color: var(--educrat-text-color);
  font-size: 13px;
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 10px;
  top: 100%;
  left: 0;
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:hover {
  color: var(--educrat-link-color);
}

.wpml-ls-legacy-dropdown .wpml-ls-link:hover, .wpml-ls-legacy-dropdown .wpml-ls-link:focus {
  color: var(--educrat-theme-color);
  text-decoration: underline;
}

.lp-archive-courses ul, .lp-archive-courses ol {
  list-style: initial;
  padding: 0;
}

.banner-content-wrapper {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.banner-content-wrapper .banner-title {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 0.9375rem;
  line-height: 1.5;
  font-weight: 500;
  margin: 0 0 2px;
}

@media (min-width: 1200px) {
  .banner-content-wrapper .banner-title {
    font-size: 17px;
  }
}

.banner-content-wrapper .number {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  color: var(--educrat-text-color);
  font-size: 13px;
}

.banner-content-wrapper .features-box-image {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.banner-content-wrapper.style1 {
  align-items: center;
  text-align: center;
  padding: 15px;
  background: #EEF2F6;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .banner-content-wrapper.style1 {
    padding: 25px 38px;
  }
}

.banner-content-wrapper.style1 .features-box-image {
  color: var(--educrat-theme-color);
  font-size: 25px;
  width: 50px;
  height: 50px;
  background: #fff;
  margin-bottom: 10px;
  border-radius: 50%;
  line-height: 1;
}

@media (min-width: 1200px) {
  .banner-content-wrapper.style1 .features-box-image {
    font-size: 45px;
    width: 90px;
    height: 90px;
    margin-bottom: 15px;
  }
}

.banner-content-wrapper.style1:hover, .banner-content-wrapper.style1:focus {
  background: #1A064F;
}

.banner-content-wrapper.style1:hover .number,
.banner-content-wrapper.style1:hover .banner-title, .banner-content-wrapper.style1:focus .number,
.banner-content-wrapper.style1:focus .banner-title {
  color: #fff;
}

.banner-content-wrapper.style2 {
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  background-color: var(--educrat-text-color);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .banner-content-wrapper.style2 {
    padding: 1.875rem;
  }
}

.banner-content-wrapper.style2:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: -1;
  position: absolute;
  opacity: 0;
  filter: alpha(opacity=0);
  background: var(--educrat-theme-color);
  width: 100%;
  height: 100%;
  content: '';
  top: 0;
  left: 0;
}

.banner-content-wrapper.style2 .number {
  color: #fff;
}

.banner-content-wrapper.style2 .banner-title {
  color: #fff;
}

.banner-content-wrapper.style2 .banner-content {
  color: #fff;
}

.banner-content-wrapper.style2 .right-inner {
  position: absolute;
  width: 100%;
  z-index: 1;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.banner-content-wrapper.style2:hover:before, .banner-content-wrapper.style2:focus:before {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.banner-content-wrapper.style3 {
  flex-direction: row !important;
  align-items: center;
}

.banner-content-wrapper.style3 .features-box-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  line-height: 1;
  overflow: hidden;
  border-radius: 8px;
}

.banner-content-wrapper.style3 .features-box-image + .right-inner {
  padding-left: 15px;
}

@media (min-width: 576px) {
  .banner-content-wrapper.style3 .features-box-image + .right-inner {
    padding-left: 20px;
  }
}

.banner-content-wrapper.style3 .right-inner {
  flex-grow: 1;
}

.banner-content-wrapper.style3:hover .banner-title {
  color: var(--educrat-theme-color);
}

.banner-content-wrapper.style4 {
  flex-direction: row !important;
  align-items: center;
  border: 1px solid #EDEDED;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
}

.banner-content-wrapper.style4 .features-box-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  font-size: 30px;
  line-height: 1;
  overflow: hidden;
  color: var(--educrat-text-color);
  background: #EEF2F6;
  border-radius: 50%;
}

@media (min-width: 1200px) {
  .banner-content-wrapper.style4 .features-box-image {
    width: 80px;
    height: 80px;
    font-size: 35px;
  }
}

.banner-content-wrapper.style4 .features-box-image + .right-inner {
  padding-left: 15px;
}

@media (min-width: 576px) {
  .banner-content-wrapper.style4 .features-box-image + .right-inner {
    padding-left: 20px;
  }
}

.banner-content-wrapper.style4 .right-inner {
  flex-grow: 1;
}

.banner-content-wrapper.style4:hover {
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

.banner-content-wrapper.style4:hover .banner-title {
  color: var(--educrat-theme-color);
}

.banner-content-wrapper.style4:hover .features-box-image {
  color: #fff;
  background: var(--educrat-theme-color);
}

.banner-content-wrapper.style5 .features-box-image {
  line-height: 1;
  font-size: 60px;
  padding: 30px 15px;
  border-radius: 8px;
  background: #F5F7FE;
  color: #1A064F;
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .banner-content-wrapper.style5 .features-box-image {
    font-size: 100px;
    padding: 60px 30px;
  }
}

.banner-content-wrapper.style5:focus .features-box-image,
.banner-content-wrapper.style5:focus .banner-title, .banner-content-wrapper.style5:hover .features-box-image,
.banner-content-wrapper.style5:hover .banner-title {
  color: var(--educrat-theme-color);
}

.tabs-course {
  border: 0;
  margin: 0 0 1.875rem;
}

@media (min-width: 1200px) {
  .tabs-course {
    margin-bottom: 60px;
  }
}

.tabs-course > li {
  margin: 0 2px 0 0;
}

.tabs-course > li:last-child {
  margin-right: 0 !important;
}

.tabs-course > li > a {
  padding: 7px 18px;
  border-radius: 8px;
  display: inline-block;
  font-size: 0.9375rem;
  color: var(--educrat-text-color);
  font-weight: 500;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.tabs-course > li > a.active, .tabs-course > li > a:focus, .tabs-course > li > a:hover {
  background: var(--educrat-theme-color-007);
  color: var(--educrat-theme-color);
}

.tabs-course.st_gray {
  background: #EEF2F6;
  border-radius: 50px;
  padding: 5px;
}

.tabs-course.st_gray > li > a {
  padding: 4px 25px;
}

.widget-courses-tabs .title {
  font-size: 25px;
  margin: 0 0 5px;
}

@media (min-width: 1200px) {
  .widget-courses-tabs .title {
    font-size: 30px;
  }
}

.widget-courses-tabs .top-info {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .widget-courses-tabs .top-info {
    margin-bottom: 60px;
  }
}

.widget-courses-tabs .top-info .ms-auto {
  margin-bottom: 5px;
}

.review-stars-rated {
  position: relative;
  overflow: hidden;
  width: 82px;
}

.review-stars-rated .review-stars {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #dadde6;
  white-space: nowrap;
  overflow: hidden;
  font-size: 10px;
  letter-spacing: 2px;
}

.review-stars-rated .review-stars li {
  display: inline-block;
}

.review-stars-rated .review-stars.filled {
  color: #ff9800;
  position: absolute;
  top: 0;
  left: 0;
}

.review-stars-rated-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
}

.review-stars-rated-wrapper .nb-review {
  margin-left: 7px;
}

.review-stars-rated-wrapper i,
.review-stars-rated-wrapper .nb-pre-review {
  margin-right: 7px;
}

.wrapper_rating_avg .rating_avg {
  margin-right: 7px;
  font-weight: 500;
  font-size: 14px;
  color: #ff9800;
}

.review-stars-wrap {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  overflow: hidden;
  clear: both;
  cursor: pointer;
}

.review-stars-wrap .review-stars {
  color: #e1e1e1;
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 13px;
  letter-spacing: 6px;
}

.review-stars-wrap .review-stars li {
  float: left;
}

.review-stars-wrap .review-stars li.active {
  color: #e1e1e1;
}

.review-stars-wrap .review-stars.filled {
  color: #ff9800;
  position: absolute;
  top: 0;
  left: 0;
}

.box-info-white {
  position: relative;
  z-index: 1;
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .box-info-white {
    margin-bottom: 60px;
  }
}

.box-info-white .title {
  font-weight: 500;
  font-size: 20px;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .box-info-white .title {
    margin-bottom: 20px;
  }
}

.box-info-white .comment-form {
  margin: 0;
}

.detail-average-rating {
  padding: 0.9375rem;
  text-align: center;
  background: #F5F7FE;
  border-radius: 8px;
}

@media (min-width: 768px) {
  .detail-average-rating {
    width: 30%;
    padding: 1.875rem;
    margin-right: 10px;
  }
}

.detail-average-rating .average-value {
  font-size: 35px;
  font-weight: 500;
  line-height: 1.2;
  display: block;
  color: var(--educrat-link-color);
}

@media (min-width: 768px) {
  .detail-average-rating .average-value {
    font-size: 60px;
  }
}

.detail-average-rating .total-rating {
  margin-top: 2px;
}

.detail-average-rating .tutor-ratings-stars {
  font-size: 11px;
}

.detail-rating {
  padding: 0.9375rem;
  background: #F5F7FE;
  border-radius: 8px;
}

@media (min-width: 768px) {
  .detail-rating {
    width: calc(100% - 30%);
    padding: 1.875rem;
  }
}

.list-rating .progress {
  height: 5px;
  width: calc(100% - 140px);
  background: #CCE0F8;
}

.list-rating .progress .progress-bar {
  background: var(--educrat-theme-color);
}

.list-rating .value-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.list-rating .value {
  color: var(--educrat-link-color);
  display: inline-block;
  font-size: 0.9375rem;
  width: 140px;
  padding-left: 15px;
}

.item-rating {
  margin-bottom: 5px;
}

@media (min-width: 1200px) {
  .item-rating {
    margin-bottom: 10px;
  }
}

.item-rating:last-child {
  margin-bottom: 0;
}

.lp-course-author .course-author__pull-left {
  width: 30px;
  height: 30px;
  border-radius: 30px;
  overflow: hidden;
}

.lp-course-author .author-title {
  font-weight: 400;
  font-size: 14px;
  padding-left: 10px;
}

.lp-course-author a {
  color: var(--educrat-text-color);
}

.lp-course-author a:hover, .lp-course-author a:focus {
  color: var(--educrat-link-color);
}

.tutor-tag-list {
  margin: 0;
}

.tutor-course-detail-author .author-image,
.lp-course-detail-author .author-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
}

.tutor-course-detail-author .tutor-avatar-lg,
.lp-course-detail-author .tutor-avatar-lg {
  width: 100%;
  height: 100%;
  @inlcude box-shadow(none);
}

.tutor-course-detail-author .course-author-infomation,
.lp-course-detail-author .course-author-infomation {
  padding-left: 20px;
}

.course-detail-author .author-description {
  margin-top: 10px;
}

@media (min-width: 1200px) {
  .course-detail-author .author-description {
    margin-top: 20px;
  }
}

.author-social a {
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  background: var(--educrat-theme-color-010);
  align-items: center;
  justify-content: center;
  border: 1px solid var(--educrat-theme-color-010);
  border-radius: 8px;
  font-size: 12px;
  color: var(--educrat-theme-color);
}

.author-social a:hover, .author-social a:focus {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.author-social a + a {
  margin-left: 10px;
}

@media (min-width: 1200px) {
  .author-social a + a {
    margin-left: 15px;
  }
}

.author-top-content {
  margin-top: 2px;
}

.author-top-content i {
  margin-right: 5px;
}

.author-top-content > * {
  display: inline-block;
  vertical-align: middle;
}

.author-top-content > * + * {
  margin-left: 0.625rem;
}

@media (min-width: 1200px) {
  .author-top-content > * + * {
    margin-left: 1rem;
  }
}

.course-author-title {
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 3px;
}

.comments-course .comment-list .the-comment {
  margin: 0 0 20px;
  padding: 0 0 20px;
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .comments-course .comment-list .the-comment {
    margin: 0 0 35px;
    padding: 0 0 35px;
  }
}

.comments-course .comment-list > .comment:last-child > .the-comment {
  margin: 0;
  padding: 0;
  border: 0;
}

#learn-press-checkout {
  margin-top: 1.875rem;
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  #learn-press-checkout {
    margin-top: 50px;
    margin-bottom: 50px;
  }
}

.lp-content-wrap > h2 {
  font-size: 24px;
  font-weight: 500;
}

.lp-checkout-form__before .lp-checkout-block h4, .lp-checkout-form__after .lp-checkout-block h4 {
  font-size: 22px;
  font-weight: 500;
}

#checkout-order .course-name,
#checkout-order .col-number {
  font-weight: 500;
}

#checkout-order .order-total .col-number {
  color: var(--educrat-link-color);
}

#checkout-payment #checkout-order-action button.loading::before {
  margin-right: 3px;
}

.lp-list-table thead tr th {
  font-weight: 500;
  font-size: 1rem;
  color: var(--educrat-link-color);
}

.lp-user-profile .profile-orders .column-order-actions a {
  color: var(--educrat-theme-color) !important;
}

.learn-press-form .form-field input,
.learn-press-form .form-field textarea,
form[name="profile-change-password"] .form-field input,
form[name="profile-change-password"] .form-field textarea {
  outline: none !important;
  border-radius: 8px;
  border: 1px solid #EDEDED !important;
  padding: 8px 15px !important;
}

.learn-press-form .form-field input:focus,
.learn-press-form .form-field textarea:focus,
form[name="profile-change-password"] .form-field input:focus,
form[name="profile-change-password"] .form-field textarea:focus {
  border-color: var(--educrat-theme-color) !important;
}

.learnpress_avatar__button {
  margin: 10px 10px 0 0;
}

button.learnpress_avatar__button,
#learn-press-profile-basic-information button[type="submit"],
form[name="profile-change-password"] button[type="submit"],
#popup-course #popup-content .lp-button,
#learn-press-profile #profile-content .lp-button,
#checkout-payment #checkout-order-action .lp-button,
.lp-course-buttons .lp-button,
.learnpress-page .lp-button,
.learnpress-page #lp-button {
  border: 1px solid transparent;
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
  padding: 0.65rem 1.875rem;
  height: auto;
  font-size: 0.9375rem;
  font-family: var(--bs-font-sans-serif);
  font-weight: 500;
  line-height: 1.8;
  border-radius: 8px;
  opacity: 1;
  filter: alpha(opacity=100);
}

button.learnpress_avatar__button:hover, button.learnpress_avatar__button:focus,
#learn-press-profile-basic-information button[type="submit"]:hover,
#learn-press-profile-basic-information button[type="submit"]:focus,
form[name="profile-change-password"] button[type="submit"]:hover,
form[name="profile-change-password"] button[type="submit"]:focus,
#popup-course #popup-content .lp-button:hover,
#popup-course #popup-content .lp-button:focus,
#learn-press-profile #profile-content .lp-button:hover,
#learn-press-profile #profile-content .lp-button:focus,
#checkout-payment #checkout-order-action .lp-button:hover,
#checkout-payment #checkout-order-action .lp-button:focus,
.lp-course-buttons .lp-button:hover,
.lp-course-buttons .lp-button:focus,
.learnpress-page .lp-button:hover,
.learnpress-page .lp-button:focus,
.learnpress-page #lp-button:hover,
.learnpress-page #lp-button:focus {
  opacity: 1;
  filter: alpha(opacity=100);
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

#popup-course #popup-header .lp-button {
  padding: 0 30px;
  background: var(--educrat-theme-color);
  color: #fff;
  white-space: nowrap;
}

#popup-course #popup-header .lp-button:hover, #popup-course #popup-header .lp-button:focus {
  color: #fff;
  background: var(--educrat-theme-hover-color);
}

.profile-basic-information .form-field > label, form[name="profile-change-password"] .form-field > label {
  font-size: 0.9375rem;
  font-weight: 400;
  font-style: normal;
}

.course-header {
  background: #fff;
  padding-bottom: 1.875rem;
}

.course-header .course-category {
  margin-bottom: 10px;
}

@media (min-width: 1200px) {
  .course-header .course-category {
    margin-bottom: 15px;
  }
}

.course-header .course-excerpt {
  margin-top: 0.75rem;
}

.course-header .course-excerpt p:last-child {
  margin-bottom: 0;
}

.course-header .course-header-bottom {
  margin-top: 10px;
}

@media (min-width: 1200px) {
  .course-header .course-header-bottom {
    margin-top: 18px;
  }
}

.course-header .title {
  text-transform: capitalize;
  font-size: 22px;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .course-header .title {
    font-size: 30px;
    line-height: 45px;
  }
}

@media (min-width: 1200px) {
  .course-header .title {
    margin-bottom: 15px;
  }
}

.course-header .course-header-meta {
  margin-top: 10px;
}

@media (min-width: 1200px) {
  .course-header .course-header-meta {
    margin-top: 15px;
  }
}

.course-header .course-header-meta > div {
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
}

.course-header .course-header-meta > div + div {
  margin-left: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-header .course-header-meta > div + div {
    margin-left: 1.875rem;
  }
}

.course-header .course-header-meta i {
  line-height: 1;
  vertical-align: middle;
  font-size: 1rem;
  margin-right: 5px;
}

.course-header.default {
  background: #F5F7FE;
  padding: 0;
  margin: 0 0 1.875rem;
}

.course-header .inner-default {
  padding: 15px 0 1.875rem;
}

@media (min-width: 1200px) {
  .course-header .inner-default {
    padding: 65px 0rem 90px;
  }
}

.course-category-item {
  padding: 10px 16px 8px;
  line-height: 1;
  font-size: 11px;
  font-weight: 500;
  border-radius: 30px;
  display: inline-block;
  text-transform: uppercase;
  color: #fff;
  background: var(--educrat-theme-color);
}

.course-category-item:hover, .course-category-item:focus {
  color: #fff;
  background: var(--educrat-theme-hover-color);
}

.course-category-item + .course-category-item {
  margin-left: 5px;
}

@media (min-width: 1200px) {
  .course-category-item + .course-category-item {
    margin-left: 12px;
  }
}

.course-header.v2 {
  position: relative;
  z-index: 1;
  color: #6A7A99;
}

.course-header.v2 .breadcrumb > li + li::before {
  color: #6A7A99;
}

.course-header.v2 a:not([class]) {
  color: #6A7A99;
}

.course-header.v2 a:not([class]):hover, .course-header.v2 a:not([class]):focus {
  color: #fff;
}

.course-header.v2:before {
  z-index: -1;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: var(--educrat-link-color);
}

.course-header.v2 .active,
.course-header.v2 .title {
  color: #fff;
}

.header-inner-v3 {
  background: var(--educrat-theme-color);
  color: #fff;
  max-width: 1500px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 1530px) {
  .header-inner-v3 {
    border-radius: 8px;
  }
}

.header-inner-v3 a:not([class]) {
  color: #fff !important;
}

.header-inner-v3 a:not([class]):hover, .header-inner-v3 a:not([class]):focus {
  text-decoration: underline;
}

.header-inner-v3 .tutor-color-secondary,
.header-inner-v3 .title {
  color: #fff;
}

.header-inner-v3 .inner-default {
  padding: 1.875rem 0;
}

@media (min-width: 1200px) {
  .header-inner-v3 .inner-default {
    padding: 90px 0;
  }
}

.header-inner-v3 .course-category-item {
  background: #00FF84;
  color: var(--educrat-link-color);
}

.header-inner-v3 .course-category-item:hover, .header-inner-v3 .course-category-item:focus {
  color: #fff;
  background: #4417FF;
}

@media (min-width: 1530px) {
  .course-header.v3 .apus-breadscrumb {
    margin-bottom: 90px;
  }
}

.course-header.v4 {
  background: #fff;
}

.course-header.v4 .inner-default {
  padding: 0;
}

@media (min-width: 1200px) {
  .course-header.v4 .inner-default {
    padding: 25px 0rem 50px;
  }
}

.course-header.v5 {
  background: #F7F8FB;
}

.course-header.v5 .apus-breadscrumb {
  background: #fff;
}

.course-header.v5 + #main-container {
  background: #F7F8FB;
}

.course-header.v5 .inner-default {
  padding: 0;
}

@media (min-width: 1200px) {
  .course-header.v5 .inner-default {
    padding: 25px 0rem 50px;
  }
}

.course-header.v6 {
  background: var(--educrat-link-color);
  color: #6A7A99;
}

.course-header.v6 a:not([class]) {
  color: #6A7A99;
}

.course-header.v6 a:not([class]):hover, .course-header.v6 a:not([class]):focus {
  color: #fff;
}

.course-header.v6 .breadcrumb > li + li::before {
  color: #6A7A99;
}

.course-header.v6 .active,
.course-header.v6 .title {
  color: #fff;
}

.course-header.v6 .apus-social-share {
  margin-top: 20px;
  text-align: left !important;
}

@media (min-width: 1500px) {
  .course-header.v6 .apus-social-share {
    margin-left: -15px;
  }
}

.course-header.v6 .apus-social-share a {
  color: #6A7A99;
  background: transparent;
}

.course-header.v6 .apus-social-share a:hover, .course-header.v6 .apus-social-share a:hover {
  color: #fff;
  background: #282664;
}

.course-header.v6 .inner-v6 {
  padding: 10px 0 0;
}

@media (min-width: 1200px) {
  .course-header.v6 .inner-v6 {
    padding: 70px 0 30px;
  }
}

@media (min-width: 1350px) {
  .course-header.v6 .course-header-right {
    padding-left: 110px;
  }
}

.course-header.v6 .course-header-right .tutor-video-player,
.course-header.v6 .course-header-right .course-video {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-header.v6 .course-header-right .tutor-video-player,
  .course-header.v6 .course-header-right .course-video {
    margin-bottom: 1.875rem;
  }
}

.course-header.v6 .course-header-right .course-price {
  color: #fff;
  line-height: 1;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-header.v6 .course-header-right .course-price {
    margin-bottom: 1.875rem;
    font-size: 24px;
  }
}

.course-header.v6 .course-header-right .sale-price del,
.course-header.v6 .course-header-right .sale-price .origin-price {
  display: none;
}

.course-header.v6 .course-header-right .sale-price + del,
.course-header.v6 .course-header-right .sale-price + .origin-price {
  font-size: 0.9375rem;
  margin-right: 10px;
  color: #6A7A99;
  text-decoration: line-through;
}

.course-header.v6 .course-header-right .lp-button {
  width: 100%;
  text-align: center;
  border-color: var(--educrat-theme-color);
  background: var(--educrat-theme-color);
  color: #fff;
}

@media (min-width: 1200px) {
  .course-header.v6 .course-header-right .lp-button {
    font-size: 1rem;
    padding: 14px 30px;
  }
}

.course-header.v6 .course-header-right .lp-button:hover, .course-header.v6 .course-header-right .lp-button:focus {
  border-color: var(--educrat-theme-hover-color);
  background: var(--educrat-theme-hover-color);
  color: #fff;
}

.course-header.v6 .course-header-right .lp-button + * {
  margin-top: 10px;
}

.course-header.v6 .course-header-right .lp-course-buttons form {
  width: 100%;
}

.course-header.v6 .course-info-widget {
  background: transparent;
  border-radius: 0;
  border: 0;
  padding: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.course-header.v6 .course-info-widget .bottom-inner {
  padding: 0;
}

.course-cover-thumb {
  position: relative;
  overflow: hidden;
}

.course-cover-thumb .video {
  bottom: 12px;
  left: 10px;
  position: absolute;
  z-index: 1;
  line-height: 40px;
  padding: 0 12px;
  background: rgba(2, 2, 2, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-weight: 500;
  font-size: 1rem;
  font-family: var(--educrat-heading-font);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.course-cover-thumb .video i {
  width: 25px;
  height: 25px;
  font-size: 10px;
  margin-right: 7px;
  border-radius: 50%;
  display: inline-block;
  line-height: 25px;
  text-align: center;
  background: #ffffff;
  color: #7b7d98;
}

.course-cover-thumb .video:hover, .course-cover-thumb .video:focus {
  background: rgba(2, 2, 2, 0.5);
}

.course-detail-info-bottom {
  font-size: 14px;
  padding: 1rem;
  background: #fff;
  border-radius: 10px;
  -webkit-box-shadow: rgba(62, 28, 131, 0.1) 0px 0px 20px 0px;
  box-shadow: rgba(62, 28, 131, 0.1) 0px 0px 20px 0px;
}

@media (min-width: 1200px) {
  .course-detail-info-bottom {
    padding: 1.5rem 20px;
  }
}

.course-detail-info-bottom i {
  width: 50px;
  height: 50px;
  font-size: 20px;
  border-radius: 50%;
  background: rgba(255, 87, 34, 0.11);
  border: 1px dashed;
  color: #ff5722;
}

.course-detail-info-bottom i.fa-play {
  background: rgba(144, 106, 212, 0.12);
  color: #906ad4;
}

.course-detail-info-bottom i.fa-user-shield {
  background: rgba(124, 191, 47, 0.12);
  color: #7cbf2f;
}

.course-detail-info-bottom .inner-right {
  flex: 1;
  padding-left: 1rem;
}

.course-detail-info-bottom .info {
  font-size: 18px;
  margin: 0 0 2px;
}

.single-content-course {
  padding-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .single-content-course {
    padding-bottom: 50px;
  }
}

@media (min-width: 1200px) {
  .single-content-course.v4 .detail-course .sidebar {
    margin-top: -350px;
  }
}

@media (min-width: 1200px) {
  .single-content-course.v5 {
    padding-bottom: 80px;
  }
  .single-content-course.v5 .detail-course .sidebar {
    margin-top: -350px;
  }
}

.single-content-course.v5 .apus-lp-content-area {
  border: 1px solid #EDEDED;
  background: #fff;
  -webkit-box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  border-radius: 8px;
}

@media (max-width: 991px) {
  .single-content-course.v5 .apus-lp-content-area {
    margin-bottom: 1.875rem;
  }
}

.single-content-course.v5 .apus-lp-content-area .course-single-tab {
  padding: 15px 15px 0;
  border-width: 0 0 1px;
}

@media (min-width: 1200px) {
  .single-content-course.v5 .apus-lp-content-area .course-single-tab {
    padding: 14px 30px 0;
  }
}

.single-content-course.v5 .apus-lp-content-area .course-tab-panels {
  padding: 0 15px 15px;
}

@media (min-width: 1200px) {
  .single-content-course.v5 .apus-lp-content-area .course-tab-panels {
    padding: 0 30px 30px;
  }
}

.single-content-course.v5 .apus-lp-content-area .course-tab-panels .box-info-white:last-child {
  margin-bottom: 0;
}

.single-content-course.v6 .apus-lp-content-area {
  padding-top: 1.875rem;
}

@media (min-width: 1200px) {
  .single-content-course.v6 .apus-lp-content-area {
    padding-top: 100px;
  }
}

@media (min-width: 1200px) {
  .single-content-course.v6 .course-single-tab {
    border-width: 0 0 0 1px !important;
    border-style: solid;
    border-color: #EDEDED;
  }
  .single-content-course.v6 .course-single-tab > li {
    width: 100%;
    margin: 0;
  }
  .single-content-course.v6 .course-single-tab > li > a {
    font-size: 18px;
    padding: 10px 20px;
    color: var(--educrat-link-color);
  }
  .single-content-course.v6 .course-single-tab > li > a:before {
    top: 0;
    bottom: inherit;
    left: -1px;
    width: 2px;
    height: 0;
  }
  .single-content-course.v6 .course-single-tab > li > a.is-active, .single-content-course.v6 .course-single-tab > li > a.active {
    color: var(--educrat-theme-color);
  }
  .single-content-course.v6 .course-single-tab > li > a.is-active:before, .single-content-course.v6 .course-single-tab > li > a.active:before {
    width: 2px;
    height: 100%;
  }
  .single-content-course.v6 .course-single-tab > li:hover > a.is-active,
  .single-content-course.v6 .course-single-tab > li:hover > a.active,
  .single-content-course.v6 .course-single-tab > li:hover > a {
    color: var(--educrat-theme-color);
  }
  .single-content-course.v6 .course-single-tab > li:hover > a.is-active:before,
  .single-content-course.v6 .course-single-tab > li:hover > a.active:before,
  .single-content-course.v6 .course-single-tab > li:hover > a:before {
    width: 2px;
    height: 100%;
  }
  .single-content-course.v6 .apus-lp-content-area {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: start;
  }
  .single-content-course.v6 .tutor-is-sticky,
  .single-content-course.v6 .course-single-tab {
    width: 30%;
    padding-right: 1.875rem;
  }
  .single-content-course.v6 .tutor-is-sticky .course-single-tab {
    padding: 0;
    width: 100%;
  }
  .single-content-course.v6 .tutor-tab,
  .single-content-course.v6 .course-tabs-scrollspy {
    width: 70%;
  }
}

.tutor-course-details-page .tutor-course-details-tab .tutor-is-sticky {
  z-index: 2;
}

.course-tab-panel .course-description p {
  font-size: 0.9375rem;
  color: var(--educrat-text-color);
}

.tutor-course-details-widget,
.apus-course-extra-box {
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .tutor-course-details-widget,
  .apus-course-extra-box {
    margin-bottom: 40px;
  }
}

.tutor-course-details-widget:last-child,
.apus-course-extra-box:last-child {
  margin: 0;
}

.tutor-course-details-widget .tutor-course-details-widget-title,
.tutor-course-details-widget .title,
.apus-course-extra-box .tutor-course-details-widget-title,
.apus-course-extra-box .title {
  font-weight: 500;
  font-size: 20px;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .tutor-course-details-widget .tutor-course-details-widget-title,
  .tutor-course-details-widget .title,
  .apus-course-extra-box .tutor-course-details-widget-title,
  .apus-course-extra-box .title {
    margin-bottom: 20px;
  }
}

.tutor-course-details-widget ul,
.apus-course-extra-box ul {
  margin: 0;
  padding: 0;
  list-style: none;
  color: var(--educrat-text-color);
}

.tutor-course-details-widget ul li,
.apus-course-extra-box ul li {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 0.625rem;
}

.tutor-course-details-widget ul li:before,
.apus-course-extra-box ul li:before {
  content: "\f15d";
  font-family: Flaticon;
  color: #6A7A99;
  border: 2px solid #EDEDED;
  font-size: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

@media (min-width: 1200px) {
  .tutor-course-details-widget ul li,
  .apus-course-extra-box ul li {
    margin-bottom: 20px;
  }
}

.tutor-course-details-widget ul li:last-child,
.apus-course-extra-box ul li:last-child {
  margin-bottom: 0;
}

.tutor-course-details-widget .tutor-icon-bullet-point::before,
.apus-course-extra-box .tutor-icon-bullet-point::before {
  display: none;
}

.tutor-course-details-widget .tutor-fs-6,
.apus-course-extra-box .tutor-fs-6 {
  font-size: inherit;
}

.tutor-fs-1, .tutor-fs-2, .tutor-fs-3, .tutor-fs-4, .tutor-fs-5, .tutor-fs-6, .tutor-fs-7, .tutor-fs-8, .tutor-fs-9 {
  line-height: inherit;
}

.accordion-header {
  margin: 0;
}

.accordion-button {
  padding: .75rem 1.25rem;
  font-size: 1rem;
  font-weight: 500;
  color: var(--educrat-link-color);
  border: 0;
  background: #F7F8FB !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .accordion-button {
    padding: 1.2rem 1.875rem;
  }
}

.accordion-button:before {
  content: "\e64b";
  font-family: 'themify';
  font-weight: 700;
  position: absolute;
  top: 50%;
  display: block;
  right: 1rem;
  font-size: 13px;
  display: block;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .accordion-button:before {
    right: 1.875rem;
  }
}

.accordion-button:after {
  display: none;
}

.accordion-button:not(.collapsed) {
  color: var(--educrat-theme-color);
}

.accordion-button:not(.collapsed):before {
  content: "\e648";
}

.accordion-item {
  border: 1px solid #EDEDED !important;
  border-radius: 8px;
}

.accordion-item + .accordion-item {
  margin-top: 10px;
}

.accordion-item .accordion-body {
  border-top: 1px solid #EDEDED;
}

@media (min-width: 1200px) {
  .accordion-item .accordion-body {
    padding: 25px 1.875rem;
  }
}

.course-video {
  position: relative;
  overflow: hidden;
}

.course-video:before {
  padding-top: 56.25%;
  content: "";
  display: block;
}

.course-video iframe {
  max-width: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.course-summary .tutor-video-player,
.course-summary .course-video {
  border-radius: 12px;
}

.course-summary .tutor-video-player {
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .course-summary .tutor-video-player {
    margin-bottom: 60px;
  }
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section {
  cursor: pointer;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  background: #F7F8FB !important;
  overflow: hidden;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section + .section {
  margin-top: 10px;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section .section-desc {
  color: var(--educrat-text-color);
  font-style: normal;
  margin: 10px 0 0;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section:not(.closed) .section-toggle:before {
  content: "\e648" !important;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-header {
  height: auto !important;
  border: 0;
  padding: .75rem 1.25rem !important;
}

@media (min-width: 1200px) {
  #learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-header {
    padding: 21px 1.875rem !important;
  }
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-header .section-left .section-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--educrat-heading-color);
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-header .section-left .section-toggle {
  width: 14px;
  height: 14px;
  flex: 0 0 14px !important;
  position: relative;
  line-height: 1;
  color: var(--educrat-link-color);
  font-size: 13px;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-header .section-left .section-toggle i {
  display: none !important;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-header .section-left .section-toggle:before {
  content: "\e64b";
  font-family: 'themify';
  font-weight: 700;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content {
  margin: 0;
  background: #fff;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .inner {
  padding: 1rem 1.25rem;
}

@media (min-width: 1200px) {
  #learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .inner {
    padding: 25px 1.875rem;
  }
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item {
  margin: 0 0 20px;
  padding: 0;
  background: transparent;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item:last-child {
  margin-bottom: 0;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item.item-locked .course-item-status:before {
  color: #f33066;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link {
  width: 100%;
  padding: 0;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link:hover .item-name, #learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link:focus .item-name {
  color: var(--educrat-theme-color);
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link::before,
#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta,
#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .item-name {
  padding: 0;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link::before {
  font-size: 11px;
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
  width: 25px;
  height: 25px;
  min-width: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 50%;
  padding: 0 !important;
}

#learn-press-course-curriculum.course-curriculum ul.curriculum-sections .section-content .course-item .item-name {
  font-size: 0.9375rem;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 0 12px;
}

#popup-course #popup-sidebar .course-item {
  min-height: 0;
  padding: 12px 20px;
  margin-bottom: 10px;
  border-radius: 3px;
}

#popup-course #popup-sidebar .section {
  padding: 0;
  border-width: 0 0 1px;
  border-color: #EDEDED;
}

.quiz-intro-item::before {
  color: var(--educrat-theme-color);
}

.lp-archive-courses thead th, .lp-archive-courses tr th {
  font-weight: 500;
}

.quiz-status > div {
  background: #F7F8FB;
}

.course-info-widget {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  background: #fff;
  -webkit-box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  padding: 10px;
}

.course-info-widget .tutor-video-player,
.course-info-widget .course-video {
  border-radius: 8px;
  overflow: hidden;
  margin: 0 0 10px;
}

.course-info-widget .course-price {
  color: var(--educrat-link-color);
  line-height: 1;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-info-widget .course-price {
    margin-bottom: 1.875rem;
    font-size: 24px;
  }
}

.course-info-widget .sale-price del,
.course-info-widget .sale-price .origin-price {
  display: none;
}

.course-info-widget .sale-price + del,
.course-info-widget .sale-price + .origin-price {
  font-size: 0.9375rem;
  margin-right: 10px;
  color: var(--educrat-text-color);
  text-decoration: line-through;
}

.course-info-widget .tutor-btn,
.course-info-widget .lp-button {
  width: 100%;
  text-align: center;
  border-color: var(--educrat-theme-color);
  background: var(--educrat-theme-color);
  color: #fff;
  line-height: 1.8;
  font-weight: 500;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .course-info-widget .tutor-btn,
  .course-info-widget .lp-button {
    font-size: 1rem;
    padding: 14px 30px;
  }
}

.course-info-widget .tutor-btn:hover, .course-info-widget .tutor-btn:focus,
.course-info-widget .lp-button:hover,
.course-info-widget .lp-button:focus {
  border-color: var(--educrat-theme-hover-color);
  background: var(--educrat-theme-hover-color);
  color: #fff;
}

.course-info-widget .tutor-btn + *,
.course-info-widget .lp-button + * {
  margin-top: 10px;
}

.course-info-widget .save-bookmark-btn {
  margin-top: 18px;
}

@media (min-width: 1200px) {
  .course-info-widget .save-bookmark-btn {
    padding: 13px 50px;
  }
}

.course-info-widget .lp-course-buttons form {
  width: 100%;
}

.course-info-widget .purchase-course {
  width: 100%;
}

.course-info-widget .title-info {
  font-size: 18px;
  margin: 0 0 1rem;
}

.course-info-widget .bottom-inner {
  padding: 10px;
}

@media (min-width: 1200px) {
  .course-info-widget .bottom-inner {
    padding: 20px;
  }
}

.course-info-widget .apus-social-share {
  margin-top: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-info-widget .apus-social-share {
    margin-top: 1.875rem;
  }
}

.tutor-course-info-fields,
.lp-course-info-fields {
  padding: 0;
  margin: 5px 0 0;
  color: var(--educrat-link-color);
}

@media (min-width: 1200px) {
  .tutor-course-info-fields,
  .lp-course-info-fields {
    margin-top: 20px;
  }
}

.tutor-course-info-fields li,
.lp-course-info-fields li {
  padding: 10px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  width: 100%;
  border-bottom: 1px solid #EDEDED;
}

.tutor-course-info-fields i,
.lp-course-info-fields i {
  margin-right: 10px;
}

.tutor-course-info-fields .tutor-label,
.tutor-course-info-fields .lp-label,
.lp-course-info-fields .tutor-label,
.lp-course-info-fields .lp-label {
  color: var(--educrat-text-color);
  margin-left: auto;
}

.tutor-course-info-fields.style2,
.lp-course-info-fields.style2 {
  margin: 0;
}

.tutor-course-info-fields.style2 li:before,
.lp-course-info-fields.style2 li:before {
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  display: inline-block;
  margin-right: 12px;
  font-size: 12px;
}

.tutor-course-info-fields.st_white, .tutor-course-info-fields.st_white .lp-label, .tutor-course-info-fields.st_white .tutor-label,
.lp-course-info-fields.st_white,
.lp-course-info-fields.st_white .lp-label,
.lp-course-info-fields.st_white .tutor-label {
  color: #fff;
}

.tutor-course-info-fields.st_white li,
.lp-course-info-fields.st_white li {
  border-color: rgba(255, 255, 255, 0.15);
}

@media (min-width: 1200px) {
  .detail-course > .row > .col-lg-8 {
    width: 70.5%;
    padding-right: 50px;
  }
  .detail-course > .row > .col-lg-4 {
    width: 29.5%;
  }
  .detail-course .sidebar {
    margin-top: -360px;
  }
}

#learn-press-course-tabs.course-tabs {
  margin: 0;
}

#learn-press-course-tabs ul.learn-press-nav-tabs {
  border: 0;
  background: transparent;
}

#learn-press-course-tabs .course-nav {
  border: 0;
  margin-right: 0.8rem;
  flex-grow: 0;
}

#learn-press-course-tabs .course-nav:last-child {
  margin-right: 0;
}

#learn-press-course-tabs .course-nav:before {
  display: none !important;
}

#learn-press-course-tabs .course-nav label {
  display: inline-block;
  padding: .7rem 1.6rem;
  background: #ffffff;
  font-weight: 600;
  font-size: 14px;
  border-radius: 0.25rem;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

#learn-press-course-tabs .course-nav.active label {
  color: #fff !important;
  background: var(--educrat-theme-color) !important;
}

.learn-press-tabs {
  background: transparent;
}

.learn-press-tabs .learn-press-tabs__nav {
  border: 0;
}

.learn-press-tabs .learn-press-tabs__nav li {
  border: 0;
  margin-right: 0.8rem;
  flex-grow: 0;
  background: transparent !important;
}

.learn-press-tabs .learn-press-tabs__nav li:last-child {
  margin-right: 0;
}

.learn-press-tabs .learn-press-tabs__nav li:after, .learn-press-tabs .learn-press-tabs__nav li:before {
  display: none !important;
}

.learn-press-tabs .learn-press-tabs__nav li label {
  overflow: hidden;
  display: inline-block;
  background: #ffffff;
  font-weight: 500;
  font-size: 14px;
  border-radius: 0.25rem;
  border: 1px solid #EDEDED;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.learn-press-tabs .learn-press-tabs__nav li label a {
  color: inherit !important;
}

.learn-press-tabs .learn-press-tabs__nav li:hover label, .learn-press-tabs .learn-press-tabs__nav li.active label {
  color: #fff !important;
  background: var(--educrat-theme-color) !important;
  border-color: var(--educrat-theme-color) !important;
}

.course-top-wrapper {
  margin: 0 0 1.875rem;
}

.course-top-wrapper .course-found {
  text-transform: capitalize;
  font-weight: 500;
  color: var(--educrat-heading-color);
  font-size: 14px;
}

.course-top-wrapper label {
  color: var(--educrat-heading-color);
  font-size: 14px;
  margin-right: 20px;
  font-weight: 500;
}

.course-top-wrapper select.orderby {
  cursor: pointer;
  color: var(--educrat-text-color);
  font-size: 14px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url("../images/select.png") #EEF2F6 right 10px center no-repeat;
  padding: 10px 12px;
  margin: 0;
  min-width: 120px;
  border: 1px solid #EEF2F6;
  border-radius: 8px;
}

.course-top-wrapper .display-mode {
  margin-left: 7px;
}

.course-top-wrapper .display-mode .change-view {
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--educrat-text-color);
  border: 1px solid #EDEDED;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.course-top-wrapper .display-mode .change-view.active {
  color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.course-top-wrapper .display-mode .change-view + .change-view {
  margin-left: 7px;
}

.course-top-wrapper .filter-top-btn,
.course-top-wrapper .filter-offcanvas-btn {
  margin-left: 20px;
  padding: 9px 20px;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  color: var(--educrat-theme-color);
  background: #e5f0fd;
  display: inline-block;
}

.course-top-wrapper .filter-top-btn i,
.course-top-wrapper .filter-offcanvas-btn i {
  margin-right: 10px;
}

.course-top-wrapper .filter-top-btn:hover, .course-top-wrapper .filter-top-btn:focus,
.course-top-wrapper .filter-offcanvas-btn:hover,
.course-top-wrapper .filter-offcanvas-btn:focus {
  color: #fff;
  background: var(--educrat-theme-color);
}

.filter-offcanvas-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 8;
  background: #fff;
  padding: 20px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
  opacity: 0;
  filter: alpha(opacity=0);
  width: 320px;
  height: 100%;
  max-width: 90%;
  scrollbar-width: thin;
  overflow-y: auto;
}

@media (min-width: 1200px) {
  .filter-offcanvas-sidebar {
    padding: 1.875rem;
  }
}

.filter-offcanvas-sidebar.active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
  opacity: 1;
  filter: alpha(opacity=100);
}

.filter-offcanvas-sidebar-overlay {
  background: rgba(24, 24, 26, 0.7);
  z-index: 7;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  cursor: pointer;
}

.filter-offcanvas-sidebar-overlay.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.course-layout-item {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  background: #fff;
  overflow: hidden;
  border-radius: 8px;
  margin: 0 0 1.875rem;
}

.course-layout-item .course-cover {
  overflow: hidden;
  border-radius: 8px;
}

.course-layout-item .course-cover img {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.course-layout-item .course-cover figure {
  margin: 0;
}

.course-layout-item .course-cover .sale-label {
  line-height: 1;
  z-index: 2;
  display: inline-block;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  border-radius: 50px;
  padding: 9px 20px;
  color: #fff;
  background: #E8543E;
  position: absolute;
  top: 10px;
  left: 10px;
}

.course-layout-item .post-thumbnail {
  display: block;
  position: relative;
}

.course-layout-item .post-thumbnail:before {
  content: '';
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #140342;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.course-layout-item .course-layout-content {
  padding: 12px 0 0;
}

.course-layout-item .course-title {
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 7px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .course-layout-item .course-title {
    font-size: 17px;
  }
}

.course-layout-item .course-info-top {
  margin: 0 0 5px;
}

.course-layout-item .course-meta-bottom {
  padding: 10px 0 0;
  margin-top: 8px;
  border-top: 1px solid #EDEDED;
}

.course-layout-item .course-meta-middle {
  font-size: 14px;
}

.course-layout-item .course-meta-middle i {
  font-size: 1rem;
  margin-right: 5px;
  line-height: 1;
  vertical-align: middle;
  display: inline-block;
}

.course-layout-item .course-meta-middle > * {
  display: inline-block;
  text-transform: capitalize;
  vertical-align: top;
  margin-right: 8px;
}

@media (min-width: 1200px) {
  .course-layout-item .course-meta-middle > * {
    margin-right: 15px;
  }
}

.course-layout-item .course-meta-middle > *:last-child {
  margin-right: 0;
}

.course-layout-item .course-meta-middle .tutor-meta-value, .course-layout-item .course-meta-middle .tutor-meta a {
  font-weight: 400;
  color: var(--educrat-text-color);
}

.course-layout-item .course-price {
  font-size: 16px;
  color: var(--educrat-link-color);
  font-weight: 500;
}

@media (min-width: 1200px) {
  .course-layout-item .course-price {
    font-size: 18px;
  }
}

.course-layout-item .course-price del,
.course-layout-item .course-price .origin-price {
  margin-right: 5px;
  text-decoration: line-through;
  font-size: 0.9375rem;
  color: var(--educrat-text-color);
}

.course-layout-item .lp-button.button {
  line-height: 1;
  padding: 13px 25px;
}

.course-layout-item .lp-button.button:after {
  display: none;
}

.course-layout-item:hover .course-cover img {
  -webkit-transform: scale(1.15);
  -ms-transform: scale(1.15);
  -o-transform: scale(1.15);
  transform: scale(1.15);
}

.course-layout-item:hover .post-thumbnail:before {
  opacity: 0.5;
  filter: alpha(opacity=50);
}

.course-layout-item .apus-wishlist-remove {
  background: #E8543E;
  color: #fff;
  width: 25px;
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  text-align: center;
  display: inline-block;
  border-radius: 4px;
  opacity: 0.8;
  filter: alpha(opacity=80);
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1;
}

.course-layout-item .apus-wishlist-remove.loading {
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.course-layout-item .apus-wishlist-remove.loading i:before {
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.course-layout-item .apus-wishlist-remove:hover, .course-layout-item .apus-wishlist-remove:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.course-grid-v2 .course-cover {
  border-radius: 8px 8px 0 0;
}

.course-grid-v2 .course-layout-content {
  padding: 12px 18px 10px;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #EDEDED;
  border-radius: 0 0 8px 8px;
}

.course-grid-v2 .course-meta-middle i {
  margin-right: 2px;
}

.course-grid-v2 .course-meta-middle > * {
  margin-right: 6px;
}

.course-grid-v2 .course-meta-middle > *:last-child {
  margin-right: 0;
}

.course-grid-v3 {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  padding: 10px;
}

.course-grid-v3 .course-layout-content {
  padding: 12px 10px 0;
}

.course-grid-v3 .course-meta-middle i {
  margin-right: 2px;
}

.course-grid-v3 .course-meta-middle > * {
  margin-right: 8px;
}

.course-grid-v3 .course-meta-middle > *:last-child {
  margin-right: 0;
}

.course-grid-v4 {
  -webkit-box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  border-radius: 8px;
}

.course-grid-v4 .course-layout-content {
  padding: 12px 15px 10px;
}

@media (min-width: 1200px) {
  .course-grid-v4 .course-layout-content {
    padding: 12px 30px 10px;
  }
}

@media (min-width: 1200px) {
  .course-grid-v4 .course-title {
    font-size: 18px;
  }
}

.course-list {
  padding: 10px;
  border: 1px solid #EDEDED;
  border-radius: 8px;
}

.course-list .course-cover-thumb {
  width: 150px;
}

@media (min-width: 1200px) {
  .course-list .course-cover-thumb {
    width: 250px;
  }
}

.course-list .course-cover + .course-layout-content {
  padding: 0 0 0 20px;
}

.course-list-v2 {
  padding: 0;
  border: 0;
}

.course-list-v3 {
  padding: 0;
  border: 0;
}

.course-list-v3 .course-cover-thumb {
  width: 200px;
}

@media (min-width: 1200px) {
  .course-list-v3 .course-cover-thumb {
    width: 260px;
  }
}

@media (min-width: 1200px) {
  .course-list-v3 .course-cover + .course-layout-content {
    padding: 0 0 0 1.875rem;
  }
}

.course-list-v3 .course-price {
  font-size: 18px;
}

@media (min-width: 1200px) {
  .course-list-v3 .course-price {
    font-size: 24px;
  }
}

.course-list-v3 .course-price > * {
  display: block;
}

.course-list-v3 .course-price del,
.course-list-v3 .course-price .origin-price {
  margin: 0;
}

.course-list-v3 .course-meta-price {
  text-align: right;
  padding: 30px 0 30px 30px;
  border-left: 1px solid #EDEDED;
}

.course-list-v3 .save-bookmark-btn,
.course-list-v3 .btn-wishlist-course {
  margin-left: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-list-v3 .save-bookmark-btn,
  .course-list-v3 .btn-wishlist-course {
    margin-left: 1.875rem;
  }
}

.course-list-v3 .main-info {
  padding-right: 0.9375rem;
}

@media (min-width: 1200px) {
  .course-list-v3 .main-info {
    padding-right: 1.875rem;
  }
}

.course-list-v3 .course-excerpt {
  font-size: 14px;
  margin: 0 0 15px;
}

.course-list-v3 .course-meta-bottom {
  border: 0;
}

@media (max-width: 575px) {
  .course-list-v3 .course-cover-thumb {
    width: 100%;
    margin: 0 0 0.9375rem 0;
    border-radius: 8px;
  }
  .course-list-v3 .course-layout-content {
    padding: 0 !important;
  }
  .course-list-v3 .course-price > * {
    margin-left: 5px;
    display: inline-block;
  }
  .course-list-v3 .course-meta-price {
    padding: 5px 0 0;
    border: 0;
    text-align: left;
  }
  .course-list-v3 .course-meta-bottom {
    padding: 0;
  }
}

.wishlist-not-found {
  font-size: 1rem;
  text-transform: capitalize;
}

.save-bookmark-btn,
.btn-wishlist-course {
  display: inline-block;
  color: var(--educrat-theme-color);
  text-transform: capitalize;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.save-bookmark-btn i,
.btn-wishlist-course i {
  font-size: 1rem;
  line-height: 1;
  font-weight: 400;
  display: inline-block;
  vertical-align: text-top;
}

.save-bookmark-btn .wishlist-text,
.btn-wishlist-course .wishlist-text {
  margin-left: 10px;
  display: inline-block;
  font-size: 0.9375rem;
  font-weight: 500;
}

.save-bookmark-btn:hover .wishlist-text, .save-bookmark-btn:focus .wishlist-text,
.btn-wishlist-course:hover .wishlist-text,
.btn-wishlist-course:focus .wishlist-text {
  text-decoration: underline;
}

.save-bookmark-btn.apus-wishlist-added i:before,
.btn-wishlist-course.apus-wishlist-added i:before {
  content: '\f00d';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.save-bookmark-btn.is-loading i,
.save-bookmark-btn.loading i,
.btn-wishlist-course.is-loading i,
.btn-wishlist-course.loading i {
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.save-bookmark-btn.is-loading i:before,
.save-bookmark-btn.loading i:before,
.btn-wishlist-course.is-loading i:before,
.btn-wishlist-course.loading i:before {
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.save-bookmark-btn.only-icon,
.btn-wishlist-course.only-icon {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  background: #EEF2F6;
  color: #6A7A99;
}

.save-bookmark-btn.only-icon.apus-wishlist-added, .save-bookmark-btn.only-icon:hover,
.btn-wishlist-course.only-icon.apus-wishlist-added,
.btn-wishlist-course.only-icon:hover {
  background: var(--educrat-theme-color);
}

.save-bookmark-btn.only-icon.apus-wishlist-added, .save-bookmark-btn.only-icon.apus-wishlist-added i, .save-bookmark-btn.only-icon:hover, .save-bookmark-btn.only-icon:hover i,
.btn-wishlist-course.only-icon.apus-wishlist-added,
.btn-wishlist-course.only-icon.apus-wishlist-added i,
.btn-wishlist-course.only-icon:hover,
.btn-wishlist-course.only-icon:hover i {
  color: #fff !important;
}

.save-bookmark-btn.only-icon i.tutor-icon-bookmark-bold,
.btn-wishlist-course.only-icon i.tutor-icon-bookmark-bold {
  color: var(--educrat-theme-color);
}

.sidebar-course .widget_apus_course_filter_keywords {
  border: 0;
  padding: 0;
}

@media (min-width: 1320px) {
  .sidebar-course .sidebar-left {
    padding-right: 1.875rem;
  }
  .sidebar-course .sidebar-right {
    padding-left: 1.875rem;
  }
}

.sidebar-course-single .close-sidebar-btn {
  display: none !important;
}

.sidebar-course-single .sidebar .widget {
  border: 0;
}

.course-list-check {
  list-style: none;
  padding: 0;
  margin: 0;
}

.course-list-check label {
  cursor: pointer;
  font-weight: 400;
  width: 100%;
  padding-left: 25px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  color: var(--educrat-link-color);
}

.course-list-check label:before {
  position: absolute;
  top: 5px;
  left: 0;
  width: 15px;
  height: 15px;
  border: 2px solid #6A7A99;
  background: #fff;
  content: '';
  border-radius: 0;
}

.course-list-check label:after {
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  position: absolute;
  left: 4px;
  top: 5px;
  font-size: 8px;
  color: #fff;
  font-weight: 900;
  opacity: 0;
  filter: alpha(opacity=0);
}

.course-list-check label .count {
  margin-left: auto;
}

.course-list-check > li {
  margin: 0 0 6px;
}

.course-list-check > li:last-child {
  margin-bottom: 0;
}

.course-list-check input:checked + label:before {
  border-color: #1A064F;
  background: #1A064F;
}

.course-list-check input:checked + label:after {
  opacity: 1;
  filter: alpha(opacity=100);
}

.course-list-check .review-stars-rated-wrapper {
  margin-top: 2px;
}

.rating-list label:before {
  border-radius: 50%;
  background: transparent !important;
}

.rating-list label:after {
  content: '';
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #1A064F;
  top: 9px;
}

.learn-press-progress .learn-press-progress__active {
  background: var(--educrat-theme-color);
}

.course-curriculum .section-header .learn-press-progress {
  width: 100%;
  height: 4px;
}

.learn-press-progress::before {
  background: #e5e5e5;
}

#popup-course #popup-content #learn-press-quiz-app .questions-pagination .nav-links .page-numbers {
  border-radius: 8px;
  border: 1px solid var(--educrat-theme-color);
  color: var(--educrat-theme-color);
  background: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-weight: 600;
  font-size: 15px;
  margin: 0 3px;
}

#popup-course #popup-content #learn-press-quiz-app .questions-pagination .nav-links .page-numbers.current, #popup-course #popup-content #learn-press-quiz-app .questions-pagination .nav-links .page-numbers:hover, #popup-course #popup-content #learn-press-quiz-app .questions-pagination .nav-links .page-numbers:focus {
  color: #fff;
  background: var(--educrat-theme-color);
}

#popup-course #popup-footer .course-item-nav .prev:before,
#popup-course #popup-footer .course-item-nav .next:before {
  color: var(--educrat-theme-color) !important;
}

#popup-course #popup-footer .course-item-nav .prev a,
#popup-course #popup-footer .course-item-nav .next a {
  font-weight: 600;
  color: var(--educrat-theme-color) !important;
}

#popup-course #popup-footer .course-item-nav .course-item-nav__name {
  border-radius: 8px;
  background: var(--educrat-theme-color);
  color: #fff;
  font-weight: 600;
}

.quiz-status .countdown {
  padding: 17px 30px;
  border-radius: 8px;
}

#popup-course #popup-content .lp-button.instant-check.loading .instant-check__icon {
  display: none;
}

.answer-options .answer-option input[type="checkbox"]::after,
.answer-options .answer-option input[type="radio"]::after {
  top: 12px;
}

.quiz-result .result-statistic .result-statistic-field p {
  color: var(--educrat-link-color);
}

#popup-course .quiz-result .result-grade svg circle {
  stroke: var(--educrat-theme-color);
}

#popup-course #sidebar-toggle::before {
  color: var(--educrat-theme-color);
}

.learnpress-page .lp-modal-footer {
  text-align: center;
  padding: 20px;
}

.learnpress-page .lp-modal-footer .lp-button {
  margin: 0 5px;
  padding: 10px 25px;
}

#popup-course .quiz-questions .lp-fib-content .lp-fib-input > input {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  margin: 0 2px;
}

.lp-modal-dialog .lp-modal-content .lp-modal-body .main-content {
  padding: 0.9375rem;
}

.lp-modal-dialog .lp-modal-content .lp-modal-header h3 {
  text-align: center;
  font-size: 18px;
}

.content-item-summary .form-button-finish-course {
  margin: 1.875rem 0 0;
}

#popup-course #popup-content #learn-press-content-item .content-item-wrap {
  clear: both;
  overflow: hidden;
}

#popup-course #popup-header .popup-header__inner .course-title a {
  font-size: 18px;
}

.apus-lp-content-area ul.curriculum-sections .section-header .section-meta {
  display: none !important;
}

.learn-press-profile-course__tab__inner {
  background: transparent;
}

.learn-press-profile-course__tab__inner > li {
  margin-right: 15px !important;
}

.learn-press-profile-course__tab__inner > li:last-child {
  margin-right: 0 !important;
}

.learn-press-profile-course__tab__inner > li a {
  border: 1px solid #EDEDED;
  display: inline-block;
  padding: .7rem 1.6rem;
  background: #ffffff;
  font-weight: 500;
  font-size: 14px;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.learn-press-profile-course__tab__inner > li a.active {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

#learn-press-profile {
  background: transparent;
}

@media (min-width: 1200px) {
  #learn-press-profile {
    margin-bottom: 1.875rem;
  }
}

.learn-press-course-tab-filters .learn-press-filters a {
  cursor: pointer;
  font-weight: 500;
}

.learn-press-course-tab-filters .learn-press-filters a.active {
  color: var(--educrat-theme-color);
}

#learn-press-profile .dashboard-general-statistic__row .statistic-box {
  background: var(--educrat-theme-color-007) !important;
  border: 1px solid var(--educrat-theme-color-010);
  color: var(--educrat-theme-color) !important;
}

#learn-press-profile .dashboard-general-statistic__row .statistic-box .statistic-box__text,
#learn-press-profile .dashboard-general-statistic__row .statistic-box .statistic-box__number {
  color: inherit;
}

.lp-content-area {
  padding: 0;
}

.lp-user-profile #profile-sidebar {
  border: 0;
  margin: 0;
}

#learn-press-profile #profile-nav .lp-profile-nav-tabs li {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 0;
  margin: 0 0 10px;
  background: #fff !important;
}

#learn-press-profile #profile-nav .lp-profile-nav-tabs li:last-child {
  margin-bottom: 0;
}

#learn-press-profile #profile-nav .lp-profile-nav-tabs li a {
  border-radius: 8px;
  background: var(--educrat-theme-color-007);
  color: var(--educrat-theme-color);
}

#learn-press-profile #profile-nav .lp-profile-nav-tabs li:hover > a, #learn-press-profile #profile-nav .lp-profile-nav-tabs li.active > a {
  background: var(--educrat-theme-color);
  color: #fff;
}

#learn-press-profile #profile-nav .lp-profile-nav-tabs li > ul {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  margin-top: -15px;
}

#learn-press-profile #profile-nav .lp-profile-nav-tabs li.active > ul {
  -webkit-box-shadow: none;
  box-shadow: none;
  margin: 10px 0 0;
  background: transparent;
  padding: 0;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs > li > a:after,
.lp-user-profile #profile-nav .lp-profile-nav-tabs > li > a > i {
  color: inherit !important;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs > li ul li a {
  border: 0;
  white-space: nowrap;
}

.lp-user-profile #profile-nav .lp-profile-nav-tabs > li ul li a i {
  font-size: 12px;
  margin-right: 3px;
  background: transparent !important;
  color: inherit !important;
}

.lp-archive-courses .apus-lp-content-area {
  display: block;
}

.course-single-tab {
  background: #fff;
  border-bottom: 2px solid #EDEDED !important;
  margin: 0 0 1.875rem;
  list-style: none !important;
}

@media (min-width: 1200px) {
  .course-single-tab {
    margin-bottom: 50px;
  }
}

.course-single-tab > li {
  margin: 0 0 -2px;
  line-height: inherit;
}

.course-single-tab > li + li {
  margin-left: 15px;
}

@media (min-width: 1200px) {
  .course-single-tab > li + li {
    margin-left: 38px;
  }
}

.course-single-tab > li > .nav-link,
.course-single-tab > li > .tutor-nav-link,
.course-single-tab > li > label,
.course-single-tab > li > a {
  background: transparent !important;
  color: var(--educrat-text-color);
  font-size: 0.9375rem;
  padding: 0 0 15px;
  margin: 0;
  border: 0 !important;
  border-radius: 0;
  position: relative;
  cursor: pointer;
}

.course-single-tab > li > .nav-link:before,
.course-single-tab > li > .tutor-nav-link:before,
.course-single-tab > li > label:before,
.course-single-tab > li > a:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  background: var(--educrat-theme-color);
  width: 0;
  height: 2px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.course-single-tab > li > .nav-link:hover, .course-single-tab > li > .nav-link:focus, .course-single-tab > li > .nav-link.is-active, .course-single-tab > li > .nav-link.active,
.course-single-tab > li > .tutor-nav-link:hover,
.course-single-tab > li > .tutor-nav-link:focus,
.course-single-tab > li > .tutor-nav-link.is-active,
.course-single-tab > li > .tutor-nav-link.active,
.course-single-tab > li > label:hover,
.course-single-tab > li > label:focus,
.course-single-tab > li > label.is-active,
.course-single-tab > li > label.active,
.course-single-tab > li > a:hover,
.course-single-tab > li > a:focus,
.course-single-tab > li > a.is-active,
.course-single-tab > li > a.active {
  color: var(--educrat-theme-color);
}

.course-single-tab > li > .nav-link:hover:before, .course-single-tab > li > .nav-link:focus:before, .course-single-tab > li > .nav-link.is-active:before, .course-single-tab > li > .nav-link.active:before,
.course-single-tab > li > .tutor-nav-link:hover:before,
.course-single-tab > li > .tutor-nav-link:focus:before,
.course-single-tab > li > .tutor-nav-link.is-active:before,
.course-single-tab > li > .tutor-nav-link.active:before,
.course-single-tab > li > label:hover:before,
.course-single-tab > li > label:focus:before,
.course-single-tab > li > label.is-active:before,
.course-single-tab > li > label.active:before,
.course-single-tab > li > a:hover:before,
.course-single-tab > li > a:focus:before,
.course-single-tab > li > a.is-active:before,
.course-single-tab > li > a.active:before {
  width: 100%;
}

.course-single-tab > li:hover > .tutor-nav-link,
.course-single-tab > li:hover > .tutor-nav-link.is-active,
.course-single-tab > li:hover > .nav-link,
.course-single-tab > li:hover > .nav-link.active,
.course-single-tab > li:hover > label,
.course-single-tab > li:hover > a.active,
.course-single-tab > li:hover > a, .course-single-tab > li.active > .tutor-nav-link,
.course-single-tab > li.active > .tutor-nav-link.is-active,
.course-single-tab > li.active > .nav-link,
.course-single-tab > li.active > .nav-link.active,
.course-single-tab > li.active > label,
.course-single-tab > li.active > a.active,
.course-single-tab > li.active > a {
  color: var(--educrat-theme-color);
}

.course-single-tab > li:hover > .tutor-nav-link:before,
.course-single-tab > li:hover > .tutor-nav-link.is-active:before,
.course-single-tab > li:hover > .nav-link:before,
.course-single-tab > li:hover > .nav-link.active:before,
.course-single-tab > li:hover > label:before,
.course-single-tab > li:hover > a.active:before,
.course-single-tab > li:hover > a:before, .course-single-tab > li.active > .tutor-nav-link:before,
.course-single-tab > li.active > .tutor-nav-link.is-active:before,
.course-single-tab > li.active > .nav-link:before,
.course-single-tab > li.active > .nav-link.active:before,
.course-single-tab > li.active > label:before,
.course-single-tab > li.active > a.active:before,
.course-single-tab > li.active > a:before {
  width: 100%;
}

.learn-press-filters > li span, .learn-press-filters > li a {
  font-weight: 500;
}

.learn-press-filters > li span {
  color: var(--educrat-theme-color);
}

.learn-press-profile-course__tab__inner a.active::before {
  display: none;
}

#learn-press-profile .wrapper-profile-header {
  color: var(--educrat-text-color);
  background: transparent;
  margin: 0;
}

#learn-press-profile .wrapper-profile-header .lp-profile-right .lp-profile-username {
  font-weight: 500;
  color: var(--educrat-link-color);
}

@media (min-width: 1200px) {
  #learn-press-profile .wrapper-profile-header .lp-profile-right .lp-profile-username {
    font-size: 22px;
  }
}

#learn-press-profile .wrapper-profile-header .lp-profile-right .lp-profile-username:after, #learn-press-profile .wrapper-profile-header .lp-profile-right .lp-profile-username:before {
  display: none;
}

#learn-press-profile .wrapper-profile-header .lp-profile-content-area {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  border: 1px solid #EDEDED;
  -webkit-box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  display: block;
  min-height: 0;
}

@media (min-width: 1200px) {
  #learn-press-profile .wrapper-profile-header .lp-profile-content-area {
    margin-bottom: 50px;
  }
}

@media (min-width: 990px) {
  #learn-press-profile .wrapper-profile-header .lp-profile-content-area {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
  }
}

#learn-press-profile .wrapper-profile-header .lp-profile-left {
  position: static;
  width: 100%;
  padding: 0;
  border: 0;
}

@media (min-width: 990px) {
  #learn-press-profile .wrapper-profile-header .lp-profile-left {
    width: 200px;
  }
}

.lp-user-profile-avatar {
  border-radius: 8px;
  overflow: hidden;
}

#learn-press-profile .wrapper-profile-header .lp-profile-right {
  margin: 0;
  width: 100%;
}

@media (max-width: 989px) {
  #learn-press-profile .wrapper-profile-header .lp-profile-right {
    padding: 15px 0 0;
  }
}

@media (min-width: 990px) {
  #learn-press-profile .wrapper-profile-header .lp-profile-right {
    width: calc(100% - 200px);
  }
}

#learn-press-profile .lp-user-profile-socials a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border: 0;
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
  font-size: 0.9375rem;
}

#learn-press-profile .lp-user-profile-socials a:focus, #learn-press-profile .lp-user-profile-socials a:hover {
  background: var(--educrat-theme-color);
  color: #fff;
}

#learn-press-profile .lp-user-profile-socials {
  margin: 20px 0 0;
}

#learn-press-profile #profile-content {
  padding-top: 0;
}

@media (max-width: 990px) {
  #learn-press-profile #profile-sidebar {
    margin: 0 0 1.875rem;
  }
  #learn-press-profile .dashboard-general-statistic__row .statistic-box {
    margin-bottom: 15px;
  }
}

div.order-recover input[type="text"] {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  outline: none;
  height: 50px;
}

div.order-recover input[type="text"]:focus {
  border-color: var(--educrat-theme-color);
}

.order-comments {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  outline: none;
  min-height: 145px;
  resize: none;
  padding: 10px 20px;
}

.order-comments:focus {
  border-color: var(--educrat-theme-color);
}

#checkout-order .lp-checkout-order__inner {
  border-color: #EDEDED;
}

#checkout-order .lp-checkout-order__inner .course-name a:hover {
  color: var(--educrat-theme-color);
}

#learn-press-checkout-form {
  margin: 0 0 1.875rem;
}

@media (min-width: 816px) {
  #learn-press-checkout-form .lp-checkout-form__after,
  #learn-press-checkout-form .lp-checkout-form__before {
    margin: 0;
    width: calc(50% - 15px);
  }
}

.nav-tabs-account {
  justify-content: center;
  margin: 0.9375rem 0;
}

@media (min-width: 1200px) {
  .nav-tabs-account {
    margin: 1.875rem 0;
  }
}

.nav-tabs-account li {
  margin: 0 10px;
}

.nav-tabs-account li button {
  border: 0;
  display: inline-block;
  padding: .7rem 2rem;
  background: #ffffff;
  font-weight: 500;
  font-size: 1rem;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.nav-tabs-account li button.active {
  color: #fff;
  background: var(--educrat-theme-color);
}

.learn-press-form-login,
.learn-press-form-register {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  border: 1px solid #EDEDED;
  background: #fff;
}

@media (max-width: 1199px) {
  .learn-press-form-login,
  .learn-press-form-register {
    padding: 20px;
    margin-bottom: 1.875rem;
  }
}

.learn-press-form-login .form-fields .form-field input[type="url"],
.learn-press-form-login .form-fields .form-field input[type="tel"],
.learn-press-form-login .form-fields .form-field input[type="number"],
.learn-press-form-login .form-fields .form-field input[type="password"],
.learn-press-form-login .form-fields .form-field input[type="text"],
.learn-press-form-register .form-fields .form-field input[type="url"],
.learn-press-form-register .form-fields .form-field input[type="tel"],
.learn-press-form-register .form-fields .form-field input[type="number"],
.learn-press-form-register .form-fields .form-field input[type="password"],
.learn-press-form-register .form-fields .form-field input[type="text"] {
  border-radius: 8px;
  border: 1px solid #EDEDED;
}

.learn-press-form-login .form-fields .form-field input[type="url"]:focus,
.learn-press-form-login .form-fields .form-field input[type="tel"]:focus,
.learn-press-form-login .form-fields .form-field input[type="number"]:focus,
.learn-press-form-login .form-fields .form-field input[type="password"]:focus,
.learn-press-form-login .form-fields .form-field input[type="text"]:focus,
.learn-press-form-register .form-fields .form-field input[type="url"]:focus,
.learn-press-form-register .form-fields .form-field input[type="tel"]:focus,
.learn-press-form-register .form-fields .form-field input[type="number"]:focus,
.learn-press-form-register .form-fields .form-field input[type="password"]:focus,
.learn-press-form-register .form-fields .form-field input[type="text"]:focus {
  border-color: var(--educrat-theme-color);
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

@media (min-width: 1200px) {
  .learn-press-form-login .form-fields .form-field input[type="url"],
  .learn-press-form-login .form-fields .form-field input[type="tel"],
  .learn-press-form-login .form-fields .form-field input[type="number"],
  .learn-press-form-login .form-fields .form-field input[type="password"],
  .learn-press-form-login .form-fields .form-field input[type="text"],
  .learn-press-form-register .form-fields .form-field input[type="url"],
  .learn-press-form-register .form-fields .form-field input[type="tel"],
  .learn-press-form-register .form-fields .form-field input[type="number"],
  .learn-press-form-register .form-fields .form-field input[type="password"],
  .learn-press-form-register .form-fields .form-field input[type="text"] {
    height: 50px;
    padding: 10px 20px;
  }
}

.learn-press-form-login button[type="submit"],
.learn-press-form-register button[type="submit"] {
  border: 2px solid var(--educrat-theme-color);
  color: #fff;
  background: var(--educrat-theme-color);
  border-radius: 8px;
  font-weight: 500;
}

.learn-press-form-login button[type="submit"]:hover, .learn-press-form-login button[type="submit"]:focus,
.learn-press-form-register button[type="submit"]:hover,
.learn-press-form-register button[type="submit"]:focus {
  background: #fff;
  border-color: var(--educrat-theme-color);
  color: var(--educrat-theme-color);
}

@media (min-width: 1200px) {
  .learn-press-form-login .lp-password-input .lp-show-password-input,
  .learn-press-form-register .lp-password-input .lp-show-password-input {
    top: 12px;
  }
}

.learn-press-form-login h3,
.learn-press-form-register h3 {
  margin: 0 0 20px;
}

@media (max-width: 1199px) {
  .learn-press-form-login h3,
  .learn-press-form-register h3 {
    font-size: 23px;
  }
}

.widget-courses .slick-carousel .slick-dots {
  padding: 10px 0 0;
}

@media (min-width: 1400px) {
  .widget-courses .slick-carousel.stretch_pagination .slick-prev {
    left: -65px;
  }
}

@media (min-width: 1400px) {
  .widget-courses .slick-carousel.stretch_pagination .slick-next {
    right: -65px;
  }
}

@media (min-width: 1350px) {
  .widget-courses.fullscreen .slick-list {
    overflow: visible;
  }
}

@media (min-width: 1400px) {
  .widget-courses.fullscreen .slick-carousel .slick-next {
    right: -10px;
  }
  .widget-courses.fullscreen .slick-carousel .slick-prev {
    left: -10px;
  }
}

.instructor-grid-inside {
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .instructor-grid-inside {
    margin-bottom: 1.875rem;
  }
}

.instructor-grid-inside .instructor-name {
  font-weight: 500;
  font-size: 17px;
  margin: 0 0 5px;
}

.instructor-grid-inside .instructor-bottom {
  margin-top: 5px;
}

.instructor-grid-inside .instructor-bottom [class*="flaticon"] {
  font-size: 1rem;
  line-height: 1;
  margin-right: 5px;
  vertical-align: middle;
}

.instructor-grid-inside .instructor-bottom > * {
  margin-right: 15px;
}

.instructor-grid-inside .instructor-bottom > *:last-child {
  margin-right: 0;
}

.instructor-grid-inside .socials {
  text-align: center;
  font-size: 1rem;
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 0;
  width: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.instructor-grid-inside .socials a {
  margin: 0 7px;
  color: #fff !important;
}

@media (min-width: 1200px) {
  .instructor-grid-inside .socials a {
    margin: 0 12px;
  }
}

.instructor-grid-inside .cover-inner {
  display: block;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 23px;
}

.instructor-grid-inside .cover-inner:before {
  z-index: 1;
  top: 0;
  left: 0;
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  background: #1A064F;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.instructor-grid-inside .cover-inner img {
  width: 100%;
}

.instructor-grid-inside:hover .cover-inner:before {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.instructor-grid-inside:hover .socials {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.instructor-grid-inside-v2 {
  text-align: center;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  background: #fff;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

@media (min-width: 1200px) {
  .instructor-grid-inside-v2 {
    padding: 1.875rem;
    margin-bottom: 1.875rem;
  }
}

.instructor-grid-inside-v2 .instructor-name {
  font-weight: 500;
  font-size: 17px;
  margin: 0 0 3px;
}

.instructor-grid-inside-v2 .socials {
  margin: 5px 0 0;
  font-size: 13px;
}

.instructor-grid-inside-v2 .socials a {
  margin: 0 5px;
  color: #6A7A99;
}

@media (min-width: 1200px) {
  .instructor-grid-inside-v2 .socials a {
    margin: 0 10px;
  }
}

.instructor-grid-inside-v2 .socials a:hover, .instructor-grid-inside-v2 .socials a:focus {
  color: var(--educrat-theme-color);
}

.instructor-grid-inside-v2 .cover-inner {
  display: block;
  overflow: hidden;
  border-radius: 50%;
  margin: 0 auto 20px;
  width: 90px;
  height: 90px;
}

.instructor-grid-inside-v2 .btn {
  margin: 15px 0 0;
  width: 100%;
  font-weight: 400;
  font-size: 0.9375rem;
  border-radius: 50px;
  padding: 10px 20px;
}

.instructor-grid-inside-v2:hover {
  -webkit-box-shadow: 0 40px 30px 0 rgba(25, 25, 46, 0.04);
  box-shadow: 0 40px 30px 0 rgba(25, 25, 46, 0.04);
}

.instructor-grid-inside-v2:hover .btn {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.widget-instructors.p-left .slick-carousel .slick-dots {
  text-align: left;
  padding-left: 10px;
}

.widget-instructors.p-right .slick-carousel .slick-dots {
  text-align: right;
  padding-right: 10px;
}

@media (min-width: 1350px) {
  .widget-instructors.fullscreen .slick-list {
    overflow: visible;
  }
}

.search-form-course {
  position: relative;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
}

.search-form-course .btn-search {
  margin: 0 !important;
}

.search-form-course .btn-search i {
  font-size: 20px;
  line-height: 1;
  vertical-align: middle;
}

.search-form-course .btn-search i + .text-search {
  display: inline-block;
  margin-left: 12px;
  vertical-align: middle;
}

.search-form-course .form-control {
  border-color: #fff;
}

.search-form-course select {
  border: 0;
  background: #fff;
  padding: 10px 0;
  color: var(--educrat-text-color);
}

@media (min-width: 576px) {
  .search-form-course .addon {
    width: 52%;
    padding: 0 1.875rem;
  }
}

.search-form-course .addon > * {
  width: 100%;
  flex-grow: 1;
  padding: 0 1.875rem;
  border-left: 1px solid #EDEDED;
}

@media (max-width: 575px) {
  .search-form-course .addon > * {
    margin: 0 18px 10px;
    padding: 0;
    border-left: 0;
    width: auto;
    border-bottom: 1px solid #EDEDED;
  }
  .search-form-course .addon > *:last-child {
    border-bottom: 0;
  }
  .search-form-course .addon > * select {
    width: 100%;
  }
}

.search-form-course.button {
  padding: 0 0 0 18px;
  border-bottom: 2px solid #EDEDED;
  border-radius: 0;
}

.search-form-course.button .btn-search {
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.search-form-course.button .form-control {
  font-size: 1rem;
  font-weight: 500;
  color: var(--educrat-link-color);
}

@media (min-width: 1200px) {
  .search-form-course.button .form-control {
    height: 60px;
    font-size: 18px;
  }
}

.search-form-course.button .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  opacity: 1;
  filter: alpha(opacity=100);
  color: var(--educrat-link-color);
}

.search-form-course.button .form-control::-moz-placeholder {
  /* Firefox 19+ */
  opacity: 1;
  filter: alpha(opacity=100);
  color: var(--educrat-link-color);
}

.search-form-course.button .form-control:-ms-input-placeholder {
  /* IE 10+ */
  opacity: 1;
  filter: alpha(opacity=100);
  color: var(--educrat-link-color);
}

.search-form-course.button .form-control:-moz-placeholder {
  /* Firefox 18- */
  opacity: 1;
  filter: alpha(opacity=100);
  color: var(--educrat-link-color);
}

.apus-search-form-course .search-button {
  font-size: 20px;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.apus-search-form-course.button .form-inner {
  max-width: 1320px;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  margin-left: auto;
  margin-right: auto;
}

.apus-search-form-course.button .search-form-popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 7;
  background: #fff;
  padding: 1.875rem 0;
  width: 100% !important;
}

@media (min-width: 1200px) {
  .apus-search-form-course.button .search-form-popup {
    padding: 60px 0 80px;
  }
}

.apus-search-form-course .close-search {
  display: inline-block;
  line-height: 40px;
  text-align: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 12px;
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
}

.apus-search-form-course .close-search i:before {
  font-weight: 700;
}

.apus-search-form-course .close-search:hover, .apus-search-form-course .close-search:focus {
  color: #fff;
  background: var(--educrat-theme-color);
}

#checkout-account-guest #guest_email {
  outline: none !important;
  padding: 5px 20px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #EDEDED;
}

#checkout-account-guest #guest_email:focus {
  border-color: var(--educrat-theme-color);
}

#learn-press-profile #profile-content .lp-button,
#learn-press-profile #profile-content .lp-archive-courses ul.learn-press-courses .course {
  margin: 0;
}

.event-item {
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .event-item {
    margin-bottom: 1.875rem;
  }
}

.event-item .entry-thumb {
  border-radius: 8px;
  overflow: hidden;
}

.event-item .entry-title {
  font-weight: 500;
  font-size: 17px;
  margin: 0 0 5px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-item .inner-left {
  padding-right: 7px;
}

.event-item .event-metas {
  position: relative;
  z-index: 1;
  margin: -45px 10px 0;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

.event-item .event-metas i {
  font-size: 1rem;
  margin-right: 5px;
}

.event-item .event-address {
  margin-left: 10px;
}

.event-item .btn {
  font-size: 15px;
  padding: 5px 30px;
  border-radius: 40px;
}

.event-item:hover .btn {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.event-grid {
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.07);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .event-grid {
    margin-bottom: 1.875rem;
  }
}

.event-grid .icon-space {
  font-size: 1rem;
  margin-right: 5px;
}

.event-grid .entry-title {
  font-weight: 500;
  font-size: 17px;
  margin: 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-grid .inner {
  padding: 10px 20px;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #EDEDED;
  border-radius: 0 0 8px 8px;
}

@media (min-width: 1200px) {
  .event-grid .inner {
    padding: 20px 30px;
  }
}

.event-grid .entry-thumb {
  margin: 0;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.event-grid:hover {
  -webkit-box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.15);
  box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.15);
}

.event-grid-v2 {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  background: #F7F8FB;
  border-radius: 8px;
  overflow: hidden;
  height: 200px;
}

@media (min-width: 1200px) {
  .event-grid-v2 {
    height: 350px;
  }
}

.event-grid-v2 .startdate {
  line-height: 1;
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1;
}

@media (min-width: 1200px) {
  .event-grid-v2 .startdate {
    top: 50px;
    left: 50px;
  }
}

.event-grid-v2 .day {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-weight: 700;
  font-size: 35px;
  color: var(--educrat-link-color);
}

@media (min-width: 1200px) {
  .event-grid-v2 .day {
    font-size: 45px;
  }
}

.event-grid-v2 .month {
  text-transform: uppercase;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-size: 18px;
  font-weight: 500;
  margin-left: 15px;
}

.event-grid-v2 .entry-title {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transform: translateY(70px);
  -ms-transform: translateY(70px);
  -o-transform: translateY(70px);
  transform: translateY(70px);
  margin: 0 0 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .event-grid-v2 .entry-title {
    font-size: 24px;
    line-height: 35px;
  }
}

.event-grid-v2 .btn {
  padding: 8px 25px;
  font-weight: 500;
  font-size: 0.9375rem;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(50px);
  -ms-transform: translateY(50px);
  -o-transform: translateY(50px);
  transform: translateY(50px);
}

.event-grid-v2 .btn i {
  margin-left: 10px;
}

.event-grid-v2 .event-metas {
  padding: 20px;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

@media (min-width: 1200px) {
  .event-grid-v2 .event-metas {
    padding: 40px;
  }
}

.event-grid-v2:hover {
  background: #282664;
  color: #fff;
}

.event-grid-v2:hover .month,
.event-grid-v2:hover .entry-title a,
.event-grid-v2:hover .day {
  color: #fff;
}

.event-grid-v2:hover .entry-title {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.event-grid-v2:hover .btn {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.event-list {
  background: #fff;
  border: 1px solid #EDEDED;
  padding: 10px;
  border-radius: 8px;
  -webkit-box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .event-list {
    margin-bottom: 1.875rem;
  }
}

.event-list .post-thumbnail {
  width: 120px;
  overflow: hidden;
  display: block;
  border-radius: 8px;
}

.event-list .entry-title {
  font-size: 17px;
  margin: 0 0 3px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-list .inner-right {
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .event-list .inner-right {
    padding-left: 38px;
  }
}

.event-list .inner-right i {
  margin-right: 5px;
}

.event-list .event-address {
  margin-left: 12px;
}

.event-list .btn {
  white-space: nowrap;
  padding: 8px 25px;
  font-weight: 500;
  font-size: 0.9375rem;
  opacity: 0;
  filter: alpha(opacity=0);
}

.event-list .btn i {
  margin-left: 10px;
}

.event-list .more {
  padding: 0 10px;
}

.event-list:hover .btn {
  opacity: 1;
  filter: alpha(opacity=100);
}

.event-list-small {
  margin-bottom: 20px;
}

.event-list-small .entry-thumb {
  margin: 0;
  width: 65px;
  border-radius: 8px;
  overflow: hidden;
}

.event-list-small .entry-title {
  font-size: 15px;
  font-weight: 500;
  margin: 0 0 3px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-list-small .startdate {
  font-size: 13px;
}

.event-list-small .inner-right {
  padding-left: 15px;
}

.event-list-v2 {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  -webkit-box-shadow: 0 10px 30px 0 rgba(1, 33, 58, 0.05);
  box-shadow: 0 10px 30px 0 rgba(1, 33, 58, 0.05);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .event-list-v2 {
    margin-bottom: 1.875rem;
    -webkit-box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.07);
    box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.07);
  }
}

.event-list-v2 .entry-title {
  font-size: 17px;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-list-v2 .startdate {
  padding: 10px 15px;
  line-height: 1.3;
  text-align: center;
  display: ilnine-block;
  border-radius: 8px;
  color: #fff;
  background: var(--educrat-link-color);
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 500;
}

.event-list-v2 .startdate .day {
  font-size: 17px;
}

.event-list-v2 .inner-right {
  padding-left: 20px;
}

.event-list-v2 .event-address {
  line-height: 1;
  font-size: 14px;
  margin-top: 18px;
}

.event-list-v2 .event-address i {
  margin-right: 5px;
  font-size: 1rem;
}

.event-list-v2:hover {
  -webkit-box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.12);
  box-shadow: 0 25px 60px 0 rgba(1, 33, 58, 0.12);
}

.event-header {
  margin-bottom: 1.875rem;
}

@media (min-width: 576px) {
  .event-header .orderby {
    margin-left: auto;
  }
}

.event-header .orderby label {
  font-weight: 500;
  font-size: 14px;
  color: var(--educrat-link-color);
  margin-right: 15px;
}

.event-header select.orderby {
  cursor: pointer;
  color: var(--educrat-text-color);
  font-size: 14px;
  width: 100%;
  padding: 8px 50px 8px 15px;
  height: 50px;
  border: 0;
  border-radius: 8px;
  -webkit-appearance: none;
  -moz-appearance: none;
  -o-appearance: none;
  background: url("../images/select.png") #EEF2F6 right 15px center no-repeat;
}

.event-header .results-count {
  color: var(--educrat-link-color);
}

.header-single-envent {
  max-width: 1500px;
  margin-left: auto;
  margin-right: auto;
  background-color: var(--educrat-link-color);
  overflow: hidden;
  color: #fff;
  padding: 1.875rem 0;
  margin-bottom: 1.875rem;
  position: relative;
  z-index: 1;
}

@media (min-width: 1200px) {
  .header-single-envent {
    padding: 140px 0;
    border-radius: 8px;
  }
}

.header-single-envent:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(20, 3, 66, 0.7);
  z-index: -1;
}

.header-single-envent .entry-title {
  color: #fff;
  font-size: 25px;
  margin: 15px 0 10px;
}

@media (min-width: 1200px) {
  .header-single-envent .entry-title {
    font-size: 30px;
  }
}

.header-single-envent .detail-time-location {
  font-size: 14px;
}

.header-single-envent .detail-time-location i {
  font-size: 1rem;
  margin-right: 5px;
}

.header-single-envent .detail-time-location .event-address {
  margin-left: 12px;
}

.box-event-detail {
  max-width: 850px;
}

@media (min-width: 1350px) {
  .single-envent-content #main-content.col-lg-9 {
    width: 70%;
  }
  .single-envent-content .sidebar-wrapper.col-lg-3 {
    width: 30%;
  }
}

@media (min-width: 1200px) {
  .single-envent-content .sidebar-wrapper {
    margin-top: -100px;
    z-index: 1;
    position: relative;
  }
}

.apus_event_product {
  text-align: center;
  padding: 20px;
  background: #fff;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
  box-shadow: 0 20px 30px 0 rgba(25, 25, 46, 0.04);
}

@media (min-width: 1200px) {
  .apus_event_product {
    padding: 1.875rem;
  }
}

.apus_event_product .event-meta-price {
  margin-bottom: 15px;
  font-size: 24px;
  font-weight: 500;
  color: var(--educrat-link-color);
}

.apus_event_product .event-meta-price del {
  font-size: 0.9375rem;
  color: var(--educrat-text-color);
}

.apus_event_product .btn {
  width: 100%;
}

.apus_event_product .apus-social-share {
  margin-top: 20px;
}

.apus_event_product .apus-social-share strong {
  display: none;
}

.envent-participant {
  margin-top: 1.875rem;
}

.envent-participant .heading {
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 1.875rem;
}

.participant-item {
  text-align: center;
}

.participant-item .image {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 20px;
}

.participant-item .name {
  font-size: 17px;
  font-weight: 500;
  margin: 0;
}

.participant-item .job {
  margin: 5px 0 0;
}

@media (min-width: 1350px) {
  .widget-events .slick-list {
    overflow: visible;
  }
}

.widget-events.nofullscreen .slick-list {
  overflow: hidden;
}

@media (min-width: 1350px) {
  .widget-events.nofullscreen {
    margin-left: -15px;
    margin-right: -15px;
  }
  .widget-events.nofullscreen .slick-list {
    padding: 0 15px;
  }
}

@media (min-width: 1200px) {
  .widget-events.nofullscreen .simple_event {
    margin-bottom: 60px;
  }
  .widget-events.nofullscreen .slick-dots {
    padding: 0;
  }
}

.widget-events.p-left .slick-carousel .slick-dots {
  text-align: left;
  padding-left: 10px;
}

.widget-events.p-right .slick-carousel .slick-dots {
  text-align: right;
  padding-right: 10px;
}

.widget-events .item:not(.slick-active) .event-list-v2 {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.filter-top-sidebar {
  padding-bottom: 10px;
}

.filter-top-sidebar > * {
  margin: 0 0 15px;
}

@media (min-width: 576px) {
  .filter-top-sidebar > * {
    display: inline-block;
    vertical-align: top;
    margin: 0 20px 20px 0;
  }
}

.filter-top-sidebar select {
  cursor: pointer;
  color: var(--educrat-text-color);
  font-size: 14px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url("../images/select.png") #EEF2F6 right 10px center no-repeat;
  padding: 11px 12px;
  margin: 0;
  min-width: 125px;
  border: 1px solid #EEF2F6;
  border-radius: 8px;
}

@media (max-width: 575px) {
  .filter-top-sidebar select {
    width: 100%;
  }
}

.tutor-color-secondary,
.tutor-wrap {
  color: var(--educrat-text-color);
}

.tutor-color-black {
  color: var(--educrat-link-color);
}

.tutor-accordion .tutor-accordion-item {
  cursor: pointer;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  background: #F7F8FB !important;
  overflow: hidden;
}

.tutor-accordion .tutor-accordion-item + .tutor-accordion-item {
  margin-top: 10px;
}

.tutor-accordion .tutor-accordion-item .section-desc {
  color: var(--educrat-text-color);
  font-style: normal;
  margin: 10px 0 0;
}

.tutor-accordion .tutor-accordion-item:not(.closed) .section-toggle:before {
  content: "\e648" !important;
}

.tutor-accordion .tutor-accordion-item-header {
  height: auto !important;
  border: 0;
  padding: .75rem 1.25rem !important;
  line-height: 1;
  font-size: 1rem;
  font-weight: 500;
  color: var(--educrat-heading-color);
  background: #F7F8FB !important;
}

@media (min-width: 1200px) {
  .tutor-accordion .tutor-accordion-item-header {
    padding: 21px 1.875rem !important;
  }
}

.tutor-accordion .tutor-accordion-item-header:after {
  font-size: 13px;
  color: inherit;
  -webkit-transform: translateY(-50%) rotate(90deg);
  -moz-transform: translateY(-50%) rotate(90deg);
  -ms-transform: translateY(-50%) rotate(90deg);
  -o-transform: translateY(-50%) rotate(90deg);
  transform: translateY(-50%) rotate(90deg);
  right: 1.25rem;
}

@media (min-width: 1200px) {
  .tutor-accordion .tutor-accordion-item-header:after {
    right: 1.875rem;
  }
}

.tutor-accordion .tutor-accordion-item-header.is-active {
  color: var(--educrat-theme-color);
}

.tutor-accordion .tutor-accordion-item-header.is-active:after {
  -webkit-transform: translateY(-50%) rotate(-90deg);
  -moz-transform: translateY(-50%) rotate(-90deg);
  -ms-transform: translateY(-50%) rotate(-90deg);
  -o-transform: translateY(-50%) rotate(-90deg);
  transform: translateY(-50%) rotate(-90deg);
}

.tutor-accordion .tutor-accordion-item-body {
  margin: 0;
  background: #fff;
}

.tutor-accordion .tutor-accordion-item-body .tutor-accordion-item-body-content {
  border: 0;
  padding: 1rem 1.25rem;
}

@media (min-width: 1200px) {
  .tutor-accordion .tutor-accordion-item-body .tutor-accordion-item-body-content {
    padding: 25px 1.875rem;
  }
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item-icon {
  font-size: 11px;
  color: var(--educrat-theme-color);
  background: var(--educrat-theme-color-007);
  width: 25px;
  height: 25px;
  min-width: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 50%;
  margin: 0;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list {
  margin: 0;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item {
  margin: 0 0 20px;
  padding: 0;
  background: transparent !important;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item:last-child {
  margin-bottom: 0;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item .tutor-course-content-list-item-status {
  color: #27b737;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item .tutor-icon-lock-line {
  color: #f33066;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item .tutor-course-content-list-item-title {
  font-size: 0.9375rem;
  font-weight: 400;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 0 12px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.tutor-accordion .tutor-accordion-item-body .tutor-course-content-list-item:hover .tutor-course-content-list-item-title {
  color: var(--educrat-theme-color);
}

.comment-list .tutor-ratings-stars {
  font-size: 11px;
  margin: 5px 0 0;
}

.comment-list .tutor-avatar {
  -webkit-box-shadow: none;
  box-shadow: none;
  width: 100%;
  height: 100%;
}

.comment-form-theme .tutor-ratings-stars {
  font-size: 0.9375rem;
}

.widget-courses-related {
  margin: 1.875rem 0 0;
}

@media (min-width: 1200px) {
  .widget-courses-related {
    margin-top: 60px;
  }
}

.widget-courses-related .course-layout-item {
  background: transparent;
}

.tutor-widget-search .tutor-form-control {
  color: var(--educrat-text-color);
  font-size: .9375rem;
  height: 50px;
  padding: .6rem 1.29rem;
  border: 1px solid #EDEDED;
  border-radius: 8px;
}

.tutor-widget-search .tutor-form-control:focus {
  border-color: #1A064F;
}

.tutor-widget-search .tutor-icon-search {
  color: var(--educrat-link-color);
  left: 2px;
}

.tutor-widget-search .tutor-icon-search:focus, .tutor-widget-search .tutor-icon-search:hover {
  color: var(--educrat-theme-color);
}

.tutor-list-item {
  font-size: 0.9375rem;
  color: var(--educrat-link-color);
}

.tutor-list-item:not(:last-child) {
  margin-bottom: 6px;
}

.tutor-list-item .tutor-form-check-input {
  width: 15px;
  height: 15px;
  margin: 0 10px 0 0 !important;
  border: 2px solid #6A7A99;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.tutor-list-item .tutor-form-check-input:checked {
  background-color: var(--educrat-link-color);
  border-color: var(--educrat-link-color);
  background-size: 10px;
}

.tutor-form-control {
  font-size: 0.9375rem;
  padding: 13px 20px;
}

.tutor-form-select {
  padding: 13px 40px 13px 20px;
}

.tutor-form-select.is-active {
  border-color: var(--educrat-link-color);
}

.tutor-filter-top-sidebar {
  margin-bottom: 1.875rem;
  width: 100% !important;
  display: none;
}

.tutor-filter-top-sidebar .tutor-form {
  margin-left: -0.9375rem;
  margin-right: -0.9375rem;
}

.tutor-filter-top-sidebar .widget {
  width: 100%;
  float: left;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
}

@media (min-width: 576px) {
  .tutor-filter-top-sidebar .widget {
    width: 33.33%;
  }
}

@media (min-width: 1200px) {
  .tutor-filter-top-sidebar .widget {
    width: 20%;
  }
}

.tutor-filter-top-sidebar .tutor-widget-course-filter {
  clear: both;
  width: 100%;
  padding: 0 0.9375rem;
}

@media (max-width: 575px) {
  .tutor-pagination-wrapper .filter-offcanvas-btn,
  .tutor-pagination-wrapper .filter-top-btn {
    margin: 15px 0 0;
  }
}

.tutor-user-public-profile .photo-area .pp-area .profile-name h3 {
  font-weight: 500;
  font-size: 30px;
  text-transform: capitalize;
}

.tutor-dashboard .tutor-frontend-dashboard-header {
  margin-top: 1.875rem;
}

#tutor-registration-from {
  margin-bottom: 1.875rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1200px) {
  #tutor-registration-from {
    margin-bottom: 80px;
  }
}

.tutor-login-wrap {
  margin-top: 1.875rem;
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .tutor-login-wrap {
    margin-top: 80px;
    margin-bottom: 80px;
  }
}

.tutor-pagination {
  border-radius: 8px;
  padding: 15px 20px;
  border-color: #EDEDED;
}

.tutor-pagination ul.tutor-pagination-numbers {
  gap: 0;
}

.tutor-pagination ul.tutor-pagination-numbers .page-numbers {
  color: var(--educrat-link-color);
  font-weight: 400 !important;
  font-size: 0.9375rem !important;
  width: 40px;
  height: 40px;
  display: ilnine-block;
  text-align: center;
  line-height: 40px;
  background: #fff;
  border-radius: 50%;
  margin: 0 2px !important;
}

.tutor-pagination ul.tutor-pagination-numbers .page-numbers:before {
  display: none !important;
}

.tutor-pagination ul.tutor-pagination-numbers .page-numbers:hover, .tutor-pagination ul.tutor-pagination-numbers .page-numbers:focus, .tutor-pagination ul.tutor-pagination-numbers .page-numbers.next, .tutor-pagination ul.tutor-pagination-numbers .page-numbers.prev, .tutor-pagination ul.tutor-pagination-numbers .page-numbers.prev:hover, .tutor-pagination ul.tutor-pagination-numbers .page-numbers.next:hover, .tutor-pagination ul.tutor-pagination-numbers .page-numbers.current {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  color: #fff;
  background: var(--educrat-theme-color);
}

/* 12. responsive */
/*
*  Responsive
*/
@media (min-width: 1200px) {
  .hidden-dots .slick-dots {
    display: none;
  }
  .btn {
    font-size: 1rem;
    padding: 14px 50px;
  }
  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.82031rem;
    border-radius: 4px;
  }
  .btn-lg {
    font-size: 17px;
    padding: 15px 50px;
  }
}

@media (min-width: 1350px) {
  .container {
    max-width: 1320px;
  }
  .row-margin-left > .elementor-container {
    overflow: hidden;
    width: calc( 1320px + ((100vw - 1320px) / 2));
    max-width: calc( 1320px + ((100vw - 1320px) / 2)) !important;
    left: calc( (100vw - 1320px) / 4);
    padding-right: calc( (100vw - 1290px) / 2);
  }
}

@media (min-width: 1350px) and (max-width: 1600px) {
  .elementor-section.elementor-section-boxed:not(.row-margin-left) > .elementor-container {
    max-width: 1320px !important;
  }
}

@media (max-width: 1199px) {
  .course-header .course-category-item {
    margin-bottom: 7px;
  }
  .course-header.v3 .apus-breadscrumb {
    margin: 0;
  }
}

@media (max-width: 991px) {
  .inner-v6 .course-header-right {
    margin-top: 20px;
  }
  .widget-courses-tabs .top-info .ms-auto {
    margin: 15px 0 0;
    display: inline-block;
  }
  .widget-courses-tabs .top-info .ms-auto .tabs-course {
    justify-content: start !important;
  }
}

@media (max-width: 767px) {
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
  .row > [class*="col-"]:not(.elementor-column) {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .nav-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }
  .nav-tabs li {
    float: none;
    display: inline-block;
    margin-bottom: 8px !important;
  }
  .apus-filter > * {
    display: block;
    width: 100%;
    float: none !important;
  }
  .apus-filter > * + * {
    margin-top: 15px;
  }
  .error-404 .top-image {
    margin-top: 1.875rem;
  }
  .error-404 .top-image img {
    max-width: 80%;
  }
  .page-404 .not-found {
    text-align: center;
  }
  .details-product .information {
    margin-top: 20px;
  }
  .course-top-wrapper .course-found {
    margin-bottom: 15px;
  }
  .tabs-course.st_gray {
    border-radius: 8px;
  }
  .course-list {
    margin-bottom: 0.9375rem;
  }
}

@media (max-width: 990px) {
  .lp-user-profile #profile-nav {
    margin: 15px 0 0;
    border: 0;
  }
}

@media (max-width: 575px) {
  .tutor-course-detail-author .course-author-infomation,
  .lp-course-detail-author .course-author-infomation {
    padding: 20px 0 10px;
    text-align: center;
  }
  .tutor-course-detail-author .author-image,
  .lp-course-detail-author .author-image {
    margin: auto;
  }
  .search-form-course .btn-search {
    width: 100%;
  }
  .widget-courses-related .course-layout-item {
    margin-bottom: 0;
  }
}

@media (max-width: 560px) {
  #learn-press-profile #profile-nav .lp-profile-nav-tabs li {
    margin: 0 2px 5px;
  }
}

@media (max-width: 479px) {
  .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > *,
  .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container, .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select, .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input {
    width: 100% !important;
    margin: 0 0 5px !important;
  }
  .woocommerce-page table.cart td.actions .coupon {
    margin: 0;
  }
  .woocommerce-page table.cart td.actions .coupon .input-text {
    padding: 5px 10px !important;
    margin: 0;
  }
  .actions .update_cart {
    width: 100%;
    margin-top: 10px !important;
  }
  .elementor-icon-list-items.elementor-inline-items .elementor-icon-list-item {
    margin-bottom: 10px;
  }
  .lp-user-profile .lp-profile-left {
    width: 100%;
    max-width: 100%;
  }
}

@media (max-width: 782px) {
  .admin-bar.header_transparent #apus-header {
    top: 46px;
  }
  .admin-bar .header-mobile {
    top: 46px;
  }
}

@media (min-width: 783px) {
  .admin-bar .header-mobile {
    top: 32px;
  }
}

@media (max-width: 600px) {
  .admin-bar .header-mobile.sticky-header {
    top: 0;
  }
}
