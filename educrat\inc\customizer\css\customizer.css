
/* ==========================================================================
   Standard Selection
   ========================================================================== */
.customize-control select,
.select2-container--default .selection .select2-selection--single {
	border: none;
	background-color: #fcfcff;
	position: relative;
	border-radius: 0;
	height: 27px;
	line-height: 27px;
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.wp-customizer .select2-container--default .selection .select2-selection--multiple {
	border: none;
	background-color: #fcfcff;
	border-radius: 0;
	height: auto;
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.wp-customizer .select2-container--default .select2-selection--multiple .select2-selection__rendered {
	width: 95%;
}

.wp-customizer .select2-container--default .select2-selection--multiple .select2-selection__clear {
	position: absolute;
	right: 0;
}

.wp-customizer .select2-container .select2-dropdown {
	z-index: 900000;
}

.customize-control select:active,
.customize-control select:focus {
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}


/* ==========================================================================
   Google Fonts Select
   ========================================================================== */
.google_fonts_select_control .google-fonts,
.google_fonts_select_control .weight-style {
	margin-bottom: 10px;
}

.image_radio_button_control * {
 	-webkit-box-sizing: border-box;
 	-moz-box-sizing: border-box;
    box-sizing: border-box;
}
.image_radio_button_control .radio-button-label {
	width: 33%;
 	float: left;
 	text-align: center;
}

.image_radio_button_control .radio-button-label label > input {
	display: none;
}

.image_radio_button_control .radio-button-label label > img {
	cursor: pointer;
	border: 3px solid #ddd;
}

.image_radio_button_control .radio-button-label label > input:checked + img {
	border: 3px solid #2885bb;
}
