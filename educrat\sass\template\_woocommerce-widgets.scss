// loading products
.widget.widget-products{
    .tab-content{
        .ajax-loading{
            background: url('#{$image-theme-path}loading-quick.gif') center 100px no-repeat #fff;
        }
    }
    .widget-title{
        padding: 0 0 10px;
        margin-bottom: 25px;
    }
    .slick-carousel-top .slick-arrow{
        top: -60px;
    }
    &.column1{
        .shop-list-small{
            margin-top: -1px;
        }
    }
}
.link-readmore{
    position:relative;
    padding:$theme-margin 0;
    &:before{
        content:'';
        background:$border-color;
        position:absolute;
        top:50%;
        left:0;
        @include size(100%,1px);
        z-index:2;
    }
    .link-inner{
        display:inline-block;
        padding:0 30px;
        background:#fff;
        position:relative;
        z-index:3;
    }
}
// banner category
.category-item{
    text-align: center;
    border:1px solid $border-color;
    @include transition(all 0.35s ease-in-out 0s);
    padding:10px;
    @media(min-width: 1200px){
        padding:50px 30px 30px;
    }
    .image-wrapper{
        margin-bottom: 10px;
        @media(min-width: 1200px){
            margin-bottom: 25px;
        }
    }
    .cat-title{
        margin:0;
        font-size: 18px;
        @media(min-width: 1200px){
            font-size: 24px;
        }
    }
    .product-nb{
        font-size: 12px;
        color: $theme-color;
        letter-spacing: 1px;
        text-transform: uppercase;
    }
    &:hover{
        border-color:$theme-color;
    }
}
/*------------------------------------*\
    Widget Price Filter
\*------------------------------------*/
.woocommerce .widget_price_filter .ui-slider .ui-slider-range{
    background:$theme-color;
}
.woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content{
    background:#ebebeb;
    height: 3px;
    margin:12px 10px;
}
.widget_price_filter {
    font-family: $font-family-sans-serif;
    .price_slider_wrapper {
        overflow: hidden;
    }
	.price_slider_amount {
        .price_label{
            font-weight: 400;
            font-size:$font-size-base;
            display: inline-block;
            text-transform: capitalize;
            float: left;
        }
	}
	.ui-slider {
		position: relative;
        text-align: left;
        .ui-slider-range {
            top: 0;
            height: 100%;
            background: #dddddd;
        }
	}
	.price_slider_wrapper .ui-widget-content {
		background: lighten(#dddddd,5%);
        height: 4px;
        margin: 5px 10px 20px;
	}
}
.woocommerce .widget_price_filter .ui-slider .ui-slider-handle{
    z-index: 2;
    position:absolute;
    @include size(15px,15px);
    @include border-radius(15px);
    cursor: pointer;
    background:$theme-color;
    top:-6px;
}
.woocommerce .widget_price_filter .price_slider_amount{
    text-align: left;
    margin-top: 22px;
    > input{
        width: 48%;
        margin-bottom: 5px;
        border:2px solid $border-color;
        &:focus{
            border-color:#000;
        }
    }
}
/*------------------------------------*\
    Product List Widget
\*------------------------------------*/
.woocommerce ul.product_list_widget{
    list-style: none;
    li{
        clear: both;
        margin:0 0 15px;
        padding: 0;
        @media(min-width: 1200px){
            margin-bottom: 20px;
        }
        @include flexbox();
        align-items: center;
        &:last-child{
            margin-bottom: 0;
        }
        img{
            width: 100%;
            margin:0;
            float: none;
        }
    }
    .star-rating{
        display: none;
    }
    .woocommerce-Price-amount {
        font-size: $font-size-base;
        font-weight: 600;
        color: $body-link;
    }
    del{
        display: none;
    }
    .product-title{
        font-size: $font-size-base;
        margin: 3px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        a{
            font-weight: inherit;
        }
    }
    .left-content{
        width: 80px;
        padding: 5px;
        border: 1px solid $border-color;
        @include border-radius($border-radius);
        flex-shrink: 0;
        a{
            display: block;
            overflow: hidden;
            @include border-radius($border-radius);
            max-height: 80px;
        }
        + .right-content{
            padding-left: 20px;
            flex-grow: 1;
        }
    }
}
/*------------------------------------*\
    Widget currency-switcher
\*------------------------------------*/
.woocommerce-currency-switcher-form{
    min-width: 100px;
    .dd-select{
        background: #fff !important;
        border:none;
        border-radius:0;
    }
    ul.dd-options{
        border:none;
        @include box-shadow(none);
        
        li{
            padding:0;
            border:none;
        }
    }
}
.widget-woocommerce-currency-switcher{
    .dd-desc{
        display: none;
    }
    a.dd-option,
    .dd-selected{
        padding:5px 10px !important;
        color:$body-color;
    }
    label{
        line-height: 100%;
        float: left;
        margin:0;
    }
    .dd-pointer{
        border:none !important;
        margin:0 !important;
        &:before{
            font-family: FontAwesome;
            position:absolute;
            line-height: 100%;
            right:0;
            bottom:-4px;
        }
        &.dd-pointer-down{
            &:before{
                content: "\f107";
            }
        }
        &.dd-pointer-up{
            &:before{
                content: "\f106"; 
            }
        }
    }
}

.apus-products-list{
    list-style: none;
    padding:0;
    margin: 0;
    .product-block{
        padding: 10px 0;
        background: #ffffff;
    }
    .media-left{
        padding: 0;
    }
    .media-body{
        padding-left: 20px;
    }

    .rating {
        display: none;
    }

    .name{
        font-family: $font-family-sans-serif;
        margin: 0;
        a{
            font-size: 16px;
            text-transform: capitalize;
        }
    }

    
    .product-block{
        &:hover{
            .name{
                a{
                    color: $theme-color;
                }
            }
        }
    }
    
    .groups-button{
        *{
            i{
                color: $body-color;
                &:hover{
                    color: $theme-color;
                }
            }
        }

        .addcart, .yith-wcwl-add-to-wishlist, .quick-view{
            display: inline-block;
            padding-right: 26px;
        }
        .addcart{
            .add-cart{
                a{
                    background: transparent;
                    padding:0;
                    .title-cart{
                        display: none;
                    }
                }
            }
        }

        .yith-wcwl-add-to-wishlist{
            vertical-align: bottom;
            .sub-title{
                display: none;
            }
            .feedback{
                display: none;
            }
        }

        .quick-view{
            padding-right: 0;
            vertical-align: middle;
            a.quickview{
                background: transparent;
                border: none;
                padding:0px;
            }
        }
    }

    .price{
        margin-bottom: 10px;
        span.woocs_price_code{
            del{
                span.woocommerce-Price-amount{
                    font-size: 20px;
                    color: #888625;
                }
            }
            ins{
                span.woocommerce-Price-amount{
                    font-size: 24px;
                    font-weight: normal;
                    color: #888625;
                }
            }
            span.woocommerce-Price-amount{
                font-size: 24px;
                font-weight: normal;
                color: #888625;
            }
        }
    }
}
.sub-categories{
    .sub-title{
        font-size: 15px;
        color: #fff;
        background: $primary;
        padding: 14px 40px;
        margin: 0;
        text-transform: uppercase;
        .icon{
            margin-right: 20px;
        }
        .pull-right{
            margin-top: 3px;
        }
    }
    > .list-square{
        padding: 15px 40px;
        background: #f5f5f5;
        > li{
            > a{
                color: $body-color;
                &:before{
                    background: $body-color;
                }
            }
            &:hover,
            &.active{
                > a{
                    color: $body-link;
                    &:before{
                        background: $body-link;
                    }
                }
            }
        }
    }
}
.list-banner-category{
    .category-wrapper{
        position:relative;
        .category-meta{
            position:absolute;
            bottom:50px;
            left: 0;
            z-index: 1;
        }
    }
    .title{
        margin: 0;
        font-size: 36px;
        letter-spacing: 0.5px;
        a:hover,a:active{
            text-decoration: underline;
        }
    }
}
.all-products{
    font-size: 36px;
    color: $body-link;
    text-align: right;
    a{
        &:hover,&:active{
            text-decoration: underline;
        }
    }
}
// widget banner product
.grid-banner-category{
    &.style1{
        .link-action{
            display:block;
            position:relative;
            &:before{
                content:'';
                position:absolute;
                top:0;
                left:0;
                @include size(100%,100%);
                background:rgba(0,0,0,0.3);
                @include opacity(0);
                @include transition(all 0.3s ease-in-out 0s);
            }
            .title{
                font-size:14px;
                text-transform:uppercase;
                margin:0;
                display:inline-block;
                font-weight:500;
                padding:10px 35px;
                background:#fff;
                letter-spacing:1px;
            }
            .info{
                text-align:center;
                top:50%;
                margin-top:-19px;
                position:absolute;
                left:0;
                width:100%;
                @include opacity(0);
                @include transition(all 0.3s ease-in-out 0s);
            }
            &:hover,&:active{
                &:before,
                .info{
                    @include opacity(1);
                }
                .info{
                    -webkit-animation: zoomInDown 0.5s linear 1; /* Safari 4.0 - 8.0 */
                    animation: zoomInDown 0.5s linear 1;
                }
            }
        }
    }
    &.style2{
        .link-action{
            display:block;
            position:relative;
            overflow:hidden;
            &:before{
                content:'';
                position:absolute;
                top:0;
                left:0;
                @include size(200%,200%);
                background:rgba(0,0,0,0.2);
                @include border-radius(0 0 100% 0);
                @include scale(0);
                transform-origin: 0 0;
                -ms-transform-origin: 0 0; /* IE 9 */
                -webkit-origin: 0 0; /* Safari 3-8 */
                @include transition(all 0.4s ease-in-out 0s);
            }
            .title{
                font-size:16px;
                text-transform:uppercase;
                margin:0;
                display:inline-block;
                font-weight:500;
                padding:10px 35px;
                background:#fff;
                letter-spacing:1px;
                border:1px solid #ebebeb;
            }
            .info{
                text-align:center;
                top:10px;
                position:absolute;
                left: 10px;
                @media(min-width:1200px){
                    top:40px;
                    left: 40px;
                }
            }
            &:hover,&:active{
                &:before{
                    @include scale(1);
                }
            }
        }
    }
}
// woocommerce
table > thead > tr > th, table > thead > tr > td, .table-bordered > thead > tr > th, .table-bordered > thead > tr > td{
    border:0;
}
table > thead > tr > th, table > thead > tr > td, table > tbody > tr > th, table > tbody > tr > td, table > tfoot > tr > th, table > tfoot > tr > td, .table-bordered > thead > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > tfoot > tr > td{
    border-bottom:0;
    border-right:0;
}
.woocommerce #content table.cart td.actions .coupon, .woocommerce table.cart td.actions .coupon, .woocommerce-page #content table.cart td.actions .coupon, .woocommerce-page table.cart td.actions .coupon{
    margin-right: 10px;
}
.woocommerce-info{
    background-color: #f4f5f7;
}
.select2-container--default .select2-selection--single{
    border:none;
}
.woocommerce form .form-row .input-checkbox{
    position:static;
    float: none;
    display: inline-block;
    margin:0 5px 0 0;
    vertical-align: inherit;
    + label{
        display: inline-block;
    }
}
//widget-categoriestabs
.widget-categoriestabs{
    .nav-tabs{
        margin: 40px 0;
        border:none;
        text-align: center;
        > li{
            margin: 0 12px;
            display: inline-block;
            float: none;
            &.active{
                > a{
                    text-decoration: underline;
                    color: #000;
                }
            }
            > a{
                text-transform: capitalize;
                font-size: 16px;
                color: #000;
                border:none !important;
                .product-count{
                    font-size: 14px;
                    color: $body-color;
                    display: inline-block;
                    vertical-align: top;
                }
            }
        }
    }
}
//woocommerce-widget-layered-nav-list
.woocommerce-widget-layered-nav{
    .view-more-list{
        font-size: 14px;
        text-decoration: underline;
        color: $success;
    }
    .woocommerce-widget-layered-nav-list{
        overflow: hidden;
        &.hideContent{
            margin-bottom: 10px;
            height: 260px;
        }
        &.showContent{
            height: auto;
            margin-bottom: 10px;
        }
    }
}
.woocommerce-widget-layered-nav-list{
    -webkit-column-count: 2; /* Chrome, Safari, Opera */
    -moz-column-count: 2; /* Firefox */
    column-count: 2;
    .woocommerce-widget-layered-nav-list__item{
        font-size: 15px;
        margin:0 0 5px;
        width:100%;
        white-space: nowrap;
        &:last-child{
            margin:0;
        }
        > a{
            color: $body-color;
            &:hover,&:active{
                color:$theme-color;
            }
            padding:1px !important;
            .swatch-color{
                display:inline-block;
                @include size(12px,12px);
                @include border-radius(50%);
                vertical-align:baseline;
                margin-right: 10px;
            }
            .swatch-label{
                display:none;
            }
        }
        &.chosen{
            > a{
                color:$theme-color;
                .swatch-color{
                    display:none;
                }
                &:before{
                    vertical-align:baseline;
                    color: $theme-color;
                    content: "\f14a";
                    font-family: 'FontAwesome';
                }
                &:hover,&:active{
                    &:before{
                        color: $danger;
                        font-family: 'FontAwesome';
                        content: "\f057";
                    }
                }
            }
        }
    }
}
.apus-price-filter,
.apus-product-sorting{
    list-style:none;
    padding:0;
    margin:0;
    li{
        margin-bottom:5px;
        &:last-child{
            margin-bottom:0;
        }
        a{
            color:$body-color;
            &:hover,&:active{
                color:$theme-color;
            }
        }
        &.current,
        &.active{
            color:$theme-color;
        }
    }
}
.widget.widget-products-tabs{
    margin-bottom: 0;
    @media(min-width: 1200px){
        .widget-title{
            font-size: 44px;
        }
    }
    .top-info{
        overflow: hidden;
        margin-bottom: 15px;
        @media(min-width: 1200px){
            margin-bottom: 35px;
        }
        -webkit-justify-content: space-between; /* Safari 6.1+ */
        justify-content: space-between;
        .nav.tabs-product.center{
            margin-bottom: 0;
        }
    }
    .widget-title{
        padding: 0 0 10px;
        margin:0;
        &:before{
            width: 2000px;
        }
        &.center{
            &:before,&:after{
                display: none;
            }
        }
    }
    .widget-content.carousel{
        margin-bottom: -40px;
        .slick-list{
            padding-bottom:40px;
        }
    }
}
.widget.widget-products-deal {
    margin:0;
    .widget-title{
        padding: 0 0 10px;
        margin-bottom: 25px;
    }
    .slick-carousel-top .slick-arrow{
        top: -60px;
    }
    .apus-countdown-dark .times > div > span{
        color: $body-link;
    }
}
// tap products loading
.tab-content{
    &.loading{
        min-height: 400px;
        position: relative;
        &:before{
            position: absolute;
            @include size(100%,100%);
            top: 0;
            left: 0;
            z-index: 99;
            content: '';
            background:url('#{$image-theme-path}loading-quick.gif') center 100px no-repeat rgba(255,255,255,0.9);
        }
    }
}
.widget.widget-tab-style_center{
    .widget-title{
        font-size: 36px;
        text-align: center;
        margin:0 0 10px;
        color: #252525;
        padding:0;
        border:none;
        &:before{
            display:none;
        }
    }
}
@keyframes pulsate {
    0% {
      @include opacity(0);
      @include scale(0.1);
    }
    50% {
      @include opacity(1);
    }
    100% {
        @include scale(1.2);
        @include opacity(0);
    }
}
@-webkit-keyframes pulsate {
    0% {
      @include opacity(0);
      @include scale(0.1);
    }
    50% {
      @include opacity(1);
    }
    100% {
        @include scale(1.2);
        @include opacity(0);
    }
}
// lookbook
.apus-lookbook{
    .mapper-pin-wrapper{
        > a{
            display:inline-block;
            @include size(16px,16px);
            @include border-radius(50%);
            background:#f43434;
            position:relative;
            &:before{
                content:'';
                @include size(40px,40px);
                background:rgba(#f43434,0.2);
                position:absolute;
                top:0;
                left:0;
                margin-top:-12px;
                margin-left:-12px;
                z-index:2;
                @include border-radius(50%);
                animation: 1s ease-out 0s normal none infinite running pulsate;
                -webkit-animation: 1s ease-out 0s normal none infinite running pulsate;
            }
        }
        .image{
            img{
                width:100%;
            }
        }
    }
    .mapper-popup{
        &:before{
            content:'';
            @include size(40px,40px);
            position:absolute;
            top:50%;
            left:100%;
            @include translateY(-50%);
        }
        &:after{
            content:'';
            position:absolute;
            top:50%;
            left:100%;
            @include translateY(-50%);
            @include size(30px,24px);
            border-width:12px 15px;
            border-style:solid;
            border-color:transparent transparent transparent #fff;
        }
    }
}
.widget.widget-recent_viewed,
.widget.upsells,
.related{
    .widget-title{
        font-size: 18px;
        margin:0 0 15px;
    }
    .slick-list{
        padding-top:4px;
    }
}
.cross-sells.products{
    margin-top:$theme-margin;
    margin-bottom: 0;
    @media(min-width: 1200px){
        margin-top: 50px;
    }
    > h2{
        margin:0 0 20px;
        font-size:22px;
        @media(min-width:992px){
            margin:0 0 30px;
        }
    }
}

.wc-block-components-price-slider{
    margin: 0;
}
.wc-block-components-price-slider__range-input{
    &::-moz-range-thumb{
        @include border-radius(50%);
        @include size(18px,18px);
        background: #fff;
        border: 2px solid $theme-color;
    }
    &::-webkit-slider-thumb{
        @include border-radius(50%);
        @include size(18px,18px);
        background: #fff;
        border: 2px solid $theme-color;
    }
    &::-ms-thumb{
        @include border-radius(50%);
        @include size(18px,18px);
        background: #fff;
        border: 2px solid $theme-color;
    }
}
.wc-block-components-price-slider__range-input-progress{
    --range-color: var(--educrat-theme-color);
    height: 6px;
}
.wc-block-components-price-slider__range-input-wrapper{
    background: #E5F0FD;
    height: 6px;
    @include box-shadow(none);
    @include border-radius(6px);
}
.wc-block-price-filter__controls input{
    border:0;
    font-weight: 500;
    color: $body-link;
    padding: 0;
    + input{
        text-align: right;
    }
}
.wc-block-price-filter__range-text{
    span{
        color: $body-link;
        font-weight: 500;
    }
}