/*------------------------------------------------------------------
[Table of contents]
1. form
2. utilities
3. theme effect
4. buttons
5. alerts
6. woocommerce
7. woocommerce widgets
-------------------------------------------------------------------*/
/**
* Web Application Prefix Apply For Making Owner Styles
*/
/**
 *   Blocks Layout Selectors
 */
/********* LAYOUT **************/
/* carousel-controls-v1 */
/* carousel-controls-v2 */
/* carousel-controls-v3 */
@keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(-360deg);
    -ms-transform: rotate(-360deg);
    -o-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}

@-webkit-keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(-360deg);
    -ms-transform: rotate(-360deg);
    -o-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}

@-webkit-keyframes fl-x {
  0% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
}

@keyframes fl-x {
  0% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
}

@-webkit-keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

@keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

.animate-fl-x {
  -webkit-animation-name: fl-x;
  animation-name: fl-x;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.animate-fl-y {
  -webkit-animation-name: fl-y;
  animation-name: fl-y;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

/* 1. form */
.btn {
  border-width: 2px;
  white-space: nowrap;
}

.btn-action-icon {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 1rem;
  text-align: center;
  color: var(--educrat-text-color);
  background: #F4F4F4;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.btn-action-icon:hover, .btn-action-icon:focus {
  color: #fff;
  background: var(--educrat-theme-color);
}

.btn-action-icon.rejec:hover, .btn-action-icon.rejec:focus {
  color: #fff;
  background: #ff9b20;
}

.btn-action-icon.rejec.rejected {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.btn-action-icon.download:hover, .btn-action-icon.download:focus {
  color: #fff;
  background: #222;
}

.btn-action-icon[class*="remove"]:hover, .btn-action-icon[class*="remove"]:focus {
  color: #fff;
  background: #f33066;
}

.btn-action-icon:before {
  line-height: 30px;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  line-height: 35px;
  opacity: 0;
  filter: alpha(opacity=0);
  color: var(--educrat-link-color);
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.btn-action-icon.loading {
  background: rgba(255, 255, 255, 0.8) !important;
  color: transparent !important;
}

.btn-action-icon.loading:before {
  opacity: 0.8;
  filter: alpha(opacity=80);
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.list-action [class*="btn"] i {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  background-color: #f4f4f4;
  color: #717171;
  font-size: 1.0625rem;
  height: 45px;
  line-height: 45px;
  width: 45px;
  text-align: center;
  border-radius: 50%;
  margin-left: .5rem;
}

.list-action [class*="btn"]:hover i {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.list-action [class*="added"] i {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.list-action [class*="added"]:hover i:before {
  content: "\e646";
  font-family: 'themify';
  font-weight: 400;
}

.list-action [class*="btn"].loading i:before {
  display: inline-block;
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

@media (min-width: 1200px) {
  .list-action .review {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
    min-width: 200px;
    text-align: center;
  }
}

.list-action > * {
  display: inline-block;
  margin-left: 0.625rem;
}

@media (min-width: 1200px) {
  .list-action > * {
    margin-left: 1.875rem;
  }
}

.list-action > *:last-child {
  margin-left: 0 !important;
}

.view_all {
  font-weight: 500;
  display: inline-block;
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.view_all i {
  display: inline-block;
  margin-right: 10px;
}

.view_all:hover {
  -webkit-animation: slideIcon 0.6s linear 0s 1 normal;
  /* Safari 4.0 - 8.0 */
  animation: slideIcon 0.6s linear 0s 1 normal;
}

.pre {
  margin-left: 0.4rem;
}

@media (min-width: 1200px) {
  .pre {
    margin-left: 0.625rem;
  }
}

.next {
  margin-right: 0.4rem;
}

@media (min-width: 1200px) {
  .next {
    margin-right: 0.625rem;
  }
}

.btn-candidate-alert:before,
.btn-job-alert:before {
  content: "\f142";
  font-family: "Flaticon";
  margin-left: 10px;
  font-size: 25px;
  line-height: 0;
  vertical-align: sub;
  display: inline-block;
}

.btn-outline.btn-primary {
  background: transparent;
  border-color: #0d6efd;
  color: #0d6efd;
}

.btn-outline.btn-primary:hover {
  color: #fff;
  border-color: #0d6efd;
  background: #0d6efd;
}

.btn-outline.btn-success {
  background: transparent;
  border-color: #27b737;
  color: #27b737;
}

.btn-outline.btn-success:hover {
  color: #FFFFFF;
  border-color: #27b737;
  background: #27b737;
}

.btn-outline.btn-info {
  background: transparent;
  border-color: #0dcaf0;
  color: #0dcaf0;
}

.btn-outline.btn-info:hover {
  color: #FFFFFF;
  border-color: #0dcaf0;
  background: #0dcaf0;
}

.btn-outline.btn-danger {
  background: transparent;
  border-color: #f33066;
  color: #f33066;
}

.btn-outline.btn-danger:hover {
  color: #FFFFFF;
  border-color: #f33066;
  background: #f33066;
}

.btn-outline.btn-warning {
  background: transparent;
  border-color: #ff9b20;
  color: #ff9b20;
}

.btn-outline.btn-warning:hover {
  color: #FFFFFF;
  border-color: #ff9b20;
  background: #ff9b20;
}

.btn-inverse.btn-primary:hover {
  color: #0d6efd;
  background: #FFFFFF;
}

.btn-inverse.btn-success:hover {
  color: #27b737;
  background: #FFFFFF;
}

.btn-inverse.btn-info:hover {
  color: #0dcaf0;
  background: #FFFFFF;
}

.btn-inverse.btn-danger:hover {
  color: #f33066;
  background: #FFFFFF;
}

.btn-inverse.btn-warning:hover {
  color: #ff9b20;
  background: #FFFFFF;
}

.btn-inverse.btn-theme:hover {
  color: var(--educrat-theme-color);
  background: #FFFFFF;
}

.view-more-btn i {
  margin-right: 12px;
}

.reamore {
  font-size: 14px;
  font-weight: 500;
  color: var(--educrat-theme-color) !important;
  text-transform: uppercase;
  padding: 0 0 4px;
  border-bottom: 2px solid var(--educrat-theme-color);
}

.reamore i {
  margin-right: 8px;
}

.btn-browse {
  text-transform: uppercase;
  font-size: 12px;
  padding: 10px 15px;
  border: 1px solid #eaeff5;
  border-radius: 50px;
  line-height: 1.42857143;
}

.btn-browse:hover, .btn-browse:focus {
  background: var(--educrat-theme-color);
  color: #fff;
  border-color: var(--educrat-theme-color);
}

.apus-loadmore-btn {
  display: inline-block;
  padding: 10px 30px;
  border: 1px solid #24324A;
  text-transform: capitalize;
  font-weight: 600;
  color: #24324A;
  background-color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 8px;
  position: relative;
}

@media (min-width: 1200px) {
  .apus-loadmore-btn {
    padding: 10px 40px;
  }
}

.apus-loadmore-btn:hover, .apus-loadmore-btn:focus {
  color: #fff;
  border-color: #24324A;
  background-color: #24324A;
}

.apus-loadmore-btn.loading {
  border-color: transparent !important;
  background-color: transparent !important;
  color: transparent !important;
}

.apus-loadmore-btn.loading:after {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  background-position: center center;
  background-repeat: no-repeat;
  content: "";
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  right: 0;
}

.viewmore-products-btn {
  position: relative;
}

.viewmore-products-btn:before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  z-index: 2;
  opacity: 0;
  filter: alpha(opacity=0);
  background: rgba(255, 255, 255, 0.9) url(../images/loading-quick.gif) no-repeat scroll center center/20px auto;
}

.viewmore-products-btn.loading:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

button:focus,
.btn:focus {
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.radius-0 {
  border-radius: 0 !important;
}

.radius-circle {
  border-radius: 100px !important;
}

.read-more {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--educrat-theme-color);
}

.btn-theme.btn-white {
  background: #fff;
  color: var(--educrat-theme-color) !important;
  border-color: #fff;
}

.btn-theme.btn-white:active, .btn-theme.btn-white:hover {
  background-color: var(--educrat-theme-color);
  color: #fff !important;
  border-color: var(--educrat-theme-color);
}

.btn-purple {
  background: #bc7cbf;
  color: #fff;
  border-color: #bc7cbf;
}

.btn-purple:active, .btn-purple:hover {
  color: #fff;
  background: #b36bb7;
  border-color: #b36bb7;
}

.btn-orange {
  background: #E8543E;
  color: #fff;
  border-color: #E8543E;
}

.btn-orange:active, .btn-orange:hover {
  color: #fff;
  background: #e54027;
  border-color: #e54027;
}

.btn-brown {
  background: transparent;
  color: #c0c0c0;
  border-color: #4e4f4f;
}

.btn-brown:focus, .btn-brown:hover {
  color: #fff;
  background: transparent;
  border-color: #fff;
}

.btn-back {
  padding: 8px 15px;
  border-radius: 2px;
  background: rgba(255, 58, 114, 0.1);
  color: #ff7c39;
  border-color: #ff7c39;
}

.btn-back:focus, .btn-back:hover {
  color: #fff;
  background: #ff3a72;
  border-color: #ff7c39;
}

.btn-white.btn-br-white {
  background: #fff;
  color: var(--educrat-link-color);
  border-color: #fff;
}

.btn-white.btn-br-white:active, .btn-white.btn-br-white:hover {
  color: var(--educrat-link-color);
  background: #d9d9d9;
  border-color: #d9d9d9;
}

.btn.btn-readmore {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  color: var(--educrat-theme-hover-color);
  font-size: 15px;
  padding: 6px 20px;
  border: 0;
  background: rgba(100, 64, 251, 0.07);
  color: var(--educrat-theme-color);
}

@media (min-width: 1200px) {
  .btn.btn-readmore {
    padding: 11px 30px;
  }
}

.btn.btn-readmore:hover, .btn.btn-readmore:focus {
  background: var(--educrat-theme-color);
  color: #fff;
}

.btn-lighten {
  border-color: #fff;
  color: #fff;
  background: transparent;
}

.btn-lighten:hover {
  color: #fff;
  background: transparent;
  border-color: #fff;
}

.btn-outline.btn-white {
  background: transparent;
  color: #fff;
  border-color: #fff;
}

.btn-outline.btn-white:active, .btn-outline.btn-white:hover {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.btn-primary.btn-inverse:active, .btn-primary.btn-inverse:hover {
  background: #fff !important;
  color: #0d6efd !important;
  border-color: #0d6efd !important;
}

.btn-theme {
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.btn-theme:active, .btn-theme:hover {
  background: var(--educrat-theme-hover-color);
  border-color: var(--educrat-theme-hover-color);
  color: #fff;
}

.btn-theme-rgb7 {
  background: var(--educrat-theme-color-007);
  color: var(--educrat-theme-color);
}

.btn-theme-rgb7:active, .btn-theme-rgb7:hover {
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.btn-theme.btn-outline {
  color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  background: transparent;
}

.btn-theme.btn-outline:hover, .btn-theme.btn-outline:active {
  color: #fff;
  background: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
}

.btn-outline.btn-green {
  color: #00FF84;
  border-color: #00FF84;
  background: transparent;
}

.btn-outline.btn-green:hover, .btn-outline.btn-green:active {
  color: #fff;
  background: #00FF84;
  border-color: #00FF84;
}

/* Search
------------------------------------------------*/
.search-popup .dropdown-menu {
  padding: 10px;
}

.searchform .input-search {
  padding: 15px;
  border-left: 0;
  line-height: 1.5;
}

.searchform .btn-search {
  vertical-align: top;
  color: #adafac;
  padding: 12px 0.6rem;
}

.searchform .input-group-btn {
  line-height: 100%;
}

.search-category .btn {
  margin-right: 10px !important;
  border-radius: 4px !important;
}

.search-category .wpo-search-inner label.form-control {
  border: none;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}

.search-category select {
  border: none;
  text-transform: capitalize;
  font-weight: 500;
}

/* comment form
------------------------------------------------*/
.chosen-container {
  width: 100% !important;
}

.input-group-form {
  border-radius: 3px;
  background: transparent;
  margin: 0 0 5px 0;
}

.input-group-form .form-control-reversed {
  border: 0px;
  background: #e5e5e5;
  color: #cccccc;
  font-size: 14px;
  height: 34px;
}

.input-group-form .form-control-reversed:hover, .input-group-form .form-control-reversed:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.input-group-form .input-group-addon {
  border: 0;
  background: #e5e5e5;
  border-radius-right: 4px;
}

.btn-compare,
.btn-favorites {
  font-size: 21px;
  line-height: 1;
}

.btn-compare .count,
.btn-favorites .count {
  font-size: 13px;
  display: inline-block;
  font-weight: 600;
  color: #fff;
  background-color: var(--educrat-theme-color);
  border-radius: 50%;
  padding: 4px 7px;
  vertical-align: top;
  margin-top: -10px;
  margin-right: -14px;
}

.btn-underline {
  text-decoration: underline;
}

.btn-underline:hover {
  color: var(--educrat-theme-color);
}

.btn-view-all-photos {
  background-color: #fff;
}

.btn-view-all-photos i {
  display: inline-block;
  margin-left: 8px;
}

@media (max-width: 991px) {
  .btn-view-all-photos {
    padding: 5px 10px;
  }
}

.btn-view i,
.view-my-listings i {
  display: inline-block;
  margin-right: 5px;
  font-size: 10px;
}

.btn-view {
  font-size: 16px;
  font-weight: 700;
  white-space: nowrap;
}

.btn-show-filter i {
  display: inline-block;
  margin-right: 10px;
  line-height: 1;
  vertical-align: middle;
  font-size: 16px;
}

.btn-app {
  line-height: 1;
  color: #fff;
  background-color: #1A064F;
  padding: 7px 20px;
  border: 2px solid #1A064F;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  text-align: right;
}

@media (min-width: 1200px) {
  .btn-app {
    padding: 11px 25px;
    min-width: 200px;
  }
}

.btn-app:hover, .btn-app:focus {
  color: #1A064F;
  background-color: #fff;
  border-color: #1A064F;
}

.btn-app .app-icon {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  font-size: 20px;
  line-height: 1;
  width: 20px;
}

@media (min-width: 1200px) {
  .btn-app .app-icon {
    width: 28px;
    font-size: 28px;
  }
}

.btn-app .inner {
  padding-right: 15px;
  flex: 1;
  -ms-flex: 1;
  -webkit-box-flex: 1;
}

.btn-app .name-app {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: block;
  font-size: 15px;
  font-weight: 500;
  margin-top: 7px;
}

.btn-app .sub {
  font-size: 13px;
}

.btn-app.st_white {
  background: rgba(255, 255, 255, 0.1);
}

.btn-app.st_white:hover, .btn-app.st_white:focus {
  background-color: #fff;
  border-color: #fff;
}

.btn-app.st_normal {
  background: #EEF2F6;
  border-color: #EEF2F6;
  border-radius: 60px;
  color: var(--educrat-text-color);
}

.btn-app.st_normal .app-icon,
.btn-app.st_normal .name-app {
  color: var(--educrat-link-color);
}

.btn-app.st_normal:hover, .btn-app.st_normal:focus {
  background: var(--educrat-link-color);
  border-color: var(--educrat-link-color);
}

.btn-app.st_normal:hover .app-icon, .btn-app.st_normal:hover,
.btn-app.st_normal:hover .name-app, .btn-app.st_normal:focus .app-icon, .btn-app.st_normal:focus,
.btn-app.st_normal:focus .name-app {
  color: #fff;
}

.btn-light-theme {
  border: 0;
  text-transform: uppercase;
  background-color: var(--educrat-theme-color-010);
  color: var(--educrat-theme-color);
  padding: 11px 35px;
}

.btn-light-theme:hover, .btn-light-theme:focus {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.filter {
  padding: 8px 20px;
  border: 0;
  background-color: #F4F4F4;
  color: var(--educrat-text-color);
}

@media (min-width: 1200px) {
  .filter {
    padding: 8px 1.875rem;
  }
}

.filter i {
  margin-left: 10px;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.filter:hover, .filter:focus {
  color: #fff;
  background-color: var(--educrat-theme-color);
}

.save-search-btn,
.reset-search-btn {
  white-space: nowrap;
}

.save-search-btn i,
.reset-search-btn i {
  display: inline-block;
  margin-left: 5px;
  vertical-align: middle;
}

.mobile-menu-icon {
  display: inline-block;
  position: relative;
  width: 25px;
  height: 12px;
  line-height: 1;
  border-top: 2px solid #fff;
}

.mobile-menu-icon:after {
  content: '';
  position: absolute;
  background-color: #fff;
  bottom: 0;
  left: 0;
  width: 15px;
  height: 2px;
}

.icon-vertical {
  display: inline-block;
  width: 20px;
  height: 2px;
  margin: 8px 0;
  background: #00FF84;
  position: relative;
}

.icon-vertical:before {
  content: '';
  position: absolute;
  top: -8px;
  right: 5px;
  width: 15px;
  height: 2px;
  background: #00FF84;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.icon-vertical:after {
  content: '';
  position: absolute;
  bottom: -8px;
  right: 0;
  width: 15px;
  height: 2px;
  background: #00FF84;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.action-vertical {
  display: inline-block;
  line-height: 1;
  cursor: pointer;
}

.action-vertical:hover .icon-vertical:before, .action-vertical:focus .icon-vertical:before {
  right: 0;
  width: 20px;
}

.action-vertical:hover .icon-vertical:after, .action-vertical:focus .icon-vertical:after {
  width: 20px;
}

.btn.btn-more {
  font-size: 0.9375rem;
  padding: 11px 30px;
  border: 0;
}

/* 2. utilities */
@keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(-360deg);
    -ms-transform: rotate(-360deg);
    -o-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}

@-webkit-keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(-360deg);
    -ms-transform: rotate(-360deg);
    -o-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}

@-webkit-keyframes fl-x {
  0% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
}

@keyframes fl-x {
  0% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
}

@-webkit-keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

@keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

.animate-fl-x {
  -webkit-animation-name: fl-x;
  animation-name: fl-x;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.animate-fl-y {
  -webkit-animation-name: fl-y;
  animation-name: fl-y;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

/* 3. theme effect */
.effect-1 {
  position: relative;
}

.effect-1:after {
  content: '';
  display: block;
  width: 0px;
  height: 1px;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  right: 0;
  bottom: 0;
  left: 0;
  background: transparent;
  margin: auto;
}

.effect-1:hover:after {
  width: 100%;
  height: 1px;
  background: var(--educrat-theme-color);
}

.zoom-2 {
  overflow: hidden;
  display: block;
  border-radius: 3px;
}

.zoom-2 img {
  position: relative;
  width: 100%;
  height: auto;
  -webkit-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.zoom-2:hover img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.close .fa {
  -webkit-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}

.close:hover .fa {
  -webkit-transform: rotate(-360deg);
  -ms-transform: rotate(-360deg);
  -o-transform: rotate(-360deg);
  transform: rotate(-360deg);
}

.image-overlay-1:after, .image-overlay-1:before {
  content: "";
  display: block;
  position: absolute;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  right: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.image-overlay-1:after {
  top: -100%;
}

.image-overlay-1:before {
  bottom: -100%;
}

.image-overlay-1:hover:after {
  top: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

.image-overlay-1:hover:before {
  bottom: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

/* 6. woocommerce */
/*-------------------------------------------
    Style for woocommerce
-------------------------------------------*/
.woocommerce div.product .stock {
  color: var(--educrat-text-color);
}

.woocommerce div.product .stock:before {
  display: inline-block;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 18px;
  border-radius: 50%;
  color: #fff;
  font-size: 8px;
  background-color: var(--educrat-theme-color);
  margin-left: 5px;
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  vertical-align: middle;
}

.woo-pay-perfect {
  font-size: 16px;
  font-weight: 600;
  margin-top: 1.875rem;
}

.woo-pay-perfect p:last-child {
  margin-bottom: 0;
}

.update_cart {
  opacity: 1 !important;
  filter: alpha(opacity=100) !important;
}

.woocommerce-shipping-destination {
  color: var(--educrat-link-color);
}

.woocommerce-shipping-destination strong {
  font-weight: 400;
}

.woocommerce-shipping-methods {
  font-size: 0.9375rem;
}

.woocommerce-shipping-methods label {
  color: var(--educrat-link-color);
  font-weight: 400 !important;
}

.woocommerce form .form-row-first, .woocommerce form .form-row-last,
.woocommerce-page form .form-row-first, .woocommerce-page form .form-row-last {
  width: calc(50% - 15px);
}

.woocommerce form .form-row-last,
.woocommerce-page form .form-row-last {
  float: left;
  margin-right: 30px;
}

.woocommerce form.checkout_coupon, .woocommerce form.login, .woocommerce form.register {
  background-color: #fff;
  border: 1px solid #EDEDED;
  border-radius: 8px;
}

.woocommerce a.added_to_cart,
.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled],
.woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt,
.woocommerce #respond input#submit, .woocommerce input.button, .woocommerce button.button, .woocommerce a.button {
  line-height: 1.8;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  border-radius: 8px;
  white-space: nowrap;
  font-size: 15px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border: 2px solid var(--educrat-theme-color);
  padding: 0.6rem 1.875rem !important;
  background: var(--educrat-theme-color);
  color: #fff;
  text-transform: capitalize;
}

.woocommerce a.added_to_cart:hover, .woocommerce a.added_to_cart:focus,
.woocommerce input.button:disabled:hover,
.woocommerce input.button:disabled:focus, .woocommerce input.button:disabled[disabled]:hover, .woocommerce input.button:disabled[disabled]:focus,
.woocommerce #respond input#submit.alt:hover,
.woocommerce #respond input#submit.alt:focus, .woocommerce a.button.alt:hover, .woocommerce a.button.alt:focus, .woocommerce button.button.alt:hover, .woocommerce button.button.alt:focus, .woocommerce input.button.alt:hover, .woocommerce input.button.alt:focus,
.woocommerce #respond input#submit:hover,
.woocommerce #respond input#submit:focus, .woocommerce input.button:hover, .woocommerce input.button:focus, .woocommerce button.button:hover, .woocommerce button.button:focus, .woocommerce a.button:hover, .woocommerce a.button:focus {
  background: #fff;
  border-color: var(--educrat-theme-color);
  color: var(--educrat-theme-color);
}

.woocommerce ul.order_details {
  margin: 0 0 2em;
}

.woocommerce #review_form #respond .form-submit {
  margin: 0;
}

.woocommerce #review_form #respond #commentform {
  margin: 0;
}

.woocommerce #review_form #respond .comment-form-rating {
  margin: 0 0 1rem;
}

.woocommerce #review_form {
  margin: 0;
}

.pp_gallery ul {
  height: auto;
}

.pp_gallery ul a {
  height: auto;
}

.woocommerce table.shop_attributes {
  border: 0;
  margin: 0;
}

.woocommerce table.shop_attributes th {
  font-weight: 500;
  color: var(--educrat-link-color);
  width: 25%;
  background-color: transparent !important;
  border-bottom: 1px solid #EDEDED;
  border-width: 0 0 1px;
  padding: 12px 0;
}

.woocommerce table.shop_attributes td {
  padding: 12px 0;
  background-color: transparent !important;
  border-bottom: 1px solid #EDEDED;
  border-width: 0 0 1px;
  font-style: normal;
}

.woocommerce table.shop_attributes td p {
  padding: 0;
}

.woocommerce .woocommerce-customer-details address {
  border: 0;
  padding: 0;
  font-size: 15px;
  font-weight: 500;
}

.woocommerce div.product form.cart .variations select {
  height: 40px;
}

.woocommerce #respond input#submit.loading, .woocommerce a.button.loading, .woocommerce button.button.loading, .woocommerce input.button.loading {
  opacity: 1;
  filter: alpha(opacity=100);
  padding-left: 1.875rem;
}

.woocommerce #respond input#submit.loading:after, .woocommerce a.button.loading:after, .woocommerce button.button.loading:after, .woocommerce input.button.loading:after {
  top: 50%;
  left: 0;
  color: var(--educrat-theme-color);
  margin: 0;
  z-index: 9;
  width: 100%;
  height: 1rem;
  line-height: 1rem;
  margin-top: -0.5rem;
}

.woocommerce #respond input#submit.loading:before, .woocommerce a.button.loading:before, .woocommerce button.button.loading:before, .woocommerce input.button.loading:before {
  opacity: 0.9;
  filter: alpha(opacity=90);
  z-index: 8;
  position: absolute;
  top: -2px;
  right: -2px;
  background: #fff;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  content: '';
  border-radius: 8px;
}

.woocommerce div.product div.images .woocommerce-product-gallery__trigger {
  border: 1px solid var(--educrat-theme-color);
  background: var(--educrat-theme-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.woocommerce div.product div.images .woocommerce-product-gallery__trigger:hover, .woocommerce div.product div.images .woocommerce-product-gallery__trigger:active {
  background: var(--educrat-theme-hover-color);
}

.woocommerce div.product div.images .woocommerce-product-gallery__trigger:before {
  border-color: #fff;
}

.woocommerce div.product div.images .woocommerce-product-gallery__trigger:after {
  background: #fff;
}

@media (min-width: 1200px) {
  .woocommerce div.product div.images .flex-control-thumbs li {
    width: 33.33%;
  }
  .woocommerce div.product div.images .flex-control-thumbs li:nth-child(3n + 1) {
    clear: right;
  }
}

.woocommerce div.product div.images .flex-control-thumbs {
  margin-right: -10px;
  margin-left: -10px;
  margin-top: 20px;
}

.woocommerce div.product div.images .flex-control-thumbs li {
  padding-left: 10px;
  padding-right: 10px;
  margin-bottom: 20px;
}

.woocommerce div.product div.images .flex-control-thumbs li img {
  border: 1px solid #fff;
  opacity: 0.8;
  filter: alpha(opacity=80);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.woocommerce div.product div.images .flex-control-thumbs li img:hover, .woocommerce div.product div.images .flex-control-thumbs li img:active, .woocommerce div.product div.images .flex-control-thumbs li img.flex-active {
  border-color: var(--educrat-theme-color);
}

.shop-pagination .apus-pagination {
  margin: 0;
  float: right;
}

.shop-pagination .woocommerce-result-count {
  float: left;
  margin: 5px 0 0;
}

.woocommerce div.product form.cart .variations {
  margin-bottom: 0;
}

table.variations .tawcvs-swatches .swatch-color {
  opacity: 1;
  filter: alpha(opacity=100);
  width: 24px;
  height: 24px;
  line-height: 24px;
  position: relative;
  border: none;
  margin-left: 15px;
}

table.variations .tawcvs-swatches .swatch-color:before {
  display: none !important;
}

table.variations .tawcvs-swatches .swatch-color:after {
  content: '';
  border-radius: 50%;
  z-index: 2;
  position: absolute;
  top: -1px;
  right: -1px;
  width: 26px;
  height: 26px;
  border: 5px solid #fff;
}

table.variations .tawcvs-swatches .swatch-color.selected {
  -webkit-box-shadow: none;
  box-shadow: none;
}

table.variations .tawcvs-swatches .swatch-color.selected:after {
  top: 1px;
  right: 1px;
  width: 22px;
  height: 22px;
  border: 3px solid #fff;
}

table.variations .tawcvs-swatches .swatch-label {
  font-size: 12px;
  font-weight: 400;
  color: var(--educrat-text-color);
  padding: 9px;
  display: inline-block;
  line-height: 1;
  background: #f2f3f5;
  min-width: 30px;
  text-align: center;
  height: auto;
  width: auto;
  border: none !important;
  border-radius: 50%;
  margin-left: 10px;
  text-transform: uppercase;
  opacity: 1;
  filter: alpha(opacity=100);
}

table.variations .tawcvs-swatches .swatch-label.selected {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: var(--educrat-theme-color);
  color: #fff;
}

.woocommerce div.product form.cart .variations td.label {
  padding: 10px 0;
  text-align: inherit;
  display: table-cell;
  vertical-align: middle;
}

.woocommerce div.product form.cart .variations td.label label {
  margin: 0;
}

.woocommerce div.product form.cart.swatches-support .variations td.label {
  vertical-align: top;
}

.woocommerce div.product form.cart .reset_variations {
  color: #f33066;
}

.woocommerce div.product form.cart .reset_variations i {
  font-size: 12px;
  margin-left: 3px;
  color: #e23e1d;
}

.woocommerce #respond input#submit.added:after,
.woocommerce a.button.added:after,
.woocommerce button.button.added:after,
.woocommerce input.button.added:after {
  display: none;
}

.woocommerce form .form-row input.input-text, .woocommerce form .form-row textarea {
  line-height: 1.8;
  border: 1px solid #EDEDED;
  background: #fff;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  border-radius: 8px;
  height: calc(1.8em + (1.2rem + 2px));
}

.woocommerce form .form-row input.input-text:focus, .woocommerce form .form-row textarea:focus {
  border-color: #1A064F;
}

.woocommerce form .form-row textarea {
  padding: 20px;
  height: 100px;
}

@media (min-width: 1200px) {
  .woocommerce form .form-row textarea {
    height: 200px;
  }
}

.woocommerce table.wishlist_table thead th {
  padding: 10px 0;
  color: var(--educrat-link-color);
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 992px) {
  .woocommerce table.wishlist_table thead th {
    padding: 20px 0;
  }
}

.woocommerce table.wishlist_table tbody td {
  padding: 10px 0;
  text-align: inherit;
  border-width: 0 0 1px;
  border-bottom: 1px solid #EDEDED;
}

@media (min-width: 992px) {
  .woocommerce table.wishlist_table tbody td {
    padding: 20px 0;
  }
}

.woocommerce table.wishlist_table tfoot td {
  border: none;
}

.woocommerce table.wishlist_table {
  font-size: 0.9375rem;
}

.woocommerce table.wishlist_table .product-name {
  white-space: nowrap;
  padding-left: 20px;
  padding-right: 20px;
}

@media (min-width: 992px) {
  .woocommerce table.wishlist_table .product-name {
    padding-left: 50px;
    padding-right: 50px;
  }
}

.woocommerce table.wishlist_table .media-body {
  width: auto;
}

.woocommerce table.wishlist_table .product-thumbnail a {
  display: block;
  width: 80px;
}

@media (min-width: 1200px) {
  .woocommerce table.wishlist_table .product-thumbnail a {
    width: 170px;
  }
}

.track_order {
  max-width: 770px;
  margin: auto;
  padding: 15px;
  background: #f2f3f5;
}

@media (min-width: 992px) {
  .track_order {
    padding: 70px;
  }
}

.track_order .form-row {
  width: 100% !important;
}

.track_order .form-row input.input-text {
  padding: 5px 20px;
  background: #fff !important;
  height: calc(1.8em + (1.2rem + 2px));
}

.track_order .form-row:last-child {
  margin-bottom: 0;
}

.track_order .form-row label {
  color: var(--educrat-link-color);
}

.woocommerce-error,
.woocommerce-message {
  line-height: 3.4;
  background-color: #fff;
  border-color: var(--educrat-theme-color);
  -webkit-box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
}

#add_payment_method #payment ul.payment_methods, .woocommerce-cart #payment ul.payment_methods, .woocommerce-checkout #payment ul.payment_methods {
  border: 0;
  padding: 0;
}

#add_payment_method #payment ul.payment_methods li, .woocommerce-cart #payment ul.payment_methods li, .woocommerce-checkout #payment ul.payment_methods li {
  margin: 0 0 12px;
}

#add_payment_method #payment ul.payment_methods li .payment_box, .woocommerce-cart #payment ul.payment_methods li .payment_box, .woocommerce-checkout #payment ul.payment_methods li .payment_box {
  padding: 5px 28px;
  margin: 0;
  font-size: 0.9375rem;
  background: transparent;
  color: var(--educrat-text-color);
}

#add_payment_method #payment ul.payment_methods li label, .woocommerce-cart #payment ul.payment_methods li label, .woocommerce-checkout #payment ul.payment_methods li label {
  cursor: pointer;
  font-weight: 500;
  font-family: var(--educrat-heading-font);
  display: inline;
  color: var(--educrat-link-color);
}

#add_payment_method #payment ul.payment_methods li [for="payment_method_paypal"] img, .woocommerce-cart #payment ul.payment_methods li [for="payment_method_paypal"] img, .woocommerce-checkout #payment ul.payment_methods li [for="payment_method_paypal"] img {
  display: none;
}

#add_payment_method #payment ul.payment_methods li .about_paypal, .woocommerce-cart #payment ul.payment_methods li .about_paypal, .woocommerce-checkout #payment ul.payment_methods li .about_paypal {
  float: none;
  line-height: inherit;
}

#add_payment_method #payment ul.payment_methods li input, .woocommerce-cart #payment ul.payment_methods li input, .woocommerce-checkout #payment ul.payment_methods li input {
  margin-left: 10px;
}

.woocommerce table.shop_table {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  margin: 0;
}

.woocommerce table.shop_table th {
  color: inherit;
  font-size: 1rem;
  font-weight: 500;
  padding: 10px;
  color: var(--educrat-theme-color);
  border: 0;
}

@media (min-width: 1200px) {
  .woocommerce table.shop_table th {
    padding: 25px 15px;
  }
}

.woocommerce table.shop_table th:last-child {
  text-align: left;
}

.woocommerce table.shop_table td {
  border: none;
  border-bottom: 1px solid #EDEDED;
  overflow: hidden;
  padding: 10px;
  background-color: transparent !important;
}

@media (min-width: 1200px) {
  .woocommerce table.shop_table td {
    padding: 25px 15px;
  }
}

.woocommerce table.shop_table td.product-thumbnail {
  width: 100px;
}

@media (min-width: 1200px) {
  .woocommerce table.shop_table td.product-thumbnail {
    width: 140px;
  }
}

.woocommerce table.shop_table td.product-thumbnail a {
  display: block;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.woocommerce table.shop_table td:last-child {
  text-align: left;
}

@media (min-width: 1200px) {
  .woocommerce table.shop_table th:first-child, .woocommerce table.shop_table td:first-child {
    padding-right: 30px;
  }
  .woocommerce table.shop_table th:last-child, .woocommerce table.shop_table td:last-child {
    padding-left: 30px;
  }
}

.woocommerce table.shop_table thead {
  background-color: #F5F7FE;
}

.woocommerce table.shop_table .reader-text {
  display: none;
}

.woocommerce table.shop_table .quantity-wrapper > label {
  display: none;
}

.woocommerce table.shop_table .product-remove .remove {
  background-color: transparent !important;
  font-size: 1rem;
  font-weight: 400;
  padding: 12px;
  width: auto;
  height: auto;
  margin: 0;
  color: #f33066;
}

.woocommerce table.shop_table .product-remove .remove:hover, .woocommerce table.shop_table .product-remove .remove:active {
  color: #f33066;
}

.woocommerce table.shop_table tbody tr:last-child {
  background-color: transparent;
}

.woocommerce table.shop_table tbody .actions {
  padding: 1rem;
  border: 0 !important;
}

@media (min-width: 1200px) {
  .woocommerce table.shop_table tbody .actions {
    padding: 1.875rem;
  }
}

.woocommerce table.shop_table tbody .product-subtotal {
  font-weight: 500;
  font-size: 1rem;
  color: var(--educrat-link-color);
}

.woocommerce table.shop_table tbody .product-name {
  font-size: 1rem;
}

.woocommerce table.shop_table tbody .cart-subtotal .woocommerce-Price-amount,
.woocommerce table.shop_table tbody .order-total .woocommerce-Price-amount {
  color: var(--educrat-link-color);
}

.woocommerce table.shop_table .list-bundles {
  font-size: 14px;
  list-style: none;
  padding-right: 25px;
}

.woocommerce table.shop_table .list-bundles strong {
  font-weight: 500;
}

.woocommerce table.shop_table .list-bundles ul {
  list-style: inside none disc;
  padding: 0;
  margin: 0;
}

.woocommerce .cart_totals > h2 {
  font-weight: 500;
  margin: 0 0 10px;
  font-size: 20px;
}

@media (min-width: 1200px) {
  .woocommerce .cart_totals > h2 {
    margin: 0 0 20px;
  }
}

.woocommerce .cart_totals table.shop_table {
  border: none;
  margin: 0;
}

.woocommerce .cart_totals table.shop_table th, .woocommerce .cart_totals table.shop_table td {
  padding: 10px 0;
  border-top: 1px dashed #dadde8;
  border-bottom: 0;
}

@media (min-width: 1200px) {
  .woocommerce .cart_totals table.shop_table th, .woocommerce .cart_totals table.shop_table td {
    padding: 15px 0;
  }
}

.woocommerce .cart_totals table.shop_table th {
  font-weight: 500;
  font-size: 15px;
  color: var(--educrat-link-color);
}

.woocommerce .cart_totals table.shop_table strong {
  font-weight: 500;
}

.woocommerce .cart_totals table.shop_table [data-title="Total"],
.woocommerce .cart_totals table.shop_table [data-title="Subtotal"] {
  font-weight: 500;
  font-size: 1rem;
}

.woocommerce .cart_totals .wc-proceed-to-checkout {
  padding: 15px 0 0;
}

.cart-collaterals {
  background: #F7F8FB;
  padding: 1.25rem;
  border-radius: 8px;
  border: 1px solid #EDEDED;
  margin-top: 20px;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .cart-collaterals {
    margin-top: 50px;
    margin-bottom: 50px;
  }
}

@media (min-width: 1200px) {
  .cart-collaterals {
    padding: 1.875rem;
    margin-top: 80px;
    margin-bottom: 80px;
  }
}

#add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment {
  background: transparent;
}

#add_payment_method #payment .place-order, .woocommerce-cart #payment .place-order, .woocommerce-checkout #payment .place-order {
  padding: 10px 0 0 !important;
  margin-bottom: 0;
}

#add_payment_method #payment .place-order #place_order, .woocommerce-cart #payment .place-order #place_order, .woocommerce-checkout #payment .place-order #place_order {
  width: 100%;
}

.woocommerce-checkout #payment {
  border-radius: 0;
  padding-top: 20px;
  border-top: 1px solid #EDEDED;
}

#add_payment_method #payment div.payment_box::before, .woocommerce-cart #payment div.payment_box::before, .woocommerce-checkout #payment div.payment_box::before {
  display: none;
}

.woocommerce #customer_details h3.form-row {
  font-size: 18px;
  font-weight: 400;
  text-transform: capitalize;
  margin: 0;
  padding: 20px 0;
}

.woocommerce form .woocommerce-billing-fields > h3,
.woocommerce form .woocommerce-shipping-fields > h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .woocommerce form .woocommerce-billing-fields > h3,
  .woocommerce form .woocommerce-shipping-fields > h3 {
    font-size: 20px;
  }
}

.woocommerce form .woocommerce-billing-fields > h3 input[type="checkbox"],
.woocommerce form .woocommerce-shipping-fields > h3 input[type="checkbox"] {
  position: static;
  margin: -3px 0 0 5px;
  vertical-align: middle;
}

.woocommerce form .woocommerce-billing-fields .select2-container,
.woocommerce form .woocommerce-shipping-fields .select2-container {
  height: calc(1.8em + (1.2rem + 2px));
}

.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > select,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > input {
  overflow: hidden;
  width: calc(100% - 200px) !important;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: #EDEDED;
  padding: 10px 0;
  border-radius: 0 !important;
  float: left;
}

.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container:focus,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select:focus,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input:focus,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container:focus,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > select:focus,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > input:focus {
  border-color: var(--educrat-theme-color);
}

.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-hidden-accessible,
.woocommerce form .woocommerce-shipping-fields .woocommerce-billing-fields__field-wrapper > * > .select2-hidden-accessible {
  height: 0;
}

.woocommerce form .woocommerce-shipping-fields {
  margin-top: 1rem;
}

@media (min-width: 1200px) {
  .woocommerce form .woocommerce-shipping-fields {
    margin-top: 2rem;
  }
}

.woocommerce form .woocommerce-shipping-fields > h3 {
  margin-bottom: 10px;
}

form.woocommerce-checkout {
  margin-bottom: 1.875rem;
}

.woocommerce form .form-row label,
.woocommerce-page form .form-row label {
  margin-bottom: 5px;
}

.woocommerce .cart-collaterals .cross-sells, .woocommerce-page .cart-collaterals .cross-sells,
.woocommerce .cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals {
  width: 100%;
}

.woocommerce div.product .product_title {
  font-size: 25px;
  margin: 0 0 5px;
}

@media (min-width: 1200px) {
  .woocommerce div.product .product_title {
    font-size: 30px;
  }
}

.woocommerce div.product p.price ins, .woocommerce div.product p.price, .woocommerce div.product span.price ins, .woocommerce div.product span.price {
  color: var(--educrat-theme-color);
  font-size: 1rem;
  font-weight: 500;
}

.woocommerce div.product p.price del, .woocommerce div.product span.price del {
  font-size: 14px;
  color: var(--educrat-text-color);
}

.woocommerce div.product p.price del, .woocommerce div.product span.price del {
  opacity: 1;
  filter: alpha(opacity=100);
}

.variations label {
  color: var(--educrat-text-color);
  font-size: 15px;
  text-transform: capitalize;
  font-weight: 400 !important;
  padding-left: 5px;
}

.variations .value {
  padding: 0;
}

.woocommerce div.product form.cart .group_table {
  border: none;
  margin-bottom: 10px;
}

.woocommerce div.product form.cart .group_table .price del {
  font-size: 12px !important;
}

.woocommerce div.product form.cart .group_table .price,
.woocommerce div.product form.cart .group_table .price ins {
  font-size: 15px !important;
  color: var(--educrat-theme-color);
}

.woocommerce div.product form.cart .group_table label {
  font-weight: 500;
}

.woocommerce div.product form.cart .group_table td {
  vertical-align: middle;
}

.woocommerce div.product form.cart .group_table td:first-child {
  padding-left: 0;
  text-align: right;
}

.woocommerce div.product form.cart .group_table .quantity .reader-text {
  display: none;
}

.woocommerce div.product form.cart.group_product {
  width: 100%;
}

.woocommerce div.product form.cart .group_table .label {
  padding: 0.5em;
  vertical-align: middle;
  font-size: 14px;
  display: table-cell;
  text-align: inherit;
  white-space: normal;
}

.woocommerce div.product form.cart .group_table .label label {
  font-weight: 400;
}

.woocommerce div.product form.cart .variations td {
  line-height: inherit;
  font-size: inherit;
  padding: 10px 0;
  vertical-align: middle;
}

.woocommerce div.product form.cart .variations td .tawcvs-swatches {
  padding: 0;
}

.woocommerce .order_details {
  padding: 0;
}

.woocommerce #content table.cart td.actions .coupon,
.woocommerce table.cart td.actions .coupon,
.woocommerce-page #content table.cart td.actions .coupon,
.woocommerce-page table.cart td.actions .coupon {
  border: 1px solid #EDEDED;
  border-radius: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.woocommerce #content table.cart td.actions .coupon .input-text,
.woocommerce table.cart td.actions .coupon .input-text,
.woocommerce-page #content table.cart td.actions .coupon .input-text,
.woocommerce-page table.cart td.actions .coupon .input-text {
  height: calc(1.8em + (1.2rem + 2px));
  padding: 5px 20px !important;
  border: 0;
  width: auto;
  margin-left: 15px;
}

.woocommerce #content table.cart td.actions .coupon .input-text::-webkit-input-placeholder,
.woocommerce table.cart td.actions .coupon .input-text::-webkit-input-placeholder,
.woocommerce-page #content table.cart td.actions .coupon .input-text::-webkit-input-placeholder,
.woocommerce-page table.cart td.actions .coupon .input-text::-webkit-input-placeholder {
  /* Edge */
  opacity: 1;
  filter: alpha(opacity=100);
}

.woocommerce #content table.cart td.actions .coupon .input-text:-ms-input-placeholder,
.woocommerce table.cart td.actions .coupon .input-text:-ms-input-placeholder,
.woocommerce-page #content table.cart td.actions .coupon .input-text:-ms-input-placeholder,
.woocommerce-page table.cart td.actions .coupon .input-text:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  opacity: 1;
  filter: alpha(opacity=100);
}

.woocommerce #content table.cart td.actions .coupon .input-text::placeholder,
.woocommerce table.cart td.actions .coupon .input-text::placeholder,
.woocommerce-page #content table.cart td.actions .coupon .input-text::placeholder,
.woocommerce-page table.cart td.actions .coupon .input-text::placeholder {
  opacity: 1;
  filter: alpha(opacity=100);
}

.woocommerce #content table.cart td.actions .coupon .btn,
.woocommerce table.cart td.actions .coupon .btn,
.woocommerce-page #content table.cart td.actions .coupon .btn,
.woocommerce-page table.cart td.actions .coupon .btn {
  font-size: 14px;
  color: var(--educrat-theme-color);
  padding: 10px 20px;
}

.woocommerce #content table.cart td.actions .coupon .btn:hover, .woocommerce #content table.cart td.actions .coupon .btn:focus,
.woocommerce table.cart td.actions .coupon .btn:hover,
.woocommerce table.cart td.actions .coupon .btn:focus,
.woocommerce-page #content table.cart td.actions .coupon .btn:hover,
.woocommerce-page #content table.cart td.actions .coupon .btn:focus,
.woocommerce-page table.cart td.actions .coupon .btn:hover,
.woocommerce-page table.cart td.actions .coupon .btn:focus {
  color: var(--educrat-theme-hover-color);
}

#add_payment_method table.cart img, .woocommerce-cart table.cart img, .woocommerce-checkout table.cart img {
  width: 90px;
  border-radius: 8px;
}

.woocommerce .percent-sale,
.woocommerce span.onsale {
  color: #fff;
  font-size: 14px;
  background: #ff5a5f;
  padding: 0 20px;
  position: absolute;
  text-align: center;
  right: 10px;
  text-transform: capitalize;
  top: 10px;
  min-height: auto;
  z-index: 1;
  border-radius: 3px;
  display: inline-block;
  line-height: 2;
  font-weight: 400;
}

.popup-cart .title-count,
.popup-cart .title-add {
  font-size: 20px;
  margin: 0 0 20px;
}

.popup-cart .gr-buttons {
  margin: 50px 0 0;
}

.popup-cart .title-add {
  color: #27b737;
}

.popup-cart .image img {
  max-width: 100px;
}

.popup-cart .name {
  margin: 30px 0 0;
}

.popup-cart .widget-product {
  margin-top: 30px;
}

#apus-cart-modal .btn-close {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  background: #fff;
  width: 30px;
  height: 30px;
  line-height: 26px;
  text-align: center;
  display: inline-block;
}

#apus-cart-modal .modal-content {
  background: #ffffff none repeat scroll 100% 0;
  min-width: 1000px;
  max-width: 100%;
  margin-top: 50px;
}

#apus-cart-modal .modal-body {
  padding: 60px;
}

.name {
  font-weight: 500;
  font-size: 1rem;
  margin: 0 0 5px;
}

.product-block {
  position: relative;
  margin-bottom: 1.25rem;
}

@media (min-width: 1200px) {
  .product-block {
    margin-bottom: 1.875rem;
  }
}

.product-block .sale-perc {
  background: #fd5f5c;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  padding: 0 5px;
  line-height: 1.7;
  position: absolute;
  right: 12px;
  text-transform: uppercase;
  top: 12px;
  z-index: 8;
}

.product-block .out-of-stock {
  background: #d4d4d4;
  color: #fff !important;
  font-size: 14px !important;
  font-weight: 400;
  padding: 0 8px;
  position: absolute;
  left: 12px;
  text-transform: uppercase;
  font-family: var(--educrat-main-font);
  top: 12px;
  z-index: 8;
}

.product-block .image {
  position: relative;
  margin: 0;
}

.product-block .image .downsale {
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 8;
  padding: 2px 10px;
  border-radius: 2px;
  background: #d42e2e;
  color: #fff;
}

.product-block .image img {
  display: inline-block;
  -webkit-transition: all 0.5s ease-in-out 0s;
  -o-transition: all 0.5s ease-in-out 0s;
  transition: all 0.5s ease-in-out 0s;
}

.product-block .image .image-effect {
  top: 0;
  position: absolute;
  right: 50%;
  -webkit-transform: translateX(50%);
  -ms-transform: translateX(50%);
  -o-transform: translateX(50%);
  transform: translateX(50%);
  z-index: 2;
  opacity: 0;
  filter: alpha(opacity=0);
}

.product-block .image .image-no-effect {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.product-block .block-inner {
  overflow: hidden;
  border-radius: 8px;
}

.product-block .block-inner:hover .image .image-hover {
  opacity: 0;
  filter: alpha(opacity=0);
}

.product-block .block-inner:hover .image .image-effect {
  opacity: 1;
  filter: alpha(opacity=100);
}

.product-block .block-inner.text-center .image img {
  margin: auto;
}

.product-block .block-inner.text-center .image .image-effect {
  right: 50%;
  -webkit-transform: translateX(50%);
  -ms-transform: translateX(50%);
  -o-transform: translateX(50%);
  transform: translateX(50%);
}

.product-block .product-image {
  position: relative;
  display: block;
}

.product-block .rating {
  margin-bottom: 3px;
}

.product-block .rating .counter {
  font-size: 13px;
}

.product-block .add-cart {
  margin-top: 12px;
}

.product-block .add-cart a {
  background: #fff;
  color: var(--educrat-link-color);
  border-color: var(--educrat-theme-color);
}

@media (min-width: 1200px) {
  .product-block .add-cart a {
    min-width: 180px;
    text-align: center;
  }
}

.product-block .add-cart a.added_to_cart, .product-block .add-cart a:hover, .product-block .add-cart a:focus {
  background-color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.product-block .name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-block .metas {
  padding: 14px;
}

.product-block .categories {
  margin-bottom: 5px;
}

.product-block .categories a {
  font-size: 14px;
  color: var(--educrat-text-color);
}

.product-block .categories a:hover, .product-block .categories a:focus {
  color: var(--educrat-theme-color);
}

.product-block:hover .add-cart a {
  background-color: var(--educrat-theme-color);
  border-color: var(--educrat-theme-color);
  color: #fff;
}

.product-block-search {
  padding: 1rem;
}

.product-block-search .entry-title {
  margin: 0 0 5px;
  font-size: 1.125rem;
}

@media (min-width: 1200px) {
  .product-block-search .entry-title {
    font-size: 1.375rem;
  }
}

.product-block-search .add-cart {
  margin-top: 10px;
  position: static;
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.product-block-search .add-cart a {
  width: auto;
}

.product-block-search .entry-thumb {
  overflow: hidden;
  border-radius: 8px;
  margin: 0;
}

@media (max-width: 767px) {
  .product-block-search .col-content {
    margin-top: 10px;
  }
}

.woocommerce .woocommerce-product-rating .star-rating {
  margin: 0;
  display: inline-block;
  float: none;
  vertical-align: middle;
}

.woocommerce .woocommerce-product-rating .woocommerce-review-link {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
}

.woocommerce #content div.product div.summary, .woocommerce div.product div.summary, .woocommerce-page #content div.product div.summary, .woocommerce-page div.product div.summary,
.woocommerce #content div.product div.images, .woocommerce div.product div.images, .woocommerce-page #content div.product div.images, .woocommerce-page div.product div.images {
  width: 100%;
}

.single_variation_wrap div.qty {
  font-size: 15px;
  text-transform: uppercase;
  color: var(--educrat-text-color);
  margin-top: 10px;
  margin-left: 10px;
}

@media (min-width: 1024px) {
  .wrapper-shop {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

.wrapper-shop .apus-pagination {
  border-top: 1px solid #EDEDED;
  padding-top: 40px;
  margin-top: 0;
}

.wrapper-shop aside.sidebar {
  background: transparent;
}

.thumbnails-image ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.thumbnails-image .prev,
.thumbnails-image .next {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 18px;
  color: #000;
}

.thumbnails-image .thumb-link {
  display: block;
  opacity: 0.4;
  filter: alpha(opacity=40);
  margin: 10px 0;
}

.thumbnails-image .thumb-link:hover, .thumbnails-image .thumb-link.active {
  opacity: 1;
  filter: alpha(opacity=100);
}

@media (min-width: 1200px) {
  .details-product .left-detail {
    padding-left: 0;
  }
}

.details-product .shipping_info {
  margin-top: 15px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .details-product .shipping_info {
    margin-top: 40px;
  }
}

.details-product .shipping_info:hover {
  color: var(--educrat-text-color);
}

.details-product .shipping_info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.details-product .shipping_info ul i {
  margin-left: 5px;
}

.details-product .shipping_info ul li {
  margin-bottom: 0px;
}

@media (min-width: 1200px) {
  .details-product .shipping_info ul li {
    margin-bottom: 5px;
  }
}

.details-product .shipping_info ul li:last-child {
  margin-bottom: 0;
}

.details-product .price-rating-wrapper {
  margin-top: 10px;
  clear: both;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .details-product .price-rating-wrapper {
    margin-top: 20px;
  }
}

.details-product .price-rating-wrapper .price {
  margin-left: 15px !important;
  line-height: 1.4;
}

.details-product .price-rating-wrapper .price del {
  display: block !important;
}

.details-product .price-rating-wrapper > * {
  display: inline-block;
  vertical-align: bottom;
}

.details-product .pro-info {
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .details-product .pro-info {
    font-size: 30px;
  }
}

.details-product .popup-video {
  background: #fff;
  height: 40px;
  line-height: 40px;
  min-width: 40px;
  overflow: hidden;
  display: inline-block;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  border-radius: 50px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  /* Safari 7.0+ */
  flex-direction: row;
  -webkit-flex-direction: row;
}

.details-product .popup-video i {
  height: 40px;
  line-height: 40px;
  width: 40px;
  font-size: 13px;
  text-align: center;
  text-indent: 3px;
}

.details-product .popup-video span {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  white-space: nowrap;
  max-width: 0;
  padding: 0;
  overflow: hidden;
}

.details-product .popup-video:hover span {
  max-width: 280px;
  padding-left: 12px;
}

.details-product .product-cat {
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 12px;
}

.details-product .product-cat a {
  color: var(--educrat-theme-color);
}

.details-product div.video {
  z-index: 8;
  position: absolute;
  right: 10px;
  bottom: 10px;
}

@media (min-width: 768px) {
  .details-product div.video {
    right: 20px;
    bottom: 20px;
  }
}

.details-product .apus-countdown {
  margin-top: 5px;
}

.details-product .special-product {
  padding: 8px 0;
}

.details-product .apus-countdown .times {
  margin-bottom: 5px;
}

.details-product .apus-countdown .times > span {
  color: var(--educrat-theme-color);
  margin-bottom: 5px;
}

.details-product .apus-countdown .times > div {
  text-align: center;
  vertical-align: middle;
  min-width: 40px;
  font-size: 12px;
  display: inline-block;
  font-weight: 400;
  text-transform: uppercase;
  margin: 0 5px;
  padding: 8px;
}

.details-product .apus-countdown .times > div:first-child {
  margin-right: 0;
}

.details-product .apus-countdown .times > div span {
  font-weight: 500;
  margin-bottom: 5px;
  border-radius: 3px;
  font-size: 18px;
  display: block;
  color: var(--educrat-link-color);
}

.details-product .apus-woocommerce-product-gallery-thumbs .slick-slide:hover .thumbs-inner, .details-product .apus-woocommerce-product-gallery-thumbs .slick-slide:active .thumbs-inner, .details-product .apus-woocommerce-product-gallery-thumbs .slick-slide.slick-current .thumbs-inner {
  opacity: 1;
  filter: alpha(opacity=100);
  border-color: #1A3454;
}

.details-product .apus-woocommerce-product-gallery-thumbs .slick-slide .thumbs-inner {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  max-width: 100%;
  display: block;
  cursor: pointer;
  position: relative;
  opacity: 0.7;
  filter: alpha(opacity=70);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid transparent;
}

.details-product .apus-woocommerce-product-gallery-thumbs .slick-slide .thumbs-inner:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  border-color: #1A3454;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical {
  margin: 0;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-slide {
  padding: 0;
  margin-bottom: 10px;
  border: none;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow {
  text-align: center;
  background-color: transparent !important;
  border: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow i {
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  line-height: 30px;
  display: inline-block;
  -webkit-transition: all 0.2s ease-in-outs 0s;
  -o-transition: all 0.2s ease-in-outs 0s;
  transition: all 0.2s ease-in-outs 0s;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow:hover i, .details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow:focus i {
  color: #fff;
  background-color: var(--educrat-theme-color);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-prev {
  top: -40px;
  bottom: 100%;
  -webkit-transform: translate(0, -5px);
  -ms-transform: translate(0, -5px);
  -o-transform: translate(0, -5px);
  transform: translate(0, -5px);
  width: 100%;
  right: 0;
  font-size: 11px;
}

.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-next {
  width: 100%;
  top: 100%;
  bottom: inherit;
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
  left: 0;
  font-size: 11px;
}

.details-product .image-mains.thumbnails-bottom .wrapper-thumbs {
  margin-top: 10px;
}

.details-product .image-mains.thumbnails-bottom .slick-carousel {
  margin-right: -5px;
  margin-left: -5px;
}

.details-product .image-mains.thumbnails-bottom .slick-carousel .slick-slide {
  padding-right: 5px;
  padding-left: 5px;
}

.details-product .image-mains.thumbnails-left .apus-woocommerce-product-gallery-wrapper {
  width: calc(100% - 100px);
  float: left;
}

@media (min-width: 1200px) {
  .details-product .image-mains.thumbnails-left .apus-woocommerce-product-gallery-wrapper {
    width: calc(100% - 160px);
  }
}

.details-product .image-mains.thumbnails-left .wrapper-thumbs {
  float: right;
  width: 100px;
  padding-left: 20px;
}

@media (min-width: 1200px) {
  .details-product .image-mains.thumbnails-left .wrapper-thumbs {
    padding-left: 30px;
    width: 160px;
  }
}

@media (max-width: 767px) {
  .details-product .image-mains.thumbnails-left .apus-woocommerce-product-gallery-wrapper {
    width: calc(100% - 70px);
  }
  .details-product .image-mains.thumbnails-left .wrapper-thumbs {
    width: 70px;
    padding-left: 10px;
  }
}

.details-product .image-mains.thumbnails-right .apus-woocommerce-product-gallery-wrapper {
  width: calc(100% - 160px);
  float: right;
}

.details-product .image-mains.thumbnails-right .wrapper-thumbs {
  float: left;
  width: 160px;
  padding-right: 20px;
}

@media (min-width: 1200px) {
  .details-product .image-mains.thumbnails-right .wrapper-thumbs {
    padding-right: 30px;
  }
}

@media (max-width: 767px) {
  .details-product .image-mains.thumbnails-right .apus-woocommerce-product-gallery-wrapper {
    width: calc(100% - 70px);
  }
  .details-product .image-mains.thumbnails-right .wrapper-thumbs {
    width: 70px;
    padding-right: 10px;
  }
}

.details-product .description .title {
  font-size: 21px;
}

.details-product .apus-woocommerce-product-gallery-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.details-product .apus-woocommerce-product-gallery-wrapper .downsale {
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 9;
  padding: 2px 10px;
  border-radius: 0;
  background: #d42e2e;
  color: #fff;
}

.details-product .apus-woocommerce-product-gallery-wrapper .apus-woocommerce-product-gallery {
  margin: 0;
  overflow: hidden;
  border-radius: 4px;
}

.details-product .apus-woocommerce-product-gallery-wrapper .apus-woocommerce-product-gallery .slick-slide {
  padding: 0;
}

.details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger {
  position: absolute;
  z-index: 2;
  top: 10px;
  left: 10px;
  display: inline-block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 15px;
  text-align: center;
  border: 0;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  color: var(--educrat-link-color);
  background: #fff;
}

@media (min-width: 1200px) {
  .details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger {
    top: 20px;
    left: 20px;
    width: 45px;
    height: 45px;
    line-height: 45px;
    font-size: 21px;
  }
}

.details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger:hover, .details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger:active {
  background: var(--educrat-theme-color);
  color: #fff;
}

.details-product .apus-woocommerce-product-gallery-wrapper:hover .woocommerce-product-gallery__trigger {
  opacity: 1;
  filter: alpha(opacity=100);
}

.details-product .woocommerce-product-details__short-description.hideContent {
  overflow: hidden;
  height: 60px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.details-product .woocommerce-variation-add-to-cart {
  width: 100%;
  overflow: hidden;
}

.details-product .list li {
  margin-bottom: 10px;
}

.details-product .list i {
  color: var(--educrat-theme-color);
  margin-left: 8px;
}

.details-product .woocommerce-variation-price {
  margin-bottom: 15px;
}

.details-product .product_meta {
  overflow: hidden;
  clear: both;
}

.details-product .product_meta > span {
  display: block;
}

.details-product .product_meta .sub_title {
  display: inline-block;
  color: var(--educrat-link-color);
}

.details-product .product_meta a {
  color: var(--educrat-text-color);
}

.details-product .product_meta a:hover, .details-product .product_meta a:focus {
  color: var(--educrat-link-color);
}

@media (min-width: 992px) {
  .details-product .information {
    padding-right: 30px;
  }
}

@media (min-width: 1200px) {
  .details-product .information {
    padding-right: 90px;
  }
}

.details-product .information .summary {
  float: none !important;
  width: 100%;
  margin: 0 !important;
}

.details-product .information .single_variation_wrap {
  padding-top: 10px;
}

.details-product .information .price {
  margin: 0.4rem 0 0.625rem;
}

@media (min-width: 1200px) {
  .details-product .information .price {
    margin: 0.4rem 0 1rem;
  }
}

.details-product .information .price,
.details-product .information .price ins {
  font-size: 1.2rem !important;
  color: var(--educrat-theme-color) !important;
}

@media (min-width: 1200px) {
  .details-product .information .price,
  .details-product .information .price ins {
    font-size: 24px !important;
  }
}

.details-product .information .price del {
  font-size: 1rem !important;
}

.details-product .information .woocommerce-product-rating {
  margin-bottom: 0 !important;
}

.details-product .information .woocommerce-product-details__short-description {
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .details-product .information .woocommerce-product-details__short-description {
    margin-bottom: 35px;
  }
}

.details-product .information .woocommerce-product-details__short-description p:last-child {
  margin-bottom: 0;
}

.details-product .information .woocommerce-product-details__short-description ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.details-product .information .woocommerce-product-details__short-description ul li {
  margin-bottom: 5px;
}

.details-product .information .woocommerce-product-details__short-description ul li:last-child {
  margin-bottom: 0;
}

.details-product .information .view-more-desc {
  font-size: 14px;
  color: #b7b7b7;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.details-product .information .view-more-desc:hover {
  color: var(--educrat-link-color);
}

.details-product .information .view-more-desc.view-less {
  color: #f33066;
}

.details-product .information .woocommerce-product-details__short-description-wrapper.v2 {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .details-product .information .woocommerce-product-details__short-description-wrapper.v2 {
    margin-bottom: 30px;
  }
}

.details-product .information .woocommerce-product-details__short-description-wrapper.v2 .woocommerce-product-details__short-description {
  margin-bottom: 3px;
}

.details-product .information .cart {
  width: 100%;
  margin: 0 0 15px !important;
}

@media (min-width: 1200px) {
  .details-product .information .cart {
    margin: 0 0 35px !important;
  }
}

.details-product .information .cart .group_table tr td:first-child div.quantity {
  margin: 0 !important;
}

.details-product .information .cart .group_table ~ .button {
  margin-right: 0;
}

.details-product .information .cart div.quantity-wrapper {
  overflow: hidden;
  margin: 0;
  float: right;
}

.details-product .information .cart div.quantity-wrapper > * {
  display: inline-block;
  vertical-align: middle;
  float: none !important;
}

.details-product .information .cart div.quantity-wrapper > label {
  font-weight: 400;
  margin-left: 0.625rem;
  margin-bottom: 0;
}

.details-product .information .cart div.quantity-wrapper .qty {
  background: #fff;
}

.details-product .information .cart .button {
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .details-product .information .cart .button {
    margin-right: 20px;
    min-width: 180px;
    text-align: center;
  }
}

.details-product .information .cart .quantity.hidden + .button {
  margin: 0;
}

.details-product .information .cart.grouped_form .quantity-wrapper {
  margin: 0 !important;
}

.details-product .information .cart.grouped_form .quantity-wrapper label {
  display: none;
}

.details-product .information .clear {
  display: none;
}

.details-product .information .product_title {
  clear: both;
}

.details-product .title-cat-wishlist-wrapper {
  position: relative;
  padding-left: 30px;
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .details-product .title-cat-wishlist-wrapper {
    margin-bottom: 30px;
  }
}

.details-product .apus-social-share {
  margin-top: 15px;
}

.details-product .apus-social-share span {
  font-size: 15px;
  display: inline-block;
  margin-left: 10px;
}

.details-product .apus-social-share a {
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 0.9375rem;
  margin: 0 2px;
}

.details-product .apus-social-share a:hover, .details-product .apus-social-share a:active {
  background-color: var(--educrat-theme-color);
  color: #fff;
}

.details-product .apus-discounts {
  margin: 20px 0 15px;
  padding: 15px 20px;
  background: #eceff6;
  font-size: 13px;
}

.details-product .apus-discounts ul {
  margin: 0;
  list-style: none;
  padding: 0;
}

.details-product .apus-discounts ul li {
  margin: 0 0 3px;
}

.details-product .apus-discounts ul li:before {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--educrat-theme-color);
  content: "\f00c";
  margin-left: 8px;
}

.details-product .apus-discounts .icon {
  display: inline-block;
  vertical-align: middle;
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  color: #fff;
  background: #a7b5d5;
  font-size: 14px;
  border-radius: 50%;
  margin-left: 10px;
}

.details-product .apus-discounts .title {
  font-size: 18px;
  margin: 0 0 10px;
}

.details-product .product-free-gift {
  margin: 0 0 20px;
  padding: 15px 20px;
  background: #f33066;
}

.details-product .product-free-gift .icon {
  display: inline-block;
  vertical-align: middle;
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  color: #fff;
  background: #e23e1d;
  font-size: 14px;
  border-radius: 50%;
  margin-left: 10px;
}

.details-product .product-free-gift .title {
  font-size: 18px;
  margin: 0 0 10px;
}

.details-product .product-free-gift .list-gift {
  font-size: 13px;
  list-style: none;
  padding: 0;
  margin: 0;
}

.details-product .product-free-gift .list-gift li {
  margin-bottom: 3px;
}

.details-product .product-free-gift .list-gift i {
  color: #e23e1d;
}

.details-product .product-free-gift .hightcolor {
  font-weight: 500;
  color: #e23e1d;
}

.related.products {
  padding: 1.875rem 0 0;
}

@media (min-width: 1200px) {
  .related.products {
    padding: 80px 0 0;
  }
}

.related.products .widget-title {
  text-align: center;
  font-size: 20px;
  margin: 0 0 1.875rem;
}

@media (min-width: 1200px) {
  .related.products .widget-title {
    font-size: 30px;
    margin-bottom: 50px;
  }
}

.related.products .product-block {
  margin-bottom: 0;
}

.accessoriesproducts-wrapper {
  position: relative;
}

.accessoriesproducts-wrapper.loading:before {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: 99;
  content: '';
  background: url("../images/loading-quick.gif") center center no-repeat rgba(255, 255, 255, 0.9);
}

/*------------------------------------*\
    Product Category and Subcategories
\*------------------------------------*/
.product-category .product-category-content {
  position: relative;
  overflow: hidden;
  min-height: 45px;
  margin: 0 0 1.875rem 0;
}

.product-category .product-category-image {
  display: block;
}

.product-category .product-category-title {
  text-transform: none;
  position: absolute;
  text-align: center;
  bottom: 0;
  right: 0;
  width: 100%;
  font-weight: 400;
  font-size: 0.9375rem;
  color: #fff;
  margin: 0;
  padding: 15px 10px;
  background: rgba(0, 0, 0, 0.3);
}

.product-category .product-category-title .count {
  background: transparent;
  color: #fff;
}

/*------------------------------------*\
    Quickview
\*------------------------------------*/
#apus-quickview-modal .product_meta {
  margin: 15px 0 0;
}

/**
 *
 *  Woocommerce Form
 */
.form-row .checkbox, .form-row .input-radio {
  margin-bottom: 0;
  margin-top: 0;
}

.woocommerce form .form-row {
  margin: 0 0 15px;
  padding: 0;
}

.woocommerce .col2-set .col-2, .woocommerce-page .col2-set .col-2,
.woocommerce .col2-set .col-1, .woocommerce-page .col2-set .col-1 {
  width: 100%;
}

/* End
------------------------------------------------*/
/*-------------------------------*\
    Utilities
\*------------------------------------*/
.woocommerce #reviews #comments ol.commentlist {
  padding: 0;
}

.woocommerce #reviews #comments ol.commentlist li {
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .woocommerce #reviews #comments ol.commentlist li {
    margin-bottom: 40px;
  }
}

.woocommerce #reviews #comments ol.commentlist li .apus-avata {
  min-width: 80px;
  padding-left: 10px;
}

@media (min-width: 1200px) {
  .woocommerce #reviews #comments ol.commentlist li .apus-avata {
    min-width: 90px;
    padding-left: 20px;
  }
}

.woocommerce #reviews #comments ol.commentlist li .apus-avata .apus-image {
  display: inline-block;
}

.woocommerce #reviews #comments ol.commentlist li img.avatar {
  width: 70px;
  height: 70px;
  border: none;
  border-radius: 50%;
  padding: 0;
  margin: 0;
  position: relative;
}

.woocommerce #reviews #comments ol.commentlist li .dokan-review-author-img {
  float: right;
  padding-left: 30px;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text {
  border: none;
  padding: 15px 0 0;
  margin: 0;
}

.woocommerce #reviews #comments ol.commentlist li .description {
  margin-top: 10px;
}

.woocommerce #reviews #comments ol.commentlist li .description p {
  margin: 0;
}

.woocommerce #reviews #comments ol.commentlist li .apus-author {
  font-size: 0.9375rem;
  color: var(--educrat-link-color);
  margin: 0;
  text-transform: capitalize;
}

.woocommerce #reviews #comments ol.commentlist li .content-comment {
  margin-top: 12px;
}

.woocommerce #reviews #comments ol.commentlist li .content-comment p:last-child {
  margin-bottom: 0;
}

#respond [for="rating"] {
  font-weight: 400;
  margin: 0;
  margin-left: 0.9375rem;
}

#respond .form-submit input {
  right: auto;
}

.woocommerce #reviews #comment {
  height: 150px;
  resize: none;
}

@media (min-width: 1200px) {
  .woocommerce #reviews #comment {
    height: 200px;
  }
}

.woocommerce #reviews .comment-reply-title {
  margin-bottom: 15px;
}

/*------------------------------------*\
    Quantity inputs
\*------------------------------------*/
.woocommerce .quantity .qty {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  appearance: textfield;
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
  width: 75px;
  border-radius: 8px;
  border: 1px solid #EDEDED;
  padding: 0.6rem 1.29rem;
  color: var(--educrat-text-color);
  background-color: #fff;
  outline: none;
}

.woocommerce .quantity .qty::-webkit-input-placeholder {
  /* Edge */
  opacity: 1;
  filter: alpha(opacity=100);
}

.woocommerce .quantity .qty:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  opacity: 1;
  filter: alpha(opacity=100);
}

.woocommerce .quantity .qty::placeholder {
  opacity: 1;
  filter: alpha(opacity=100);
}

.woocommerce .quantity .qty:focus {
  border-color: #1A064F;
}

.woocommerce .quantity .reader-text {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0;
  margin-left: 10px;
}

.woocommerce a.remove {
  border-radius: 0;
}

/*------------------------------------*\
    Forms
\*------------------------------------*/
.form-row::after {
  display: block;
  clear: both;
  content: "";
}

.form-row label.hidden {
  visibility: hidden;
}

.form-row label.inline {
  display: inline;
}

.form-row label {
  display: block;
  font-weight: 500;
  color: var(--educrat-link-color);
  font-size: 1rem;
}

.form-row select {
  cursor: pointer;
}

.form-row .required {
  color: #f33066;
  font-weight: 700;
  border: 0;
}

.form-row .input-text {
  width: 100%;
  padding: 5px 18px;
}

.form-row.form-row-first {
  width: 47%;
  float: right;
}

.form-row.form-row-last {
  width: 47%;
  float: left;
}

.form-row.form-row-wide {
  clear: both;
}

.select2-container .select2-choice {
  padding: 5px 7px;
}

/*------------------------------------*\
    Mini cart and wishlist
\*------------------------------------*/
.total-minicart {
  color: var(--educrat-link-color);
  font-weight: normal;
  font-size: 16px;
  margin-right: 5px;
  display: inline-block;
}

.mini-cart {
  padding: 0 10px;
}

.wishlist-icon,
.mini-cart {
  display: inline-block;
  position: relative;
  color: var(--educrat-link-color);
}

.wishlist-icon i,
.mini-cart i {
  font-size: 20px;
  line-height: 1;
  display: inline-block;
  vertical-align: text-top;
  margin: 0 !important;
}

.wishlist-icon:after,
.mini-cart:after {
  display: none !important;
}

.wishlist-icon .count,
.mini-cart .count {
  position: absolute;
  top: -5px;
  left: 2px;
  display: inline-block;
  font-size: 10px;
  color: #fff;
  background: var(--educrat-theme-color);
  border-radius: 50%;
  line-height: 15px;
  min-width: 15px;
  padding: 0 3px;
  text-align: center;
}

.wishlist-icon i {
  margin-left: 5px;
}

/*------------------------------------*\
    Star Ratings
\*------------------------------------*/
.woocommerce p.stars {
  font-size: 12px;
  letter-spacing: 3px;
  margin: 0 !important;
  height: 12px;
}

.woocommerce p.stars a:before {
  content: "\53";
  font-family: 'star';
  color: #FF9800;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}

.woocommerce p.stars:hover a:before {
  content: "\53";
  font-family: 'star';
}

.woocommerce p.stars a:hover ~ a:before {
  content: "\53";
  color: #c1cde4;
}

.woocommerce p.stars.selected a:not(.active)::before {
  content: "\53";
}

.woocommerce p.stars.selected a.active:before {
  content: "\53";
}

.woocommerce p.stars.selected a.active ~ a:before {
  content: "\53";
  color: #c1cde4;
}

.woocommerce .star-rating {
  overflow: hidden;
  position: relative;
  width: 80px;
  height: 11px;
  line-height: 11px;
  font-family: 'star';
  font-size: 11px;
  letter-spacing: 5px;
  float: none;
}

.woocommerce .star-rating:before {
  content: "\53\53\53\53\53";
  color: #c1cde4;
  float: left;
  top: 0;
  right: 0;
  position: absolute;
}

.woocommerce .star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  right: 0;
  position: absolute;
  padding-top: 1.5em;
}

.woocommerce .star-rating span:before {
  content: "\53\53\53\53\53";
  top: 0;
  position: absolute;
  right: 0;
  color: #FF9800;
}

.rating > * {
  display: inline-block !important;
  vertical-align: middle;
}

.rating .star-rating {
  margin: 0 !important;
}

.rating .counts {
  color: var(--educrat-link-color);
  margin-right: 7px;
}

/*------------------------------------*\
    Filter
\*------------------------------------*/
.archive-shop .page-title {
  display: none;
}

.show-filter {
  font-size: 18px;
  color: var(--educrat-theme-color);
  cursor: pointer;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.show-filter:hover, .show-filter:active {
  color: var(--educrat-theme-color);
}

.show-filter i {
  margin-right: 10px;
}

.apus-shop-menu {
  font-size: 15px;
  margin: 0;
  position: relative;
}

.apus-shop-menu .filter-action i {
  margin-left: 3px;
}

.apus-shop-menu ul.apus-filter-menu {
  padding: 0;
  margin: 5px 0 0;
  list-style: none;
  float: left;
}

.apus-shop-menu ul.apus-filter-menu li {
  display: inline-block;
}

.apus-shop-menu ul.apus-categories {
  float: right;
  padding: 0;
  margin: 2px 0 0;
  list-style: none;
}

.apus-shop-menu ul.apus-categories li {
  display: inline-block;
  margin-left: 40px;
}

.apus-shop-menu ul.apus-categories li a {
  text-transform: capitalize;
  padding: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--educrat-link-color);
  position: relative;
  display: inline-block;
}

.apus-shop-menu ul.apus-categories li .product-count {
  font-size: 14px;
  color: var(--educrat-text-color);
  margin: 0 2px;
  vertical-align: top;
  display: inline-block;
}

.apus-shop-menu ul.apus-categories li.current-cat > a {
  color: var(--educrat-theme-color);
}

.apus-shop-menu ul.apus-categories .apus-shop-sub-categories {
  padding: 0px;
  margin: 10px 0 0;
}

.apus-shop-menu ul.apus-categories .apus-shop-sub-categories li a {
  font-size: 16px;
}

.apus-shop-menu .content-inner #apus-orderby {
  margin-right: 40px;
}

.apus-categories-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: none;
  color: var(--educrat-link-color);
  font-size: 14px;
  margin-top: 4px;
}

.apus-categories-dropdown .category-dropdown-label {
  cursor: pointer;
}

.apus-categories-dropdown option {
  font-size: 16px;
  color: var(--educrat-text-color);
}

.apus-categories-dropdown option[selected="selected"] {
  color: var(--educrat-link-color);
}

.apus-categories-dropdown .dropdown-menu {
  min-width: 200px;
  padding: 20px 30px;
  border-radius: 0;
  border: 1px solid var(--educrat-theme-color);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.apus-categories-dropdown .dropdown-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.apus-categories-dropdown .dropdown-menu ul li {
  margin: 0 0 5px;
}

.apus-categories-dropdown .dropdown-menu ul li a {
  color: var(--educrat-text-color);
}

.apus-categories-dropdown .dropdown-menu ul li a:hover, .apus-categories-dropdown .dropdown-menu ul li a:active {
  color: var(--educrat-link-color);
}

.apus-categories-dropdown .dropdown-menu ul li.active {
  color: var(--educrat-link-color);
}

.apus-categories-dropdown .dropdown-menu ul li:last-child {
  margin: 0;
}

.before-shop-header-wrapper {
  position: relative;
}

@media (min-width: 768px) {
  .before-shop-header-wrapper .before-shop-loop-fillter {
    position: absolute;
    top: 20px;
  }
}

.pagination-top {
  margin-top: -6px;
}

.pagination-top .apus-pagination.pagination-woo {
  margin: 0;
}

.pagination-top .apus-pagination .apus-pagination-inner {
  padding: 0;
}

.pagination-top.has-fillter .apus-pagination .apus-pagination-inner {
  padding: 0 1.875rem;
}

.apus-filter {
  margin-bottom: 15px;
  font-size: 14px;
}

@media (min-width: 1200px) {
  .apus-filter {
    margin-bottom: 1.875rem;
  }
}

.apus-filter .shop-page-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 24px;
}

.apus-filter .woocommerce-result-count {
  margin: 0;
  color: var(--educrat-link-color);
  font-size: 14px;
  font-weight: 500;
}

.apus-filter #apus-orderby {
  float: right;
}

.apus-filter .woocommerce-ordering {
  margin: 0;
}

.apus-filter .subtitle {
  font-weight: 500;
  color: var(--educrat-link-color);
  margin-left: 12px;
}

.apus-filter select {
  color: var(--educrat-text-color);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url("../images/select.png") #EEF2F6 left 10px center no-repeat;
  padding: 10px 12px;
  margin: 0;
  border: 1px solid #EEF2F6;
  border-radius: 8px;
}

.apus-filter .select2-container--default.orderby {
  min-width: 180px;
}

.apus-filter .select2-selection--single {
  border: 0 !important;
}

.apus-filter .display-mode {
  margin-top: 4px;
}

.apus-filter .change-view {
  color: #cccccc;
  display: inline-block;
}

.apus-filter .change-view i {
  font-size: 24px;
  vertical-align: middle;
}

.apus-filter .change-view + .change-view {
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .apus-filter .change-view + .change-view {
    margin-right: 20px;
  }
}

.apus-filter .change-view:hover, .apus-filter .change-view.active {
  color: var(--educrat-theme-color);
}

@media (min-width: 1200px) {
  .apus-filter .form-educrat-ppp .educrat-wc-wppp-select {
    min-width: 190px;
  }
}

.form-educrat-ppp {
  float: right;
}

.form-educrat-ppp select {
  font-size: 16px;
  color: var(--educrat-text-color);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url("../images/select.png") #fff left 10px center no-repeat;
  font-weight: 400;
  border: 1px solid #EDEDED;
  padding: 3px 20px;
  border-radius: 2px;
  margin: 0;
  border: 1px solid #EDEDED;
}

#apus-orderby .orderby-label {
  color: var(--educrat-text-color);
  display: inline-block;
  font-size: 14px;
  font-weight: 300;
  cursor: pointer;
  border: 1px solid #EDEDED;
  border-radius: 50px;
  padding: 4px 15px;
}

#apus-orderby .dropdown-menu {
  min-width: 200px;
  padding: 20px 30px;
  border-radius: 5px;
  border-radius: 0;
  border: 1px solid var(--educrat-theme-color);
  -webkit-box-shadow: none;
  box-shadow: none;
}

#apus-orderby .dropdown-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#apus-orderby .dropdown-menu ul li {
  margin: 0 0 5px;
}

#apus-orderby .dropdown-menu ul li a {
  color: var(--educrat-text-color);
}

#apus-orderby .dropdown-menu ul li a:hover, #apus-orderby .dropdown-menu ul li a:active {
  color: var(--educrat-link-color);
}

#apus-orderby .dropdown-menu ul li.active {
  color: var(--educrat-link-color);
}

#apus-orderby .dropdown-menu ul li:last-child {
  margin: 0;
}

/*------------------------------------*\
    Mini Cart
\*------------------------------------*/
.apus-topcart .dropdown-menu {
  margin: 8px 0 0 !important;
  padding: 15px;
  width: 300px;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  -webkit-box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  box-shadow: 0 25px 70px 0 rgba(1, 33, 58, 0.07);
  background: #fff;
  opacity: 0;
  filter: alpha(opacity=0);
}

@media (min-width: 1200px) {
  .apus-topcart .dropdown-menu {
    width: 410px;
    padding: 1.875rem;
  }
}

.apus-topcart .dropdown-menu.show {
  display: block;
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-topcart .dropdown-menu:before {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  background-color: #ffffff;
  content: "";
  width: 10px;
  height: 10px;
  left: 15px;
  top: -5px;
  position: absolute;
}

.apus-topcart .buttons {
  margin: 0;
}

.apus-topcart .buttons .wc-forward {
  width: calc(50%-10px);
}

.apus-topcart .buttons .wc-forward + .wc-forward {
  margin-right: 20px;
}

.apus-topcart .overlay-offcanvas-content {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  cursor: no-drop;
  -webkit-transform: translateX(30px);
  -ms-transform: translateX(30px);
  -o-transform: translateX(30px);
  transform: translateX(30px);
  visibility: hidden;
  z-index: 2;
}

.apus-topcart .overlay-offcanvas-content.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.apus-topcart .offcanvas-content {
  z-index: 3;
  position: fixed;
  left: 0;
  top: 0;
  background: #fff;
  -webkit-transition: all 0.35s ease-in-out 0s;
  -o-transition: all 0.35s ease-in-out 0s;
  transition: all 0.35s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  width: 400px;
  height: 100vh;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
}

.apus-topcart .offcanvas-content.active {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.apus-topcart .offcanvas-content .shopping_cart_content .cart_list {
  max-height: calc(100% - 180px);
}

.apus-topcart .offcanvas-content .title-cart-canvas {
  font-size: 16px;
  text-align: center;
  margin: 0 0 10px;
  padding: 10px;
  border-bottom: 1px solid #EDEDED;
  text-transform: uppercase;
  position: relative;
}

.apus-topcart .offcanvas-content .title-cart-canvas .close-cart {
  position: absolute;
  top: 11px;
  right: 14px;
  z-index: 1;
  background: #fff;
  font-size: 18px;
  cursor: pointer;
  color: #f33066;
}

.apus-topcart .offcanvas-content .shopping_cart_content {
  padding: 10px;
  height: calc(100vh - 50px);
  display: -webkit-flex;
  /* Safari */
  display: flex;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
}

@media (min-width: 1200px) {
  .apus-topcart .offcanvas-content .shopping_cart_content {
    padding: 15px 15px 30px;
  }
}

.apus-topcart .offcanvas-content .shopping_cart_content .cart-bottom {
  align-self: flex-end;
  -webkit-align-self: flex-end;
  width: 100%;
}

.apus-topcart .offcanvas-content .shopping_cart_content .cart_list {
  width: 100%;
}

.shopping_cart_content {
  font-size: 14px;
}

.shopping_cart_content .variation {
  margin: 0 0 3px;
  overflow: hidden;
}

.shopping_cart_content .variation dt {
  margin-left: 5px;
}

.shopping_cart_content .variation dt, .shopping_cart_content .variation dd {
  float: right;
}

.shopping_cart_content .variation dt p, .shopping_cart_content .variation dd p {
  margin: 0;
}

.shopping_cart_content .cart_list {
  padding: 0 0 10px;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.shopping_cart_content .cart_list > div {
  margin: 0 0 20px;
  overflow: hidden;
}

.shopping_cart_content .cart_list > div.empty {
  border: none;
  margin: 0;
  color: var(--educrat-link-color);
}

.shopping_cart_content .cart_list > div:last-child {
  border: none;
}

.shopping_cart_content .cart_list .image {
  width: 80px;
  height: 80px;
  display: block;
  border-radius: 8px;
  overflow: hidden;
}

.shopping_cart_content .cart_list .image img {
  width: 80px;
  height: 80px;
  max-width: none;
}

.shopping_cart_content .cart_list .quantity {
  color: var(--educrat-link-color);
  font-weight: 500;
}

.shopping_cart_content .cart_list .name {
  font-weight: 400;
  font-size: 0.9375rem;
  margin: 0 0 5px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.shopping_cart_content .cart_list .cart-item {
  margin: 0;
  font-size: 16px;
}

.shopping_cart_content .cart_list .cart-main-content {
  text-align: right;
  position: relative;
  padding-right: 15px;
}

.shopping_cart_content .cart_list .cart-main-content .remove {
  position: absolute;
  left: 0;
  top: 6px;
  z-index: 1;
  font-size: 12px;
  background: transparent !important;
  color: var(--educrat-theme-color) !important;
}

.shopping_cart_content .cart_list .cart-main-content .remove:hover, .shopping_cart_content .cart_list .cart-main-content .remove:focus {
  color: #f33066 !important;
}

.shopping_cart_content .cart_list .cart-main-content .remove i:before {
  font-weight: 700;
}

.shopping_cart_content .cart_list .cart-item {
  overflow: hidden;
}

.shopping_cart_content .total {
  color: var(--educrat-heading-color);
  border-top: 1px solid #EDEDED;
  position: relative;
  margin: 0;
  font-weight: 500;
  text-transform: capitalize;
  padding: 20px 0;
  font-size: 18px;
}

.shopping_cart_content .total strong {
  font-weight: 500;
}

.shopping_cart_content .total .amount {
  font-size: 18px;
  margin-right: auto;
}

.shopping_cart_content .total.empty {
  border: none;
  margin: 0;
  padding-top: 0;
  text-align: center;
}

.woocommerce a.remove {
  padding: 0;
  margin: auto;
  color: #f33066;
  background: transparent;
}

/** Plugins  add to wishlist, compare **/
.place-order {
  padding: 1.875rem;
}

.input-text {
  border: 1px solid #e5e5e5;
  padding: 5px 10px;
}

.woocommerce address {
  margin-bottom: 20px;
}

.wc-block-product-categories {
  margin-bottom: 0;
}

.wc-block-product-categories-list,
.product-categories {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.wc-block-product-categories-list + .view-more-list-cat,
.product-categories + .view-more-list-cat {
  position: absolute;
  background: #fff;
  bottom: 1px;
  right: 1px;
  width: calc(100% - 2px);
  z-index: 99;
  display: block;
  color: #27b737;
  padding: 5px 54px 15px;
}

.wc-block-product-categories-list + .view-more-list-cat.view-less,
.product-categories + .view-more-list-cat.view-less {
  color: #f33066;
}

.wc-block-product-categories-list + .view-more-list-cat:hover, .wc-block-product-categories-list + .view-more-list-cat:active,
.product-categories + .view-more-list-cat:hover,
.product-categories + .view-more-list-cat:active {
  text-decoration: underline;
}

.wc-block-product-categories-list.hideContent,
.product-categories.hideContent {
  height: 435px;
}

.wc-block-product-categories-list.showContent,
.product-categories.showContent {
  height: auto;
}

.wc-block-product-categories-list .children,
.product-categories .children {
  list-style: none;
  padding: 0;
}

.wc-block-product-categories-list li,
.product-categories li {
  padding: 0 0 8px;
}

.wc-block-product-categories-list li li,
.product-categories li li {
  padding-right: 20px;
}

.wc-block-product-categories-list li:last-child,
.product-categories li:last-child {
  padding-bottom: 0;
}

.wc-block-product-categories-list li.current-cat-parent > a, .wc-block-product-categories-list li.current-cat > a, .wc-block-product-categories-list li:hover > a,
.product-categories li.current-cat-parent > a,
.product-categories li.current-cat > a,
.product-categories li:hover > a {
  color: var(--educrat-link_hover_color);
}

.wc-block-product-categories-list li [class*="count"],
.product-categories li [class*="count"] {
  display: inline-block;
  float: left;
}

.top-archive-shop {
  padding-bottom: 1.875rem;
}

.add-cart > .added {
  display: none !important;
}

.add-cart .added_to_cart:after {
  display: none;
}

.apus-shop-products-wrapper.loading {
  position: relative;
}

.apus-shop-products-wrapper.loading:before {
  background: url("../images/loading-quick.gif") center 100px/50px no-repeat rgba(255, 255, 255, 0.9);
  position: absolute;
  width: 100%;
  height: 100%;
  content: "";
  right: 0;
  top: 0;
  z-index: 99;
}

.woocommerce-account .woocommerce-MyAccount-content,
.woocommerce-account .woocommerce-MyAccount-navigation {
  width: 100%;
  float: none;
}

.woocommerce-account .woocommerce-MyAccount-navigation {
  border-bottom: 2px solid #eeeeee;
}

.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link {
  margin-left: 30px;
  display: inline-block;
}

.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a {
  padding: 0 0 7px;
  position: relative;
  display: inline-block;
}

.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a:before {
  width: 100%;
  height: 2px;
  background: var(--educrat-theme-color);
  position: absolute;
  bottom: -2px;
  right: 0;
  content: '';
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link.is-active > a, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:hover > a, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:active > a {
  color: var(--educrat-theme-color);
}

.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link.is-active > a:before, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:hover > a:before, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:active > a:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.woocommerce-MyAccount-content {
  padding: 20px 0;
}

.woocommerce-MyAccount-content h2 {
  margin: 20px 0 10px;
  text-transform: uppercase;
  font-size: 18px;
  font-family: var(--educrat-main-font);
}

.edit-account br {
  display: none;
}

.edit-account input[type="text"],
.edit-account input[type="password"] {
  height: 40px;
  border-radius: 3px;
}

.edit-account input[type="text"]:focus,
.edit-account input[type="password"]:focus {
  border-color: #EDEDED;
}

.edit-account legend {
  font-size: 72px;
  font-weight: 300;
  border: none;
  margin: 30px 0 0;
}

.edit-account label {
  font-weight: normal;
  font-size: 16px;
  color: var(--educrat-link-color);
}

.woocommerce-MyAccount-content,
.woocommerce-MyAccount-navigation {
  max-width: 970px;
  margin: auto;
}

.user .title {
  font-size: 20px;
  margin: 0 0 20px;
  text-align: center;
}

@media (min-width: 1200px) {
  .user .title {
    font-size: 25px;
  }
}

form.login,
form.register {
  margin: 0 !important;
  border: none !important;
  padding: 0 !important;
}

form.login br,
form.register br {
  display: none;
}

form.login label,
form.register label {
  font-weight: 400;
}

form.login .form-control,
form.register .form-control {
  padding: 5px 20px;
}

form.login .form-group,
form.register .form-group {
  margin: 0 0 20px;
}

form.login .form-group:last-child,
form.register .form-group:last-child {
  margin-bottom: 0;
}

form.login .lost_password a,
form.register .lost_password a {
  text-decoration: underline;
}

form.login .action-group,
form.register .action-group {
  font-size: 14px;
}

form.login .input-text,
form.register .input-text {
  background: #fff !important;
  border: 1px solid #EDEDED !important;
  height: 40px;
}

form.login .input-text:focus,
form.register .input-text:focus {
  border-color: #d4d4d4 !important;
}

form.login input[type="checkbox"],
form.register input[type="checkbox"] {
  margin-left: 7px;
}

form.login .input-submit ~ span,
form.register .input-submit ~ span {
  margin: 10px 0 0;
}

form.login .input-submit ~ span.pull-left,
form.register .input-submit ~ span.pull-left {
  margin-right: 15px;
}

form.login .input-submit ~ span.lost_password a,
form.register .input-submit ~ span.lost_password a {
  color: var(--educrat-theme-color);
}

form.login .user-role,
form.register .user-role {
  padding-right: 20px;
}

form.login .user-role [type="radio"],
form.register .user-role [type="radio"] {
  margin-top: 11px;
}

.login-wrapper .mfp-content {
  width: 500px !important;
  max-width: 80%;
  background-color: #fff;
}

.login-wrapper .title {
  text-align: center;
}

.login-wrapper .apus-mfp-close {
  font-size: 20px;
  display: inline-block;
  background: #f33066;
  color: #fff;
  display: inline-block;
  width: 42px;
  height: 42px;
  line-height: 42px;
  border: none;
  margin: -21px;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  opacity: 0.9;
  filter: alpha(opacity=90);
}

.login-wrapper .apus-mfp-close:hover, .login-wrapper .apus-mfp-close:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.cart_item > .media-left {
  width: 70%;
}

.cart_item img {
  width: 90px;
  max-width: none;
}

.cart_item .content-left {
  overflow: hidden;
  padding-right: 20px;
}

.cart_item .product-name {
  font-size: 15px;
  font-weight: 500;
  margin: 0 0 15px;
}

.cart_item .price {
  font-size: 20px;
  color: #4c4c4c;
  font-weight: 400;
}

.cart_item a.remove {
  margin: 0 0 15px;
  display: inline-block;
  color: var(--educrat-text-color) !important;
}

.cart_item a.remove:hover, .cart_item a.remove:active {
  color: #f33066 !important;
}

div.cart .input-text {
  height: 53px;
  border: 2px solid #EDEDED;
}

div.cart .input-text:focus, div.cart .input-text:active {
  border-color: var(--educrat-link-color);
}

div.cart label {
  font-size: 18px;
  color: #000;
}

.woocommerce #order_review_heading {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .woocommerce #order_review_heading {
    font-size: 20px;
  }
}

.woocommerce-order-details table.woocommerce-table--order-details,
.woocommerce-order-details table.woocommerce-checkout-review-order-table,
#order_review table.woocommerce-table--order-details,
#order_review table.woocommerce-checkout-review-order-table {
  border: none;
  margin-bottom: 20px;
}

.woocommerce-order-details table.woocommerce-table--order-details th,
.woocommerce-order-details table.woocommerce-table--order-details td,
.woocommerce-order-details table.woocommerce-checkout-review-order-table th,
.woocommerce-order-details table.woocommerce-checkout-review-order-table td,
#order_review table.woocommerce-table--order-details th,
#order_review table.woocommerce-table--order-details td,
#order_review table.woocommerce-checkout-review-order-table th,
#order_review table.woocommerce-checkout-review-order-table td {
  padding: 15px 0 !important;
  border: 0;
}

.woocommerce-order-details table.woocommerce-table--order-details thead,
.woocommerce-order-details table.woocommerce-checkout-review-order-table thead,
#order_review table.woocommerce-table--order-details thead,
#order_review table.woocommerce-checkout-review-order-table thead {
  background-color: transparent;
}

.woocommerce-order-details table.woocommerce-table--order-details thead th,
.woocommerce-order-details table.woocommerce-checkout-review-order-table thead th,
#order_review table.woocommerce-table--order-details thead th,
#order_review table.woocommerce-checkout-review-order-table thead th {
  color: var(--educrat-link-color);
  font-size: 15px;
  font-weight: 500;
  border-bottom: 1px solid #EDEDED;
}

.woocommerce-order-details table.woocommerce-table--order-details tbody td,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tbody td,
#order_review table.woocommerce-table--order-details tbody td,
#order_review table.woocommerce-checkout-review-order-table tbody td {
  width: 50%;
  padding: 7px 0 !important;
  font-size: 15px;
  font-weight: 400;
}

.woocommerce-order-details table.woocommerce-table--order-details tbody .product-quantity,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tbody .product-quantity,
#order_review table.woocommerce-table--order-details tbody .product-quantity,
#order_review table.woocommerce-checkout-review-order-table tbody .product-quantity {
  font-weight: 400;
}

.woocommerce-order-details table.woocommerce-table--order-details tbody tr:first-child td,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tbody tr:first-child td,
#order_review table.woocommerce-table--order-details tbody tr:first-child td,
#order_review table.woocommerce-checkout-review-order-table tbody tr:first-child td {
  padding-top: 15px !important;
}

.woocommerce-order-details table.woocommerce-table--order-details tfoot th, .woocommerce-order-details table.woocommerce-table--order-details tfoot td,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tfoot th,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tfoot td,
#order_review table.woocommerce-table--order-details tfoot th,
#order_review table.woocommerce-table--order-details tfoot td,
#order_review table.woocommerce-checkout-review-order-table tfoot th,
#order_review table.woocommerce-checkout-review-order-table tfoot td {
  border-top: 0;
  border-bottom: 1px solid #EDEDED !important;
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--educrat-link-color);
}

.woocommerce-order-details table.woocommerce-table--order-details tfoot tr:last-child th, .woocommerce-order-details table.woocommerce-table--order-details tfoot tr:last-child td,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tfoot tr:last-child th,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tfoot tr:last-child td,
#order_review table.woocommerce-table--order-details tfoot tr:last-child th,
#order_review table.woocommerce-table--order-details tfoot tr:last-child td,
#order_review table.woocommerce-checkout-review-order-table tfoot tr:last-child th,
#order_review table.woocommerce-checkout-review-order-table tfoot tr:last-child td {
  border: 0 !important;
  padding-bottom: 0 !important;
}

.woocommerce-order-details table.woocommerce-table--order-details tfoot .woocommerce-Price-amount,
.woocommerce-order-details table.woocommerce-checkout-review-order-table tfoot .woocommerce-Price-amount,
#order_review table.woocommerce-table--order-details tfoot .woocommerce-Price-amount,
#order_review table.woocommerce-checkout-review-order-table tfoot .woocommerce-Price-amount {
  font-weight: 500;
}

.woocommerce-order-details .cart_item,
#order_review .cart_item {
  margin: 0;
  padding: 0;
  border: none;
}

.woocommerce-order-details .product-name,
#order_review .product-name {
  margin: 0;
}

.woocommerce-order-details > .media-left,
#order_review > .media-left {
  width: auto;
}

.woocommerce-order-details .subtotal tr > *,
#order_review .subtotal tr > * {
  border-bottom: 1px solid #EDEDED !important;
}

.woocommerce-order-details .subtotal th,
#order_review .subtotal th {
  border: none;
  font-weight: 400;
  color: var(--educrat-link-color);
}

.woocommerce-order-details .subtotal td,
#order_review .subtotal td {
  text-align: left;
  padding: 10px 0;
  font-weight: 400;
}

.woocommerce-order-details .subtotal td label,
#order_review .subtotal td label {
  font-weight: 400;
}

.woocommerce-order-details .subtotal .order-total strong,
#order_review .subtotal .order-total strong {
  font-size: 20px;
}

.woocommerce-order-details .order-total .amount,
#order_review .order-total .amount {
  font-weight: 500;
}

.wrapper-icon-completed {
  display: inline-block;
  text-align: center;
  color: #fff;
  background-color: var(--educrat-link_hover_color);
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  font-size: 18px;
}

@media (min-width: 1200px) {
  .wrapper-icon-completed {
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 28px;
  }
}

.order-completed {
  margin: 10px 0 5px;
  font-size: 22px;
}

@media (min-width: 1200px) {
  .order-completed {
    font-size: 30px;
    margin: 20px 0 10px;
  }
}

.woocommerce ul.order_details {
  margin: 0;
}

.woocommerce ul.order_details li {
  float: none;
  display: inline-block;
  font-size: 0.9375rem;
  text-transform: inherit;
  width: 50%;
  margin: 0 0 20px;
  float: right;
  border: 0;
  padding: 0;
  width: 50%;
}

.woocommerce ul.order_details li:nth-child(2) ~ li {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .woocommerce ul.order_details li {
    width: 25%;
    margin: 0;
  }
}

.woocommerce ul.order_details li strong {
  display: block;
  width: 100%;
  margin-top: 10px;
  font-weight: 500;
  color: var(--educrat-theme-color);
  font-size: 15px;
}

.product-top-title {
  position: relative;
}

.product-top-title .view-more {
  position: absolute;
  top: 5px;
  left: 0;
}

.layout-detail-product #tabs-list-specifications td {
  padding: 15px;
  border-color: #eff0f2;
}

.layout-detail-product #tabs-list-specifications td:first-child {
  font-weight: 500;
  text-transform: uppercase;
}

.single-rating {
  margin: 0 0 30px;
  padding: 0 0 20px;
  border-bottom: 1px solid #EDEDED;
}

.single-rating:last-child {
  border: none;
  padding: 0;
  margin: 0;
}

.single-rating .avatar {
  max-width: none;
  min-width: 70px;
  border-radius: 50%;
}

.single-rating .media-left {
  padding-left: 20px;
}

.single-rating .stars-value {
  float: left;
}

.single-rating .stars-value .fa-star {
  color: #fednormal;
}

.single-rating h4 {
  font-weight: 400;
  font-size: 10px;
  margin: 0 0 15px;
  color: var(--educrat-text-color);
}

.single-rating h4 .name {
  font-weight: normal;
  font-size: 12px;
  color: #464646;
  text-transform: uppercase;
}

.single-rating h6 {
  margin: 0 0 15px;
}

.wrapper-filter {
  min-height: 73px;
  position: relative;
  padding: 20px 0;
  border-bottom: 1px solid #EDEDED;
}

.shop-top-sidebar-wrapper {
  background: #fff;
  padding: 20px 0 0;
  display: block;
  overflow: hidden;
  width: 100% !important;
}

@media (min-width: 992px) {
  .shop-top-sidebar-wrapper {
    padding: 40px 0 0;
  }
}

.shop-top-sidebar-wrapper .dropdown > span {
  color: #252525;
  font-weight: 500;
  font-size: 15px;
  display: block;
  margin: 0 0 15px;
  text-transform: uppercase;
}

.shop-top-sidebar-wrapper .widget {
  margin-bottom: 0;
}

@media (max-width: 767px) {
  .shop-top-sidebar-wrapper {
    margin-bottom: 15px;
  }
}

.shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner {
  margin-right: -15px;
  margin-left: -15px;
}

.shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner > * {
  padding-right: 15px;
  padding-left: 15px;
  float: right;
  width: 100%;
}

@media (min-width: 768px) {
  .shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner > * {
    width: 20%;
  }
}

.shop-top-sidebar-wrapper .wrapper-limit {
  padding: 10px;
}

.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter {
  padding: 0;
  margin: 0;
  list-style: none;
}

.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting li,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter li {
  margin-bottom: 8px;
}

.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting li:last-child,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter li:last-child {
  margin: 0;
}

.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting a,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter a {
  white-space: nowrap;
}

.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting .active,
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting .current,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter .active,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter .current {
  color: var(--educrat-theme-color);
}

.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter,
.shop-top-sidebar-wrapper .wrapper-limit .woocommerce-widget-layered-nav-list {
  height: 200px;
}

.shop-top-sidebar-wrapper .tagcloud {
  height: 200px;
}

.products-wrapper-grid-banner .cl-3 div.product.col-sm-4.first,
.products-wrapper-grid-banner .cl-2 div.product.col-sm-4.first {
  clear: none;
}

@media (min-width: 768px) {
  .products-wrapper-grid-banner .cl-3 div.product.col-sm-4:nth-child(3n + 1),
  .products-wrapper-grid-banner .cl-2 div.product.col-sm-4:nth-child(3n + 1) {
    clear: both;
  }
}

.products-wrapper-grid-banner .col-md-cus-5 {
  float: right;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 992px) {
  .products-wrapper-grid-banner .col-md-cus-5 {
    width: 20%;
  }
}

.product-category h3 {
  margin: 15px 0 0;
  font-size: 18px;
}

.product-category h3 .count {
  background: transparent;
  padding: 0;
}

.product-category .category-body {
  margin: 0 0 20px;
  text-align: center;
}

@media (min-width: 768px) {
  .product-category .category-body {
    margin: 0 0 30px;
  }
}

form.woocommerce-checkout .select2-container--default.select2-container .select2-selection--single {
  border-color: #EDEDED;
}

form.woocommerce-checkout .select2-container--default.select2-container.select2-container--open .select2-selection--single {
  border-color: #e0e0e0;
}

.wc-tab > h2 {
  font-size: 1rem;
  margin: 0 0 1.25rem;
}

.wc-tab p:last-child {
  margin-bottom: 0;
}

.woocommerce-tabs {
  margin-top: 1.875rem;
  max-width: 850px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1200px) {
  .woocommerce-tabs {
    margin-top: 80px;
  }
}

.details-product .image-mains .slick-slide img {
  display: inline-block;
}

.box-white-inner {
  background: #fff;
  border: 1px solid #EDEDED;
  padding: 20px;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .box-white-inner {
    padding: 1.875rem;
  }
}

.box-white-inner.box-order {
  margin-top: 20px;
  border: 2px dashed var(--educrat-theme-color);
}

@media (min-width: 1200px) {
  .box-white-inner.box-order {
    padding: 35px 50px;
    margin-top: 60px;
  }
}

.box-white-inner.order-details {
  margin-bottom: 1.875rem;
  background: #F7F8FB;
}

@media (min-width: 1200px) {
  .box-white-inner.order-details {
    padding: 50px;
    margin-top: 30px;
    margin-bottom: 70px;
  }
}

.woocommerce-customer-details > h2,
.woocommerce-order-details__title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .woocommerce-customer-details > h2,
  .woocommerce-order-details__title {
    font-size: 20px;
  }
}

@media (max-width: 1199px) {
  .details-review {
    margin-top: 1.875rem;
  }
}

.order-review {
  background-color: #F7F8FB;
}

.woocommerce-noreviews {
  margin-bottom: 20px !important;
}

.apus-woocommerce-breadcrumb {
  background: #f7f8f9 !important;
}

.woocommerce-info {
  border-color: var(--educrat-theme-color);
}

.woocommerce-info:before {
  color: var(--educrat-theme-color);
}

/* 7. woocommerce widgets */
.widget.widget-products .tab-content .ajax-loading {
  background: url("../images/loading-quick.gif") center 100px no-repeat #fff;
}

.widget.widget-products .widget-title {
  padding: 0 0 10px;
  margin-bottom: 25px;
}

.widget.widget-products .slick-carousel-top .slick-arrow {
  top: -60px;
}

.widget.widget-products.column1 .shop-list-small {
  margin-top: -1px;
}

.link-readmore {
  position: relative;
  padding: 1.875rem 0;
}

.link-readmore:before {
  content: '';
  background: #EDEDED;
  position: absolute;
  top: 50%;
  right: 0;
  width: 100%;
  height: 1px;
  z-index: 2;
}

.link-readmore .link-inner {
  display: inline-block;
  padding: 0 30px;
  background: #fff;
  position: relative;
  z-index: 3;
}

.category-item {
  text-align: center;
  border: 1px solid #EDEDED;
  -webkit-transition: all 0.35s ease-in-out 0s;
  -o-transition: all 0.35s ease-in-out 0s;
  transition: all 0.35s ease-in-out 0s;
  padding: 10px;
}

@media (min-width: 1200px) {
  .category-item {
    padding: 50px 30px 30px;
  }
}

.category-item .image-wrapper {
  margin-bottom: 10px;
}

@media (min-width: 1200px) {
  .category-item .image-wrapper {
    margin-bottom: 25px;
  }
}

.category-item .cat-title {
  margin: 0;
  font-size: 18px;
}

@media (min-width: 1200px) {
  .category-item .cat-title {
    font-size: 24px;
  }
}

.category-item .product-nb {
  font-size: 12px;
  color: var(--educrat-theme-color);
  letter-spacing: 1px;
  text-transform: uppercase;
}

.category-item:hover {
  border-color: var(--educrat-theme-color);
}

/*------------------------------------*\
    Widget Price Filter
\*------------------------------------*/
.woocommerce .widget_price_filter .ui-slider .ui-slider-range {
  background: var(--educrat-theme-color);
}

.woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content {
  background: #ebebeb;
  height: 3px;
  margin: 12px 10px;
}

.widget_price_filter {
  font-family: var(--educrat-main-font);
}

.widget_price_filter .price_slider_wrapper {
  overflow: hidden;
}

.widget_price_filter .price_slider_amount .price_label {
  font-weight: 400;
  font-size: 0.9375rem;
  display: inline-block;
  text-transform: capitalize;
  float: right;
}

.widget_price_filter .ui-slider {
  position: relative;
  text-align: right;
}

.widget_price_filter .ui-slider .ui-slider-range {
  top: 0;
  height: 100%;
  background: #dddddd;
}

.widget_price_filter .price_slider_wrapper .ui-widget-content {
  background: #eaeaea;
  height: 4px;
  margin: 5px 10px 20px;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-handle {
  z-index: 2;
  position: absolute;
  width: 15px;
  height: 15px;
  border-radius: 15px;
  cursor: pointer;
  background: var(--educrat-theme-color);
  top: -6px;
}

.woocommerce .widget_price_filter .price_slider_amount {
  text-align: right;
  margin-top: 22px;
}

.woocommerce .widget_price_filter .price_slider_amount > input {
  width: 48%;
  margin-bottom: 5px;
  border: 2px solid #EDEDED;
}

.woocommerce .widget_price_filter .price_slider_amount > input:focus {
  border-color: #000;
}

/*------------------------------------*\
    Product List Widget
\*------------------------------------*/
.woocommerce ul.product_list_widget {
  list-style: none;
}

.woocommerce ul.product_list_widget li {
  clear: both;
  margin: 0 0 15px;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

@media (min-width: 1200px) {
  .woocommerce ul.product_list_widget li {
    margin-bottom: 20px;
  }
}

.woocommerce ul.product_list_widget li:last-child {
  margin-bottom: 0;
}

.woocommerce ul.product_list_widget li img {
  width: 100%;
  margin: 0;
  float: none;
}

.woocommerce ul.product_list_widget .star-rating {
  display: none;
}

.woocommerce ul.product_list_widget .woocommerce-Price-amount {
  font-size: 0.9375rem;
  font-weight: 600;
  color: var(--educrat-link-color);
}

.woocommerce ul.product_list_widget del {
  display: none;
}

.woocommerce ul.product_list_widget .product-title {
  font-size: 0.9375rem;
  margin: 3px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.woocommerce ul.product_list_widget .product-title a {
  font-weight: inherit;
}

.woocommerce ul.product_list_widget .left-content {
  width: 80px;
  padding: 5px;
  border: 1px solid #EDEDED;
  border-radius: 8px;
  flex-shrink: 0;
}

.woocommerce ul.product_list_widget .left-content a {
  display: block;
  overflow: hidden;
  border-radius: 8px;
  max-height: 80px;
}

.woocommerce ul.product_list_widget .left-content + .right-content {
  padding-right: 20px;
  flex-grow: 1;
}

/*------------------------------------*\
    Widget currency-switcher
\*------------------------------------*/
.woocommerce-currency-switcher-form {
  min-width: 100px;
}

.woocommerce-currency-switcher-form .dd-select {
  background: #fff !important;
  border: none;
  border-radius: 0;
}

.woocommerce-currency-switcher-form ul.dd-options {
  border: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.woocommerce-currency-switcher-form ul.dd-options li {
  padding: 0;
  border: none;
}

.widget-woocommerce-currency-switcher .dd-desc {
  display: none;
}

.widget-woocommerce-currency-switcher a.dd-option,
.widget-woocommerce-currency-switcher .dd-selected {
  padding: 5px 10px !important;
  color: var(--educrat-text-color);
}

.widget-woocommerce-currency-switcher label {
  line-height: 100%;
  float: right;
  margin: 0;
}

.widget-woocommerce-currency-switcher .dd-pointer {
  border: none !important;
  margin: 0 !important;
}

.widget-woocommerce-currency-switcher .dd-pointer:before {
  font-family: FontAwesome;
  position: absolute;
  line-height: 100%;
  left: 0;
  bottom: -4px;
}

.widget-woocommerce-currency-switcher .dd-pointer.dd-pointer-down:before {
  content: "\f107";
}

.widget-woocommerce-currency-switcher .dd-pointer.dd-pointer-up:before {
  content: "\f106";
}

.apus-products-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.apus-products-list .product-block {
  padding: 10px 0;
  background: #ffffff;
}

.apus-products-list .media-left {
  padding: 0;
}

.apus-products-list .media-body {
  padding-right: 20px;
}

.apus-products-list .rating {
  display: none;
}

.apus-products-list .name {
  font-family: var(--educrat-main-font);
  margin: 0;
}

.apus-products-list .name a {
  font-size: 16px;
  text-transform: capitalize;
}

.apus-products-list .product-block:hover .name a {
  color: var(--educrat-theme-color);
}

.apus-products-list .groups-button * i {
  color: var(--educrat-text-color);
}

.apus-products-list .groups-button * i:hover {
  color: var(--educrat-theme-color);
}

.apus-products-list .groups-button .addcart, .apus-products-list .groups-button .yith-wcwl-add-to-wishlist, .apus-products-list .groups-button .quick-view {
  display: inline-block;
  padding-left: 26px;
}

.apus-products-list .groups-button .addcart .add-cart a {
  background: transparent;
  padding: 0;
}

.apus-products-list .groups-button .addcart .add-cart a .title-cart {
  display: none;
}

.apus-products-list .groups-button .yith-wcwl-add-to-wishlist {
  vertical-align: bottom;
}

.apus-products-list .groups-button .yith-wcwl-add-to-wishlist .sub-title {
  display: none;
}

.apus-products-list .groups-button .yith-wcwl-add-to-wishlist .feedback {
  display: none;
}

.apus-products-list .groups-button .quick-view {
  padding-left: 0;
  vertical-align: middle;
}

.apus-products-list .groups-button .quick-view a.quickview {
  background: transparent;
  border: none;
  padding: 0px;
}

.apus-products-list .price {
  margin-bottom: 10px;
}

.apus-products-list .price span.woocs_price_code del span.woocommerce-Price-amount {
  font-size: 20px;
  color: #888625;
}

.apus-products-list .price span.woocs_price_code ins span.woocommerce-Price-amount {
  font-size: 24px;
  font-weight: normal;
  color: #888625;
}

.apus-products-list .price span.woocs_price_code span.woocommerce-Price-amount {
  font-size: 24px;
  font-weight: normal;
  color: #888625;
}

.sub-categories .sub-title {
  font-size: 15px;
  color: #fff;
  background: #0d6efd;
  padding: 14px 40px;
  margin: 0;
  text-transform: uppercase;
}

.sub-categories .sub-title .icon {
  margin-left: 20px;
}

.sub-categories .sub-title .pull-right {
  margin-top: 3px;
}

.sub-categories > .list-square {
  padding: 15px 40px;
  background: #f5f5f5;
}

.sub-categories > .list-square > li > a {
  color: var(--educrat-text-color);
}

.sub-categories > .list-square > li > a:before {
  background: var(--educrat-text-color);
}

.sub-categories > .list-square > li:hover > a, .sub-categories > .list-square > li.active > a {
  color: var(--educrat-link-color);
}

.sub-categories > .list-square > li:hover > a:before, .sub-categories > .list-square > li.active > a:before {
  background: var(--educrat-link-color);
}

.list-banner-category .category-wrapper {
  position: relative;
}

.list-banner-category .category-wrapper .category-meta {
  position: absolute;
  bottom: 50px;
  right: 0;
  z-index: 1;
}

.list-banner-category .title {
  margin: 0;
  font-size: 36px;
  letter-spacing: 0.5px;
}

.list-banner-category .title a:hover, .list-banner-category .title a:active {
  text-decoration: underline;
}

.all-products {
  font-size: 36px;
  color: var(--educrat-link-color);
  text-align: left;
}

.all-products a:hover, .all-products a:active {
  text-decoration: underline;
}

.grid-banner-category.style1 .link-action {
  display: block;
  position: relative;
}

.grid-banner-category.style1 .link-action:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.grid-banner-category.style1 .link-action .title {
  font-size: 14px;
  text-transform: uppercase;
  margin: 0;
  display: inline-block;
  font-weight: 500;
  padding: 10px 35px;
  background: #fff;
  letter-spacing: 1px;
}

.grid-banner-category.style1 .link-action .info {
  text-align: center;
  top: 50%;
  margin-top: -19px;
  position: absolute;
  right: 0;
  width: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.grid-banner-category.style1 .link-action:hover:before,
.grid-banner-category.style1 .link-action:hover .info, .grid-banner-category.style1 .link-action:active:before,
.grid-banner-category.style1 .link-action:active .info {
  opacity: 1;
  filter: alpha(opacity=100);
}

.grid-banner-category.style1 .link-action:hover .info, .grid-banner-category.style1 .link-action:active .info {
  -webkit-animation: zoomInDown 0.5s linear 1;
  /* Safari 4.0 - 8.0 */
  animation: zoomInDown 0.5s linear 1;
}

.grid-banner-category.style2 .link-action {
  display: block;
  position: relative;
  overflow: hidden;
}

.grid-banner-category.style2 .link-action:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200%;
  height: 200%;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 0 100%;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  transform-origin: 100% 0;
  -ms-transform-origin: 100% 0;
  /* IE 9 */
  -webkit-origin: 0 0;
  /* Safari 3-8 */
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
}

.grid-banner-category.style2 .link-action .title {
  font-size: 16px;
  text-transform: uppercase;
  margin: 0;
  display: inline-block;
  font-weight: 500;
  padding: 10px 35px;
  background: #fff;
  letter-spacing: 1px;
  border: 1px solid #ebebeb;
}

.grid-banner-category.style2 .link-action .info {
  text-align: center;
  top: 10px;
  position: absolute;
  right: 10px;
}

@media (min-width: 1200px) {
  .grid-banner-category.style2 .link-action .info {
    top: 40px;
    right: 40px;
  }
}

.grid-banner-category.style2 .link-action:hover:before, .grid-banner-category.style2 .link-action:active:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

table > thead > tr > th, table > thead > tr > td, .table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
  border: 0;
}

table > thead > tr > th, table > thead > tr > td, table > tbody > tr > th, table > tbody > tr > td, table > tfoot > tr > th, table > tfoot > tr > td, .table-bordered > thead > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > tfoot > tr > td {
  border-bottom: 0;
  border-left: 0;
}

.woocommerce #content table.cart td.actions .coupon, .woocommerce table.cart td.actions .coupon, .woocommerce-page #content table.cart td.actions .coupon, .woocommerce-page table.cart td.actions .coupon {
  margin-left: 10px;
}

.woocommerce-info {
  background-color: #f4f5f7;
}

.select2-container--default .select2-selection--single {
  border: none;
}

.woocommerce form .form-row .input-checkbox {
  position: static;
  float: none;
  display: inline-block;
  margin: 0 0 0 5px;
  vertical-align: inherit;
}

.woocommerce form .form-row .input-checkbox + label {
  display: inline-block;
}

.widget-categoriestabs .nav-tabs {
  margin: 40px 0;
  border: none;
  text-align: center;
}

.widget-categoriestabs .nav-tabs > li {
  margin: 0 12px;
  display: inline-block;
  float: none;
}

.widget-categoriestabs .nav-tabs > li.active > a {
  text-decoration: underline;
  color: #000;
}

.widget-categoriestabs .nav-tabs > li > a {
  text-transform: capitalize;
  font-size: 16px;
  color: #000;
  border: none !important;
}

.widget-categoriestabs .nav-tabs > li > a .product-count {
  font-size: 14px;
  color: var(--educrat-text-color);
  display: inline-block;
  vertical-align: top;
}

.woocommerce-widget-layered-nav .view-more-list {
  font-size: 14px;
  text-decoration: underline;
  color: #27b737;
}

.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list {
  overflow: hidden;
}

.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list.hideContent {
  margin-bottom: 10px;
  height: 260px;
}

.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list.showContent {
  height: auto;
  margin-bottom: 10px;
}

.woocommerce-widget-layered-nav-list {
  -webkit-column-count: 2;
  /* Chrome, Safari, Opera */
  -moz-column-count: 2;
  /* Firefox */
  column-count: 2;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item {
  font-size: 15px;
  margin: 0 0 5px;
  width: 100%;
  white-space: nowrap;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item:last-child {
  margin: 0;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a {
  color: var(--educrat-text-color);
  padding: 1px !important;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a:hover, .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a:active {
  color: var(--educrat-theme-color);
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a .swatch-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  vertical-align: baseline;
  margin-left: 10px;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a .swatch-label {
  display: none;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a {
  color: var(--educrat-theme-color);
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a .swatch-color {
  display: none;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a:before {
  vertical-align: baseline;
  color: var(--educrat-theme-color);
  content: "\f14a";
  font-family: 'FontAwesome';
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a:hover:before, .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a:active:before {
  color: #f33066;
  font-family: 'FontAwesome';
  content: "\f057";
}

.apus-price-filter,
.apus-product-sorting {
  list-style: none;
  padding: 0;
  margin: 0;
}

.apus-price-filter li,
.apus-product-sorting li {
  margin-bottom: 5px;
}

.apus-price-filter li:last-child,
.apus-product-sorting li:last-child {
  margin-bottom: 0;
}

.apus-price-filter li a,
.apus-product-sorting li a {
  color: var(--educrat-text-color);
}

.apus-price-filter li a:hover, .apus-price-filter li a:active,
.apus-product-sorting li a:hover,
.apus-product-sorting li a:active {
  color: var(--educrat-theme-color);
}

.apus-price-filter li.current, .apus-price-filter li.active,
.apus-product-sorting li.current,
.apus-product-sorting li.active {
  color: var(--educrat-theme-color);
}

.widget.widget-products-tabs {
  margin-bottom: 0;
}

@media (min-width: 1200px) {
  .widget.widget-products-tabs .widget-title {
    font-size: 44px;
  }
}

.widget.widget-products-tabs .top-info {
  overflow: hidden;
  margin-bottom: 15px;
  -webkit-justify-content: space-between;
  /* Safari 6.1+ */
  justify-content: space-between;
}

@media (min-width: 1200px) {
  .widget.widget-products-tabs .top-info {
    margin-bottom: 35px;
  }
}

.widget.widget-products-tabs .top-info .nav.tabs-product.center {
  margin-bottom: 0;
}

.widget.widget-products-tabs .widget-title {
  padding: 0 0 10px;
  margin: 0;
}

.widget.widget-products-tabs .widget-title:before {
  width: 2000px;
}

.widget.widget-products-tabs .widget-title.center:before, .widget.widget-products-tabs .widget-title.center:after {
  display: none;
}

.widget.widget-products-tabs .widget-content.carousel {
  margin-bottom: -40px;
}

.widget.widget-products-tabs .widget-content.carousel .slick-list {
  padding-bottom: 40px;
}

.widget.widget-products-deal {
  margin: 0;
}

.widget.widget-products-deal .widget-title {
  padding: 0 0 10px;
  margin-bottom: 25px;
}

.widget.widget-products-deal .slick-carousel-top .slick-arrow {
  top: -60px;
}

.widget.widget-products-deal .apus-countdown-dark .times > div > span {
  color: var(--educrat-link-color);
}

.tab-content.loading {
  min-height: 400px;
  position: relative;
}

.tab-content.loading:before {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: 99;
  content: '';
  background: url("../images/loading-quick.gif") center 100px no-repeat rgba(255, 255, 255, 0.9);
}

.widget.widget-tab-style_center .widget-title {
  font-size: 36px;
  text-align: center;
  margin: 0 0 10px;
  color: #252525;
  padding: 0;
  border: none;
}

.widget.widget-tab-style_center .widget-title:before {
  display: none;
}

@keyframes pulsate {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0.1);
    -ms-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
  }
  50% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@-webkit-keyframes pulsate {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0.1);
    -ms-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
  }
  50% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.apus-lookbook .mapper-pin-wrapper > a {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #f43434;
  position: relative;
}

.apus-lookbook .mapper-pin-wrapper > a:before {
  content: '';
  width: 40px;
  height: 40px;
  background: rgba(244, 52, 52, 0.2);
  position: absolute;
  top: 0;
  right: 0;
  margin-top: -12px;
  margin-right: -12px;
  z-index: 2;
  border-radius: 50%;
  animation: 1s ease-out 0s normal none infinite running pulsate;
  -webkit-animation: 1s ease-out 0s normal none infinite running pulsate;
}

.apus-lookbook .mapper-pin-wrapper .image img {
  width: 100%;
}

.apus-lookbook .mapper-popup:before {
  content: '';
  width: 40px;
  height: 40px;
  position: absolute;
  top: 50%;
  right: 100%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.apus-lookbook .mapper-popup:after {
  content: '';
  position: absolute;
  top: 50%;
  right: 100%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 30px;
  height: 24px;
  border-width: 12px 15px;
  border-style: solid;
  border-color: transparent #fff transparent transparent;
}

.widget.widget-recent_viewed .widget-title,
.widget.upsells .widget-title,
.related .widget-title {
  font-size: 18px;
  margin: 0 0 15px;
}

.widget.widget-recent_viewed .slick-list,
.widget.upsells .slick-list,
.related .slick-list {
  padding-top: 4px;
}

.cross-sells.products {
  margin-top: 1.875rem;
  margin-bottom: 0;
}

@media (min-width: 1200px) {
  .cross-sells.products {
    margin-top: 50px;
  }
}

.cross-sells.products > h2 {
  margin: 0 0 20px;
  font-size: 22px;
}

@media (min-width: 992px) {
  .cross-sells.products > h2 {
    margin: 0 0 30px;
  }
}

.wc-block-components-price-slider {
  margin: 0;
}

.wc-block-components-price-slider__range-input::-moz-range-thumb {
  border-radius: 50%;
  width: 18px;
  height: 18px;
  background: #fff;
  border: 2px solid var(--educrat-theme-color);
}

.wc-block-components-price-slider__range-input::-webkit-slider-thumb {
  border-radius: 50%;
  width: 18px;
  height: 18px;
  background: #fff;
  border: 2px solid var(--educrat-theme-color);
}

.wc-block-components-price-slider__range-input::-ms-thumb {
  border-radius: 50%;
  width: 18px;
  height: 18px;
  background: #fff;
  border: 2px solid var(--educrat-theme-color);
}

.wc-block-components-price-slider__range-input-progress {
  --range-color: var(--educrat-theme-color);
  height: 6px;
}

.wc-block-components-price-slider__range-input-wrapper {
  background: #E5F0FD;
  height: 6px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 6px;
}

.wc-block-price-filter__controls input {
  border: 0;
  font-weight: 500;
  color: var(--educrat-link-color);
  padding: 0;
}

.wc-block-price-filter__controls input + input {
  text-align: left;
}

.wc-block-price-filter__range-text span {
  color: var(--educrat-link-color);
  font-weight: 500;
}
