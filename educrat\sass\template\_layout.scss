/*------------------------------------*\
    Header
\*------------------------------------*/
body.page-template-page-dashboard ,
body.fix-header{
    #apus-header{
        position:fixed;
        z-index: 5;
        top:0;
        left: 0;
        width:100%;
    }
}
.together-sidebar-account{
  font-size: 20px;
  cursor: pointer;
  @include transition(all 0.3s ease-in-out 0s);
  &:hover{
    color: $theme-color;
  }
}
.top-wrapper-menu{
    position:relative;
    display: inline-block;
    &:before{
        content:'';
        position:absolute;
        top:100%;
        left: 0;
        @include size(100%,15px);
    }
    .space{
        margin: 0 7px;
        @include opacity(0.2);
    }
    .inner-top-menu{
        margin-top: 15px;
        padding:20px 30px;
        position:absolute;
        top:100%;
        right: 0;
        @include opacity(0);
        visibility:hidden;
        @include transition(all 0.2s ease-in-out 0s);
        @include translateY(10px);
        background:#fff;
        border:1px solid $border-color;
        @include border-radius($border-radius);
        z-index:4;
        min-width: 250px;
        @include box-shadow(0 25px 70px 0 rgba(#01213A, 0.07));
        &:before{
            @include rotate(45deg);
            content:'';
            position:absolute;
            top: -5px;
            right: 15px;
            @include size(10px,10px);
            background-color: #ffffff;
        }
    }
    &:hover{
        .inner-top-menu{
            visibility:visible;
            @include opacity(1);
            @include translateY(0px);
        }
    }
    .header_customer_login{
        margin-top: 10px;
        position:absolute;
        top:100%;
        right: 0;
        @include opacity(0);
        visibility:hidden;
        @include transition(all 0.3s ease-in-out 0s);
        min-width:320px;
        z-index: 9;
    }
    &:hover{
        .header_customer_login{
            visibility:visible;
            @include opacity(1);
            @include translateY(0px);
            background:#fff;
            @include box-shadow(0 5px 10px -5px rgba(0,0,0,0.15));
        }
    }
    .infor-account{
        .avatar-wrapper{
            @include size(50px,50px);
            overflow:hidden;
            background:#fff;
            @include border-radius(16px);
            float: left;
            display: -webkit-flex; /* Safari */
            -webkit-align-items: center; /* Safari 7.0+ */
            display: flex;
            align-items: center;
            img{
                margin:0;
            }
            + .name-acount{
                padding-left: 10px;
                flex: 1;
                -webkit-box-flex: 1;
                -ms-flex: 1;
            }
        }
    }
    .btn-login{
        padding: 5px 30px;
        font-size: $font-size-base;
        font-weight: 400;
        @include border-radius($border-radius);
    }
}
.header_customer_login{
    padding:$theme-margin;
    .title{
        margin:0 0 10px;
        font-size:25px;
    }
}
.header-default{
    background: #fff;
    padding: 25px 0;
}
// header mobile
.header-mobile{
    padding: 15px 0;
    @include transition(all 0.1s ease 0s);
    z-index: 5;
    background:#140342;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    border-bottom: 1px solid rgba(#fff,0.15);
}
.main-sticky-header{
    @include transition(all 0.3s ease-in-out 0s);
    top: 0;
}
.admin-bar{
    .sticky-header{
        top: 30px;
    }
}
.sticky-header{
    position: fixed !important;
    z-index: 5;
    top: 0;
    left: 0;
    width: 100%;
    @include box-shadow(0 5px 30px rgba(0, 22, 84, 0.1));
    background:#fff;
    &.sticky-header-hidden{
        @include translateY(-110%);
    }
}
.header_transparent{
    .apus-header{
        position:absolute;
        top:0;
        left:0;
        width:100%;
        z-index:7;
    }
    &:not(.fix-header) .main-sticky-header:not(.sticky-header) section.elementor-element:not(.no-transparent){
        background:transparent !important;
        .apus-user-register,
        .apus-user-login{
            color: #fff;
        }
    }
    // fix for absolute header position
    &:not(.fix-header) .no_keep_header{
        section.elementor-element:not(.no-transparent){
            background:transparent !important;
        }
    }
}
body.header_fixed{
    .apus-header{
      position:fixed;
      z-index: 2;
      width: 100%;
      background:#fff;
      top:0;
      left: 0;
    }
}
.header-button-woo{
    > div{
        margin-left: 25px;
        &:last-child{
            margin-left: 0;
        }
    }
}
.header-sidebar{
    position:fixed;
    width:100px;
    z-index:91;
    left:0;
    top:0;
    min-height:100vh;
    background:#000;
    color:#fff;
    a{
        color:#fff;
        &:hover,&:active{
            color:$theme-color;
        }
    }
    .show-main-menu{
        @include size(100px,100px);
        line-height:100px;
        border-width:0 0 1px;
        border-color:$border-color;
        color:#fff;
        background:#000000;
    }
    .apus-topcart{
        position:absolute;
        bottom:0;
        left:0;
        width:100%;
        padding:20px 0;
        border-top:1px solid #333333;
        .count{
            color:#fff;
            font-size:12px;
            text-transform:uppercase;
        }
        .dropdown-menu{
            bottom:0;
            top:inherit;
            left:100%;
        }
    }
    .service{
        color:#999999;
        white-space:nowrap;
        position:absolute;
        top:50%;
        z-index:9;
        text-transform:uppercase;
        letter-spacing:2px;
        font-size:14px;
        left:50px;
        @include rotate(-90deg);
         transform-origin: 0 11px;
        > *{
            @include translateX(-50%);
        }
        p{
            margin:0;
        }
    }
}
.over-dark{
    cursor:not-allowed;
    display: block;
    visibility: hidden;
    @include opacity(0);
    position:fixed;
    top:0;
    right: 0;
    z-index: 6;
    @include size(100%,100%);
    background: rgba(#18181A, 0.7);
    @include transition(all 0.2s ease-in-out 0s);
    &.active{
        visibility: visible;
        @include opacity(1);
    }
}
/*------------------------------------*\
    Breadcrumb
\*------------------------------------*/
.#{$app-prefix}-breadcrumb{
    margin: $breadcrumb-margin;
    text-align: $breadcrumb-alignment;
    font-size: $breadcrumb-font-size ;
    .breadcrumb-title{
        margin: 0;
        font-weight: $breadcrumb-title-font-weight;
        font-size: $breadcrumb-title-font-size;
    }
    nav{
        text-align: left;
        line-height: $breadcrumb-line-height;
        a{
            color: $breadcrumb-link-color;
        } 
    }
}
/*------------------------------------*\
    Content
\*------------------------------------*/
.#{$app-prefix}-content{
    background: $content-bg;
}
/*------------------------------------*\
    Pagination
\*------------------------------------*/
.navigation {
    display: block;
    clear: both;
}
.pagination,
.pagination-links,
.#{$app-prefix}-pagination{
    width:100%;
    padding:0.625rem 0;
    margin:0;
    text-align: center;
    line-height: 1;
    @include flexbox();
    justify-content: center;
    -webkit-justify-content: center;
    -ms-justify-content: center;
    @media(min-width: 1200px){
        padding: $theme-margin 0;
    }
    li{
        display: inline-block;
        vertical-align: middle;
        margin:0;
        > span, > a{
            text-align: center;
            font-weight: 500;
            font-size: 0.875rem;
            margin:0 2px;
            display: inline-block;
            float: none;
            color: $body-link;
            @include border-radius(50% !important);
            @include transition(all 0.3s ease-in-out 0s);
            min-width: 35px;
            line-height: 35px;
            @media(min-width: 1200px){
                min-width: 45px;
                line-height: 45px;
            }
            padding:0 5px;
            border: 0;
            background: #fff;
            position: relative;
            overflow: hidden;
            &:focus,
            &:hover,
            &.current{
                color: #fff;
                background: $theme-color;
            }
        }
    }
    > span, > a{
        text-align: center;
        font-weight: 500;
        font-size: 0.875rem;
        margin:0 2px;
        display: inline-block;
        float: none;
        color: $body-link;
        @include border-radius(50% !important);
        @include transition(all 0.3s ease-in-out 0s);
        min-width: 35px;
        line-height: 35px;
        @media(min-width: 1200px){
            min-width: 45px;
            line-height: 45px;
        }
        padding:0 5px;
        border: 0;
        background: #fff;
        position: relative;
        overflow: hidden;
        &:focus,
        &:hover,
        &.current{
            color: #fff;
            background: $theme-color;
        }
    }
    ul.page-numbers{
        margin: 0;
        padding:0;
        list-style: none;
    }
    i{
        font-weight: 400;
    }
}

/*------------------------------------*\
    Footer
\*------------------------------------*/
.apus-footer-mobile{
    display:none;
}
.#{$app-prefix}-footer{
    background: $footer-bg;
    position: relative;
    color: $footer-color;
    font-size: $footer-font-size;
    font-weight: 400;
    a:not([class]){
        color: $footer-link-color;
        &:hover,
        &:focus,
        &:active{
            color: $footer-link-hover-color;
        }
    }
}
.#{$app-prefix}-header{
    background: $header-bg;
    position: relative;
    z-index: 3;
    color: $header-color;
    font-size: $header-font-size;
    font-weight: 400;
}
/*------------------------------------*\
    Copyright
\*------------------------------------*/
.#{$app-prefix}-copyright{
    color: $copyright-color;
    font-size: $copyright-font-size;
    font-weight: $copyright-font-weight;
    background:$copyright-bg;
    padding-top:20px;
    padding-bottom:20px;
    position: relative;
    a{
        &:hover,
        &:focus,
        &:active{
            color: $copyright-link-hover-color;
        }
        color: $copyright-link-color;
    }
}
/*------------------------------------*\
    Top bar
\*------------------------------------*/
.header-offcanvas{
    padding: 18px 5px;
    border-bottom: 1px solid $border-color;
}
.header-offcanvas-bottom{
    padding: 20px;
    border-top: 1px solid $border-color;
    font-size: 15px;
    line-height: 32px;
    .title{
        font-size: 17px;
        font-weight: 500;
        margin: 0 0 5px;
    }
    aside + aside{
        margin-top: 15px;
    }
    .apus_socials{
        margin-left: -15px;
    }
}
.apus-offcanvas {
    .apus-offcanvas-body{
        position: relative;
        height: 100%;
    }
    visibility:hidden;
    @include opacity(0);
    @include translateX(-100%);
    @include transition(all 0.2s  ease-in-out 0s);
    position: fixed;
    top:0;
    left:0;
    z-index:1000;
    width:330px;
    background: #fff;
    height: 100vh;
    overflow-x: auto;
    display: -webkit-flex; /* Safari */
    display: flex;
    flex-direction:column;
    -webkit-flex-direction:column;
    .offcanvas-bottom,
    .offcanvas-top{
        height:20%;
    }
    .offcanvas-middle{
        height:60%;
        padding:20px 0;
        overflow-x:hidden;
    }
    &.active{
        @include translateX(0);
        @include opacity(1);
        visibility:visible;
        @include box-shadow(2px 0 5px 0 rgba(0,0,0,0.15));
    }
    .elementor-column{
        width: 100% !important;
        .elementor-column-wrap, .elementor-widget-wrap{
            padding:0 !important;
        }
        .widget{
            margin-bottom: 10px;
        }
    }
}
@media(max-width:991px){
    .topbar-mobile  {
        padding: 10px;
        .btn{
            margin-right: 10px;
            padding:6px 10px;
        }
        .top-cart .dropdown-menu{
            left: 0;
            right: inherit;
            &:after,
            &:before{
                display: none;
            }
        }
    }
}
// layout for sidebar
.open-text{
    color: $success;
}
.close-text{
    color: $danger;
}
#mobile-offcanvas-sidebar{
    position: fixed;
    z-index: 999;
    top:0px;
    @include size(270px,100%);
    max-width: 80%;
    background:#fff;
    &.mobile-offcanvas-left{
        left:0;
        @include translateX(-100%);
        > .mobile-sidebar-btn{
            left: 100%;
        }
    }
    &.mobile-offcanvas-right{
        right:0;
        @include translateX(100%);
        > .mobile-sidebar-btn{
            right: 100%;
        }
    }
    .mobile-sidebar-wrapper{
        display: none;
        height: 100%;
        width:100%;
        padding:0 15px;
    }
    &.active{
        > .mobile-sidebar-wrapper{
            display: block;
        }
    }
    > .mobile-sidebar-btn{
        position: absolute;
        top: 100px;
    }
}
.mobile-sidebar-panel-overlay{
    position:fixed;
    top: 0;
    left: 0;
    z-index:-5;
    @include size(100%,100%);
    background:rgba(#222222,0.6);
    @include opacity(0);
    visibility: hidden;
    &.active{
        visibility: visible;
        @include opacity(1);
        z-index:5;
        cursor: not-allowed;
    }
}
// footer mobile
.apus-footer-mobile{
    position:fixed;
    z-index: 999;
    background:rgba(255,255,255,0.9);
    padding:10px 20px;
    bottom:0;
    left:0;
    width:100%;
    @include box-shadow(0 0 1px 0 rgba(0,0,0,0.2));
    .footer-search-mobile{
        position:absolute;
        z-index: 999;
        left: 0;
        top:-60px;
        width: 100%;
        @include opacity(0);
        visibility: hidden;
        @include transition(all 0.2s ease-in-out 0s);
        &.active{
            visibility: visible;
            @include opacity(1);
        }
    }
    > ul{
        padding:0;
        margin:0;
        list-style:none;
        text-align: center;
        > li{
            text-transform: uppercase;
            display: inline-block;
            padding:0 25px;
            text-align: center;
            position:relative;
            span{
                display: block;
                font-size: 10px;
                line-height: 1;
            }
            .wishlist-icon, .mini-cart{
                line-height:$line-height-base;
            }
            .wrapper-morelink{
                @include opacity(0);
                visibility: hidden;
                position:absolute;
                right:0;
                bottom:40px;
                padding:20px;
                background:#fff;
                @include box-shadow(0 0 4px 0 rgba(0,0,0,0.1));
                .footer-morelink{
                    list-style:none;
                    padding:0;
                    margin:0;
                    font-size: 10px;
                }
                li{
                    padding:3px 0;
                    white-space: nowrap;
                    display: block;
                    width: 100%;
                    text-align: left;
                }
                &.active{
                    visibility: visible;
                    @include opacity(1);
                }
            }
        }
    }
    .mini-cart i{
        font-size: 15px;
    }
    .mini-cart .count{
        top:2px;
    }
    .apus-search-form{
        min-width: 300px;
        padding: 10px 30px;
        background:rgba(255,255,255,0.9);
        .select-category{
          display: none;
        }
        form{
            border:none;
            margin:0;
            .form-control{
                border:1px solid $border-color;
            }
        }
    }
}
// fix sidebar
.close-sidebar-btn,
.mobile-sidebar-btn{
    cursor:pointer;
    font-weight:600;
    margin-bottom:10px;
    font-size:14px;
    display:inline-block;
}
.mobile-sidebar-btn{
    padding:8px;
    margin:0;
    color: #fff !important;
    background: $theme-color;
    position: fixed;
    top:30%;
    z-index: 6;
    font-size: 18px;
    line-height: 1;
    @include opacity(0.7);
    @include transition(all 0.3s ease-in-out 0s);
    &:hover,&:focus{
        @include opacity(1);
    }
    &.btn-left{
        right: 0;
        @include border-radius(4px 0 0 4px);
    }
    &.btn-right{
        @include border-radius(0 4px 4px 0);
        left: 0;
    }
}
.close-sidebar-btn{
    &:active,
    &:hover{
        color:$danger;
    }
}
.close-sidebar-btn{
    padding-bottom:10px;
    margin-bottom:15px;
    border-bottom:1px solid $border-color;
    width:100%;
    text-align:center;
    color: $danger;
}
@media(max-width:991px){
    .sidebar-wrapper:not(.sidebar-course-single) .sidebar{
        @include transition(all 0.2s ease-in-out 0s);
        z-index:8;
        top:0px;
        @include size(330px,100vh);
        max-width: 90%;
        position:fixed;
        padding:15px;
        background:#fff;
        overflow-y:auto;
        &.sidebar-left{
            left:0;
            @include translateX(-100%);
            &.active{
                @include translateX(0);
            }
        }
        &.sidebar-right{
            right:0;
            @include translateX(100%);
            &.active{
                @include translateX(0);
            }
        }
    }
}
// main-menu-top
.apus-header{
    .wrapper-topmenu{
        &:before{
            content:'';
            position:absolute;
            top:100%;
            left:0;
            @include size(100%,10px);
            background:transparent;
            z-index:9;
        }
        .dropdown-menu-right{
            top:calc(100% + 10px);
        }
    }
}
.apus-topbar{
    .wrapper-topmenu{
        &:hover{
            > a{
                color:#fff;
            }
        }
    }
}
// fix for add cart
.wrapper-top-cart{
    .overlay-dropdown-menu-right{
        position:fixed;
        @include transition(all 0.2s ease-in-out 0s);
        @include size(100%,100%);
        background:rgba(0,0,0,0.6);
        top:0;
        left:0;
        @include opacity(0);
        visibility:hidden;
        z-index:98;
        &.active{
            @include opacity(1);
            visibility:visible;
        }
    }
    > .dropdown-menu-right{
        max-width:70%;
        @include flexbox();
        flex-direction: column;
        -webkit-flex-direction: column;
        position:fixed;
        z-index:999;
        @include transition(all 0.2s ease-in-out 0s);
        top:0;
        right:0;
        background:#fff;
        @include size(420px,100%);
        padding:$theme-margin;
        @include translateX(100%);
        .widget_shopping_cart_heading{
            @include flexbox();
            flex:0 0 auto;
            -webkit-flex:0 0 auto;
            > h3{
                margin:0 0 20px;
                font-size:22px;
                padding:0 0 20px;
                border-bottom:1px solid $border-color;
                width:100%;
                cursor:pointer;
                color:$danger;
            }
        }
        .widget_shopping_cart_content_wrapper{
            @include flexbox();
            flex:1 1 auto;
            -webkit-flex:1 1 auto;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .shopping_cart_content{
            @include flexbox();
            flex-direction: column; 
            -webkit-flex-direction: column;
            height:100%;
            .cart_list{
                flex:1 1 auto;
                -webkit-flex:1 1 auto;
                @include flexbox();
                flex-direction: column; 
                -webkit-flex-direction: column; 
            }
            .cart-bottom{
                flex-direction: column; 
                -webkit-flex-direction: column; 
                flex:0 0 auto;
                -webkit-flex:0 0 auto;
                @include flexbox();
            }
        }
        &.active{
            @include translateX(0);
        }
    }
    .cart_list {
        .variation{
            margin:0;
            > *{
                display:inline-block;
                vertical-align:middle;
                p{
                    margin:0;
                }
            }
        }
    }
    .buttons{
        .btn-block{
            margin-bottom:10px;
        }
    }
}