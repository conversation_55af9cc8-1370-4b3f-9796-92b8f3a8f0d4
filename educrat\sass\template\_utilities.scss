// Animations
@mixin animation($animation) {
  -webkit-animation: $animation;
       -o-animation: $animation;
          animation: $animation;
}
@mixin animation-name($name) {
  -webkit-animation-name: $name;
          animation-name: $name;
}
@mixin animation-duration($duration) {
  -webkit-animation-duration: $duration;
          animation-duration: $duration;
}
@mixin animation-timing-function($timing-function) {
  -webkit-animation-timing-function: $timing-function;
          animation-timing-function: $timing-function;
}
@mixin animation-delay($delay) {
  -webkit-animation-delay: $delay;
          animation-delay: $delay;
}
@mixin animation-iteration-count($iteration-count) {
  -webkit-animation-iteration-count: $iteration-count;
          animation-iteration-count: $iteration-count;
}
@mixin animation-direction($direction) {
  -webkit-animation-direction: $direction;
          animation-direction: $direction;
}
@mixin animation-fill-mode($fill-mode) {
  -webkit-animation-fill-mode: $fill-mode;
          animation-fill-mode: $fill-mode;
}

// Backface visibility
// Prevent browsers from flickering when using CSS 3D transforms.
// Default value is `visible`, but can be changed to `hidden`

@mixin backface-visibility($visibility){
  -webkit-backface-visibility: $visibility;
     -moz-backface-visibility: $visibility;
          backface-visibility: $visibility;
}

// Drop shadows
//
// Note: Deprecated `.box-shadow()` as of v3.1.0 since all of Bootstrap's
// supported browsers that have box shadow capabilities now support it.

@mixin box-shadow($shadow...) {
  -webkit-box-shadow: $shadow; // iOS <4.3 & Android <4.1
          box-shadow: $shadow;
}

// Box sizing
@mixin box-sizing($boxmodel) {
  -webkit-box-sizing: $boxmodel;
     -moz-box-sizing: $boxmodel;
          box-sizing: $boxmodel;
}

// CSS3 Content Columns
@mixin content-columns($column-count, $column-gap: $grid-gutter-width) {
  -webkit-column-count: $column-count;
     -moz-column-count: $column-count;
          column-count: $column-count;
  -webkit-column-gap: $column-gap;
     -moz-column-gap: $column-gap;
          column-gap: $column-gap;
}

// Optional hyphenation
@mixin hyphens($mode: auto) {
  word-wrap: break-word;
  -webkit-hyphens: $mode;
     -moz-hyphens: $mode;
      -ms-hyphens: $mode; // IE10+
       -o-hyphens: $mode;
          hyphens: $mode;
}

// Placeholder text
@mixin placeholder($color: $input-color-placeholder) {
  &::-moz-placeholder           { color: $color;   // Firefox
                                  opacity: 0.65; } // See https://github.com/twbs/bootstrap/pull/11526
  &:-ms-input-placeholder       { color: $color; } // Internet Explorer 10+
  &::-webkit-input-placeholder  { color: $color; } // Safari and Chrome
}

// Transformations
@mixin scale($ratio...) {
  -webkit-transform: scale($ratio);
      -ms-transform: scale($ratio); // IE9 only
       -o-transform: scale($ratio);
          transform: scale($ratio);
}

@mixin scaleX($ratio) {
  -webkit-transform: scaleX($ratio);
      -ms-transform: scaleX($ratio); // IE9 only
       -o-transform: scaleX($ratio);
          transform: scaleX($ratio);
}
@mixin scaleY($ratio) {
  -webkit-transform: scaleY($ratio);
      -ms-transform: scaleY($ratio); // IE9 only
       -o-transform: scaleY($ratio);
          transform: scaleY($ratio);
}
@mixin skew($x, $y) {
  -webkit-transform: skewX($x) skewY($y);
      -ms-transform: skewX($x) skewY($y); // See https://github.com/twbs/bootstrap/issues/4885; IE9+
       -o-transform: skewX($x) skewY($y);
          transform: skewX($x) skewY($y);
}
@mixin translate($x, $y) {
  -webkit-transform: translate($x, $y);
      -ms-transform: translate($x, $y); // IE9 only
       -o-transform: translate($x, $y);
          transform: translate($x, $y);
}
@mixin translate3d($x, $y, $z) {
  -webkit-transform: translate3d($x, $y, $z);
          transform: translate3d($x, $y, $z);
}
@mixin rotate($degrees) {
  -webkit-transform: rotate($degrees);
      -ms-transform: rotate($degrees); // IE9 only
       -o-transform: rotate($degrees);
          transform: rotate($degrees);
}
@mixin rotateX($degrees) {
  -webkit-transform: rotateX($degrees);
      -ms-transform: rotateX($degrees); // IE9 only
       -o-transform: rotateX($degrees);
          transform: rotateX($degrees);
}
@mixin rotateY($degrees) {
  -webkit-transform: rotateY($degrees);
      -ms-transform: rotateY($degrees); // IE9 only
       -o-transform: rotateY($degrees);
          transform: rotateY($degrees);
}
@mixin perspective($perspective) {
  -webkit-perspective: $perspective;
     -moz-perspective: $perspective;
          perspective: $perspective;
}
@mixin perspective-origin($perspective) {
  -webkit-perspective-origin: $perspective;
     -moz-perspective-origin: $perspective;
          perspective-origin: $perspective;
}
@mixin transform-origin($origin) {
  -webkit-transform-origin: $origin;
     -moz-transform-origin: $origin;
      -ms-transform-origin: $origin; // IE9 only
          transform-origin: $origin;
}


// Transitions

@mixin transition($transition...) {
  -webkit-transition: $transition;
       -o-transition: $transition;
          transition: $transition;
}
@mixin transition-listing($transition-listing...) {
  -webkit-transition-listing: $transition-listing;
          transition-listing: $transition-listing;
}
@mixin transition-delay($transition-delay) {
  -webkit-transition-delay: $transition-delay;
          transition-delay: $transition-delay;
}
@mixin transition-duration($transition-duration...) {
  -webkit-transition-duration: $transition-duration;
          transition-duration: $transition-duration;
}
@mixin transition-timing-function($timing-function) {
  -webkit-transition-timing-function: $timing-function;
          transition-timing-function: $timing-function;
}
@mixin transition-transform($transition...) {
  -webkit-transition: -webkit-transform $transition;
     -moz-transition: -moz-transform $transition;
       -o-transition: -o-transform $transition;
          transition: transform $transition;
}


// User select
// For selecting text on the page

@mixin user-select($select) {
  -webkit-user-select: $select;
     -moz-user-select: $select;
      -ms-user-select: $select; // IE10+
          user-select: $select;
}

@mixin opacity($opacity) {
  opacity: $opacity;
  // IE8 filter
  $opacity-ie: ($opacity * 100);
  filter: #{alpha(opacity=$opacity-ie)};
}

//== Translate X - Y - Z
//==========================================
@mixin translateX($x) {
    -webkit-transform: translateX($x);
    -ms-transform: translateX($x); // IE9 only
    -o-transform: translateX($x);
    transform: translateX($x);
}

@mixin translateY($y) {
    -webkit-transform: translateY($y);
    -ms-transform: translateY($y); // IE9 only
    -o-transform: translateY($y);
    transform: translateY($y);
}

@mixin translateZ($z) {
    -webkit-transform: translateZ($z);
    -ms-transform: translateZ($z); // IE9 only
    -o-transform: translateZ($z);
    transform: translateZ($z);
}

//== Transform
//==========================================
@mixin transform($argument){
    -webkit-transform: ($argument);
    -moz-transform: ($argument);
    -ms-transform: ($argument);
    -o-transform: ($argument);
    transform: ($argument);
}
@mixin size($width,$height){
  width: $width;
  height: $height;
}
@mixin flexbox {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
}

@mixin button-inverse( $suffixclass,  $color ,$background ){
  &.btn-#{$suffixclass}{
      &:hover{ 
        color:$color;
        background:$background;
      }
  }
}

@mixin button-outline( $suffixclass, $color, $hovercolor ){
  &.btn-#{$suffixclass}{
      background:transparent;
      border-color:$color;
      color:$color;
      &:hover{
        color:$hovercolor;
        border-color:$color;
        background:$color;
      }
  }
}
@keyframes rotate_icon {
  100% {
    @include rotate(360deg);
  }
}
@-webkit-keyframes rotate_icon {
  100% {
    @include rotate(360deg);
  }
}

@-webkit-keyframes fl-x{
  0% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }

  50% {
    -webkit-transform: translateX(-10px);
            transform: translateX(-10px);
  }

  100% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
}
@keyframes fl-x{
  0% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }

  50% {
    -webkit-transform: translateX(-10px);
            transform: translateX(-10px);
  }

  100% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
}
@-webkit-keyframes fl-y {
  0% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }

  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }

  100% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
@keyframes fl-y{
  0% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }

  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }

  100% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
.animate-fl-x{
  -webkit-animation-name: fl-x;
          animation-name: fl-x;
  -webkit-animation-duration: 2s;
          animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}
.animate-fl-y{
  -webkit-animation-name:fl-y;
          animation-name:fl-y;
  -webkit-animation-duration: 2s;
          animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}