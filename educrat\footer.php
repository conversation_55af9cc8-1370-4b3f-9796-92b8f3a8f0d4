<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the "site-content" div and all content after.
 *
 * @package WordPress
 * @subpackage Educrat
 * @since Educrat 1.0
 */
$footer = apply_filters('educrat_get_footer_layout', 'default');
global $post;
?>
</div><!-- .site-content -->
<?php if (!empty($footer)) : ?>
    <?php educrat_display_footer_builder($footer); ?>
<?php else : ?>
    <footer id="apus-footer" class="apus-footer " role="contentinfo">
        <div class="footer-default">
            <div class="apus-footer-inner">
                <div class="apus-copyright">
                    <div class="container">
                        <div class="copyright-content clearfix">
                            <div class="text-copyright text-center">
                                <?php
                                $allowed_html_array = array(
                                    'a' => array('href' => array()),
                                    'br' => array(),
                                );
								$site_url = site_url();
                                $phone_number = '1234567890'; // Replace with your WhatsApp phone number
								$icon_path = $site_url . '/wp-content/uploads/whatsapp-icon-512x512-oph55c0m.png';
								
                                echo wp_kses(
                                    sprintf(
                                        __('&copy; %s - Educrat. All Rights Reserved. <br/> Powered by <a href="//themeforest.net/user/apustheme">ApusTheme</a>', 'educrat'),
                                        date("Y")
                                    ),
                                    $allowed_html_array
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer><!-- .site-footer -->
<?php endif; ?>
<?php if (educrat_get_config('back_to_top')) : ?>
    <a href="#" id="back-to-top" class="add-fix-top">
        <i class="ti-arrow-up"></i>
    </a>
<?php endif; ?>
</div><!-- .site -->

<!-- WhatsApp Button HTML -->

<?php

if (!is_page(array(33201, 42414))) : ?>
    <div id="whatsapp-button">
        <?php  
        $phone_number = '962782626313'; 
        $site_url = site_url(); 
        $icon_path = $site_url . '/wp-content/uploads/whatsapp-icon-512x512-oph55c0m.png'; 
        ?>
        <a href="https://wa.me/<?= $phone_number ?>" target="_blank">
            <img src="<?php echo esc_url($icon_path); ?>" alt="WhatsApp">
        </a>
    </div>

    <style>
        #whatsapp-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 999;
        }

        #whatsapp-button a img {
            width: 60px; 
            height: auto;
        }
    </style>
<?php endif; ?>



<?php wp_footer(); ?>
</body>

</html>
